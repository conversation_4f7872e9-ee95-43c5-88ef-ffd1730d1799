{"name": "UI Execution 15/06/2025, 19:22:24", "testCases": [{"name": "Browse & PDP\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            52 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2398ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "1909ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1333ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1789ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists", "status": "passed", "duration": "1135ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists", "status": "passed", "duration": "1134ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2573ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2739ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on Text: \"Latest\"", "status": "passed", "duration": "2648ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1637ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2122ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "status": "passed", "duration": "1830ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Show\")]", "status": "passed", "duration": "1816ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]\" exists", "status": "passed", "duration": "1274ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]", "status": "passed", "duration": "2097ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2650ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10003ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till xpath= (//XCUIElementTypeButton[contains(@name,\"bag Add\")])[1]/parent::*", "status": "passed", "duration": "1710ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath:  (//XCUIElementTypeButton[contains(@name,\"bag Add\")])[1]/parent::* with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2177ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[product-share-img]", "status": "passed", "duration": "2399ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists", "status": "passed", "duration": "1682ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2953ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "5194ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with accessibility_id: Learn more about AfterPay", "status": "passed", "duration": "4146ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Afterpay – Now available in store\"]\" exists", "status": "passed", "duration": "1160ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2206ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with accessibility_id: Learn more about Zip", "status": "passed", "duration": "4241ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"What is Zip?\"]\" exists", "status": "passed", "duration": "1191ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2194ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with accessibility_id: Learn more about PayPal Pay in 4", "status": "passed", "duration": "4147ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[paypal-close-img]", "status": "passed", "duration": "2415ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Shop at\"]/following-sibling::XCUIElementTypeButton", "status": "passed", "duration": "2422ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPostCodeSelectionScreenHeader\"]\" exists", "status": "passed", "duration": "1063ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2083ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3205ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "1828ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1776ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2182ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1700ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2160ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2155ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1887ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2072ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1718ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2191ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2190ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "4921ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2563ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "4827ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2003ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "1971ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1062ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}]}, {"name": "Delivery & CNC\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            27 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2197ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4424ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1125ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1735ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3991ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2193ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1686ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add", "status": "passed", "duration": "4020ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1315ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2147ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Click & Collect\"]\" exists", "status": "passed", "duration": "1118ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "1953ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2579ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on Text: \"Brunswick\"", "status": "passed", "duration": "3065ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10006ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2322ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2138ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2007ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2607ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2011ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2273ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2170ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4864ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1964ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "1963ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery  Buy (34 steps)", "status": "passed", "duration": "0ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}]}, {"name": "All Sign ins\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            53 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3245ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4443ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1143ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1704ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2215ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2867ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2232ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2602ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "1465ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Sign in from home page sign in button successfully (with screenshot)", "status": "passed", "duration": "5ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1960ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4845ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1854ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "1958ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]", "status": "passed", "duration": "1790ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1138ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1746ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2201ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2760ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2242ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2598ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "1823ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "1520ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Add Log: Sign in from wishlist page successfully (with screenshot)", "status": "passed", "duration": "5ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2068ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4866ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1842ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3214ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3972ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2176ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1698ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2277ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1918ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "8986ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Sign In\"]", "status": "passed", "duration": "2448ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1148ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email*\"]", "status": "passed", "duration": "2338ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2903ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password*\"]", "status": "passed", "duration": "2417ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2730ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Add Log: Sign in from bag page is successfully (with screenshot)", "status": "passed", "duration": "6ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2499ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4998ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1852ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "1941ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3466ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1144ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "xU3r9LL6Qs", "screenshot_filename": "xU3r9LL6Qs.png", "report_screenshot": "xU3r9LL6Qs.png", "resolved_screenshot": "screenshots/xU3r9LL6Qs.png", "clean_action_id": "xU3r9LL6Qs", "prefixed_action_id": "al_xU3r9LL6Qs", "action_id_screenshot": "screenshots/xU3r9LL6Qs.png"}, {"name": "Add Log: Sign in from universal login page successful (with screenshot)", "status": "passed", "duration": "6ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1987ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4956ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1834ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Hook Action: tap on image: banner-close-updated.png (Recovery)", "status": "passed", "duration": "0ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}]}, {"name": "WishList\n                            \n                            \n                        \n                        \n                            \n                                 <PERSON><PERSON>\n                            \n                            \n                                 Remove\n                            \n                            48 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3223ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4431ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1137ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1729ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2207ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2842ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2191ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2619ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1154ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3866ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2224ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1686ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2181ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1923ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "4934ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2455ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"You may also like\"]/following-sibling::*[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "13907ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1919ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "4932ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2404ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3236ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3952ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2175ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "1944ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2074ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "4924ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2409ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2410ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1215ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1788ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Move\"", "status": "passed", "duration": "2717ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1713ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2630ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "status": "passed", "duration": "1758ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1921ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2425ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2560ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "1540ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "1970ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2309ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2411ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2636ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2673ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2625ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2552ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1770ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4811ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1840ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}]}, {"name": "Kmart-Prod-<PERSON><PERSON>\n                            \n                            \n                                 Remove\n                            \n                            57 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3223ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4339ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1160ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1704ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2202ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2830ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2196ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2622ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1190ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1959ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4871ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1862ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "1589ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3425ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1158ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1729ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2712ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on Text: \"OnePass\"", "status": "passed", "duration": "3098ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email*\"]", "status": "passed", "duration": "2322ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2905ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password*\"]", "status": "passed", "duration": "2438ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2674ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "3370ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1973ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3621ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1853ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "1566ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3486ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1148ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1716ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2736ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on Text: \"Apple\"", "status": "passed", "duration": "3108ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10005ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on Text: \"Passcode\"", "status": "passed", "duration": "2840ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]", "status": "passed", "duration": "1578ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]", "status": "passed", "duration": "1570ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]", "status": "passed", "duration": "1566ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]", "status": "passed", "duration": "1602ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]", "status": "passed", "duration": "1581ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]", "status": "passed", "duration": "1612ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "5009ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2002ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4821ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1850ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "1601ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3436ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1303ms", "action_id": "xU3r9LL6Qs", "screenshot_filename": "xU3r9LL6Qs.png", "report_screenshot": "xU3r9LL6Qs.png", "resolved_screenshot": "screenshots/xU3r9LL6Qs.png", "clean_action_id": "xU3r9LL6Qs", "prefixed_action_id": "al_xU3r9LL6Qs", "action_id_screenshot": "screenshots/xU3r9LL6Qs.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1721ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "4571ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Sign in with Google\"]", "status": "passed", "duration": "2221ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]", "status": "passed", "duration": "2414ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2753ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2046ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4860ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "1857ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1123ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Hook Action: tap on image: banner-close-updated.png (Recovery)", "status": "passed", "duration": "0ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}]}, {"name": "AU- MyAccount\n                            \n                            \n                        \n                        \n                            \n                                 <PERSON>try\n                            \n                            \n                                 Remove\n                            \n                            61 actions", "status": "failed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2198ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5001ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4465ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1152ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2181ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "1405ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "2747ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5007ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2052ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "12783ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]", "status": "passed", "duration": "1938ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Print order details", "status": "passed", "duration": "3505ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]", "status": "passed", "duration": "2233ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2206ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5004ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2018ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]\" exists", "status": "passed", "duration": "1161ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]", "status": "passed", "duration": "1954ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Exchanges Returns\"]\" exists", "status": "passed", "duration": "1171ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2163ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "10247ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5006ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on Text: \"Return\"", "status": "passed", "duration": "2968ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2011ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1931ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "1840ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"details\"", "status": "passed", "duration": "2761ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2321ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "2777ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2222ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on Text: \"payment\"", "status": "passed", "duration": "2812ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2197ms", "action_id": "xU3r9LL6Qs", "screenshot_filename": "xU3r9LL6Qs.png", "report_screenshot": "xU3r9LL6Qs.png", "resolved_screenshot": "screenshots/xU3r9LL6Qs.png", "clean_action_id": "xU3r9LL6Qs", "prefixed_action_id": "al_xU3r9LL6Qs", "action_id_screenshot": "screenshots/xU3r9LL6Qs.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2261ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "2802ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Wait till accessibility_id=btneditFlybuysCard", "status": "failed", "duration": "3222ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btneditFlybuysCard", "status": "unknown", "duration": "4095ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Remove card", "status": "unknown", "duration": "4033ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnRemove", "status": "unknown", "duration": "4041ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "unknown", "duration": "3884ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Tap on element with accessibility_id: btnLinkFlyBuys", "status": "unknown", "duration": "4152ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Flybuys barcode number", "status": "unknown", "duration": "4133ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Input text: \"FlyBuy Number\"", "status": "unknown", "duration": "1639ms", "action_id": "0QtNHB5WEK", "screenshot_filename": "0QtNHB5WEK.png", "report_screenshot": "0QtNHB5WEK.png", "resolved_screenshot": "screenshots/0QtNHB5WEK.png", "clean_action_id": "0QtNHB5WEK", "prefixed_action_id": "al_0QtNHB5WEK", "action_id_screenshot": "screenshots/0QtNHB5WEK.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "unknown", "duration": "4248ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnSaveFlybuysCard", "status": "unknown", "duration": "4119ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[device-back-img]", "status": "unknown", "duration": "2679ms", "action_id": "q6kSH9e0MI", "screenshot_filename": "q6kSH9e0MI.png", "report_screenshot": "q6kSH9e0MI.png", "resolved_screenshot": "screenshots/q6kSH9e0MI.png", "clean_action_id": "q6kSH9e0MI", "prefixed_action_id": "al_q6kSH9e0MI", "action_id_screenshot": "screenshots/q6kSH9e0MI.png"}, {"name": "Check if element with accessibility_id=\"txtMy Flybuys card\" exists", "status": "unknown", "duration": "2007ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "6005ms", "action_id": "kDpsm2D3xt", "screenshot_filename": "kDpsm2D3xt.png", "report_screenshot": "kDpsm2D3xt.png", "resolved_screenshot": "screenshots/kDpsm2D3xt.png", "clean_action_id": "kDpsm2D3xt", "prefixed_action_id": "al_kDpsm2D3xt", "action_id_screenshot": "screenshots/kDpsm2D3xt.png"}, {"name": "Tap on Text: \"locator\"", "status": "unknown", "duration": "3442ms", "action_id": "Cmvm82hiAa", "screenshot_filename": "Cmvm82hiAa.png", "report_screenshot": "Cmvm82hiAa.png", "resolved_screenshot": "screenshots/Cmvm82hiAa.png", "clean_action_id": "Cmvm82hiAa", "prefixed_action_id": "al_Cmvm82hiAa", "action_id_screenshot": "screenshots/Cmvm82hiAa.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "unknown", "duration": "4175ms", "action_id": "OKCHAK6HCJ", "screenshot_filename": "OKCHAK6HCJ.png", "report_screenshot": "OKCHAK6HCJ.png", "resolved_screenshot": "screenshots/OKCHAK6HCJ.png", "clean_action_id": "OKCHAK6HCJ", "prefixed_action_id": "al_OKCHAK6HCJ", "action_id_screenshot": "screenshots/OKCHAK6HCJ.png"}, {"name": "Tap and Type at (env[store-locator-x], env[store-locator-y]): \"env[store-locator-postcode]\"", "status": "unknown", "duration": "5297ms", "action_id": "3hOTINBVMf", "screenshot_filename": "3hOTINBVMf.png", "report_screenshot": "3hOTINBVMf.png", "resolved_screenshot": "screenshots/3hOTINBVMf.png", "clean_action_id": "3hOTINBVMf", "prefixed_action_id": "al_3hOTINBVMf", "action_id_screenshot": "screenshots/3hOTINBVMf.png"}, {"name": "Tap on Text: \"VIC\"", "status": "unknown", "duration": "4199ms", "action_id": "4PZC1vVWJW", "screenshot_filename": "4PZC1vVWJW.png", "report_screenshot": "4PZC1vVWJW.png", "resolved_screenshot": "screenshots/4PZC1vVWJW.png", "clean_action_id": "4PZC1vVWJW", "prefixed_action_id": "al_4PZC1vVWJW", "action_id_screenshot": "screenshots/4PZC1vVWJW.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Melbourne Cbd\"]\" exists", "status": "unknown", "duration": "1622ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "unknown", "duration": "2741ms", "action_id": "NcU6aex76k", "screenshot_filename": "NcU6aex76k.png", "report_screenshot": "NcU6aex76k.png", "resolved_screenshot": "screenshots/NcU6aex76k.png", "clean_action_id": "NcU6aex76k", "prefixed_action_id": "al_NcU6aex76k", "action_id_screenshot": "screenshots/NcU6aex76k.png"}, {"name": "Tap on Text: \"Invite\"", "status": "unknown", "duration": "3922ms", "action_id": "OmKfD9iBjD", "screenshot_filename": "OmKfD9iBjD.png", "report_screenshot": "OmKfD9iBjD.png", "resolved_screenshot": "screenshots/OmKfD9iBjD.png", "clean_action_id": "OmKfD9iBjD", "prefixed_action_id": "al_OmKfD9iBjD", "action_id_screenshot": "screenshots/OmKfD9iBjD.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"I’m loving the new Kmart app\")]\" exists", "status": "unknown", "duration": "1665ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "unknown", "duration": "3359ms", "action_id": "y4i304JeJj", "screenshot_filename": "y4i304JeJj.png", "report_screenshot": "y4i304JeJj.png", "resolved_screenshot": "screenshots/y4i304JeJj.png", "clean_action_id": "y4i304JeJj", "prefixed_action_id": "al_y4i304JeJj", "action_id_screenshot": "screenshots/y4i304JeJj.png"}, {"name": "Tap on Text: \"Customer\"", "status": "unknown", "duration": "3538ms", "action_id": "zsVeGHiIgX", "screenshot_filename": "zsVeGHiIgX.png", "report_screenshot": "zsVeGHiIgX.png", "resolved_screenshot": "screenshots/zsVeGHiIgX.png", "clean_action_id": "zsVeGHiIgX", "prefixed_action_id": "al_zsVeGHiIgX", "action_id_screenshot": "screenshots/zsVeGHiIgX.png"}, {"name": "Tap on image: env[device-back-img]", "status": "unknown", "duration": "2596ms", "action_id": "5e4LeoW1YU", "screenshot_filename": "5e4LeoW1YU.png", "report_screenshot": "5e4LeoW1YU.png", "resolved_screenshot": "screenshots/5e4LeoW1YU.png", "clean_action_id": "5e4LeoW1YU", "prefixed_action_id": "al_5e4LeoW1YU", "action_id_screenshot": "screenshots/5e4LeoW1YU.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "6195ms", "action_id": "inrxgdWzXr", "screenshot_filename": "inrxgdWzXr.png", "report_screenshot": "inrxgdWzXr.png", "resolved_screenshot": "screenshots/inrxgdWzXr.png", "clean_action_id": "inrxgdWzXr", "prefixed_action_id": "al_inrxgdWzXr", "action_id_screenshot": "screenshots/inrxgdWzXr.png"}, {"name": "Tap on Text: \"out\"", "status": "unknown", "duration": "3624ms", "action_id": "x4Mid4HQ0Z", "screenshot_filename": "x4Mid4HQ0Z.png", "report_screenshot": "x4Mid4HQ0Z.png", "resolved_screenshot": "screenshots/x4Mid4HQ0Z.png", "clean_action_id": "x4Mid4HQ0Z", "prefixed_action_id": "al_x4Mid4HQ0Z", "action_id_screenshot": "screenshots/x4Mid4HQ0Z.png"}]}, {"name": "Others\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            50 actions", "status": "failed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3225ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"btnBarcodeScanner\"]", "status": "passed", "duration": "1862ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "429ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"Barcode Scanner\"]\" exists", "status": "passed", "duration": "982ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"imgHelp\"]\" exists", "status": "passed", "duration": "977ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPosition barcode lengthwise within rectangle frame to view helpful product information\"]\" exists", "status": "passed", "duration": "1006ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2588ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1942ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtTrack My Order\"]", "status": "failed", "duration": "2248ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Order number\"]", "status": "unknown", "duration": "2223ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"env[searchorder]\"", "status": "unknown", "duration": "1576ms", "action_id": "searchorde", "screenshot_filename": "searchorde.png", "report_screenshot": "searchorde.png", "resolved_screenshot": "screenshots/searchorde.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email Address\"] with fallback: Coordinates (98, 308)", "status": "unknown", "duration": "2358ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"env[uname-op]\"", "status": "unknown", "duration": "1762ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Search for order\"]", "status": "unknown", "duration": "2355ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"refunded\"]\" exists", "status": "unknown", "duration": "1453ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "unknown", "duration": "2198ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "unknown", "duration": "2218ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "unknown", "duration": "1118ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "unknown", "duration": "2625ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "unknown", "duration": "3141ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "unknown", "duration": "2599ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "unknown", "duration": "2908ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]\" exists", "status": "unknown", "duration": "6633ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "status": "unknown", "duration": "2294ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtOnePassSubscritionBox\"]\" exists", "status": "unknown", "duration": "1352ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "unknown", "duration": "2227ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Tap on Text: \"receipts\"", "status": "unknown", "duration": "2943ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on Text: \"Store\"", "status": "unknown", "duration": "3146ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Check if element with xpath=\"(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]\" exists", "status": "unknown", "duration": "1535ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "status": "unknown", "duration": "2470ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"<PERSON>ly<PERSON> Receipt\"]/XCUIElementTypeOther[2]\" exists", "status": "unknown", "duration": "1951ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "unknown", "duration": "3131ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "unknown", "duration": "3239ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on Text: \"Edit\"", "status": "unknown", "duration": "3790ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on Text: \"current\"", "status": "unknown", "duration": "3116ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeButton[@name=\"btnUpdate\"]\" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"btnUpdate\"]", "status": "unknown", "duration": "0ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "unknown", "duration": "2247ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Tap on Text: \"Find\"", "status": "unknown", "duration": "4007ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "iOS Function: text", "status": "unknown", "duration": "2532ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "unknown", "duration": "1857ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "unknown", "duration": "2360ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[atg-pdp]", "status": "unknown", "duration": "2345ms", "action_id": "xU3r9LL6Qs", "screenshot_filename": "xU3r9LL6Qs.png", "report_screenshot": "xU3r9LL6Qs.png", "resolved_screenshot": "screenshots/xU3r9LL6Qs.png", "clean_action_id": "xU3r9LL6Qs", "prefixed_action_id": "al_xU3r9LL6Qs", "action_id_screenshot": "screenshots/xU3r9LL6Qs.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeButton[@name=\"Save my location\"]\" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name=\"Save my location\"]", "status": "unknown", "duration": "10382ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "unknown", "duration": "3251ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "unknown", "duration": "2510ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery Buy Steps (41 steps)", "status": "unknown", "duration": "0ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "unknown", "duration": "2388ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "3085ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "unknown", "duration": "2286ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "unknown", "duration": "1066ms", "action_id": "0QtNHB5WEK", "screenshot_filename": "0QtNHB5WEK.png", "report_screenshot": "0QtNHB5WEK.png", "resolved_screenshot": "screenshots/0QtNHB5WEK.png", "clean_action_id": "0QtNHB5WEK", "prefixed_action_id": "al_0QtNHB5WEK", "action_id_screenshot": "screenshots/0QtNHB5WEK.png"}]}, {"name": "Postcode Flow\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            56 actions", "status": "failed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3211ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "failed", "duration": "5294ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "560ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "failed", "duration": "2090ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (6 steps)", "status": "failed", "duration": "0ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "unknown", "duration": "1877ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "unknown", "duration": "2392ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "unknown", "duration": "3978ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "unknown", "duration": "3232ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on Text: \"2000\"", "status": "unknown", "duration": "3281ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "unknown", "duration": "3035ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "unknown", "duration": "3200ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "If exists: accessibility_id=\"btnUpdate\" (timeout: 10s) → Then click element: accessibility_id=\"btnUpdate\"", "status": "unknown", "duration": "5053ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Add Log: Post code successfully changed to broadway from home page (with screenshot)", "status": "unknown", "duration": "958ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Tap on Text: \"Find\"", "status": "unknown", "duration": "3999ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "iOS Function: text", "status": "unknown", "duration": "2550ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "unknown", "duration": "2019ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "unknown", "duration": "3938ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Wait till accessibility_id=btnCurrentLocationButton", "status": "unknown", "duration": "3106ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"current\"", "status": "unknown", "duration": "3154ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "unknown", "duration": "3095ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "unknown", "duration": "3155ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Check if image \"SANCTURYLAKE-SE.png\" exists on screen", "status": "unknown", "duration": "3772ms", "action_id": "SANCTURYLA", "screenshot_filename": "SANCTURYLA.png", "report_screenshot": "SANCTURYLA.png", "resolved_screenshot": "screenshots/SANCTURYLA.png"}, {"name": "Add Log: Post code successfully changed in PLP (with screenshot)", "status": "unknown", "duration": "1037ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "unknown", "duration": "2591ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "unknown", "duration": "2306ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "unknown", "duration": "3896ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "unknown", "duration": "3934ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "unknown", "duration": "3232ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on Text: \"2000\"", "status": "unknown", "duration": "3311ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "unknown", "duration": "3081ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "unknown", "duration": "3169ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "Check if image \"shop-broadway-se.png\" exists on screen", "status": "unknown", "duration": "3719ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Add Log: Post code successfully changed in PDP (with screenshot)", "status": "unknown", "duration": "975ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "swipeTillVisible action", "status": "unknown", "duration": "6925ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "unknown", "duration": "5505ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "unknown", "duration": "1816ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "unknown", "duration": "2811ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[@name=\"Delivery options\"]/XCUIElementTypeButton[3]", "status": "unknown", "duration": "1689ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "unknown", "duration": "2386ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "status": "unknown", "duration": "1897ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "unknown", "duration": "3501ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on element with accessibility_id: delete", "status": "unknown", "duration": "4551ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"3000\"", "status": "unknown", "duration": "5221ms", "action_id": "xU3r9LL6Qs", "screenshot_filename": "xU3r9LL6Qs.png", "report_screenshot": "xU3r9LL6Qs.png", "resolved_screenshot": "screenshots/xU3r9LL6Qs.png", "clean_action_id": "xU3r9LL6Qs", "prefixed_action_id": "al_xU3r9LL6Qs", "action_id_screenshot": "screenshots/xU3r9LL6Qs.png"}, {"name": "Tap on Text: \"VIC\"", "status": "unknown", "duration": "26578ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "unknown", "duration": "13544ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "3273ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "unknown", "duration": "2640ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "unknown", "duration": "1761ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "unknown", "duration": "2221ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if image \"deliverto3000-se.png\" exists on screen", "status": "unknown", "duration": "3091ms", "action_id": "deliverto3", "screenshot_filename": "deliverto3.png", "report_screenshot": "deliverto3.png", "resolved_screenshot": "screenshots/deliverto3.png"}, {"name": "Add Log: Post code successfully changed from Bag (with screenshot)", "status": "unknown", "duration": "662ms", "action_id": "successful", "screenshot_filename": "successful.png", "report_screenshot": "successful.png", "resolved_screenshot": "screenshots/successful.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "unknown", "duration": "2830ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "6039ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "unknown", "duration": "2264ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Hook Action: tap on image: banner-close-updated.png (Recovery)", "status": "unknown", "duration": "0ms", "action_id": "0QtNHB5WEK", "screenshot_filename": "0QtNHB5WEK.png", "report_screenshot": "0QtNHB5WEK.png", "resolved_screenshot": "screenshots/0QtNHB5WEK.png", "clean_action_id": "0QtNHB5WEK", "prefixed_action_id": "al_0QtNHB5WEK", "action_id_screenshot": "screenshots/0QtNHB5WEK.png"}]}, {"name": "App Settings AU\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            59 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3198ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "status": "passed", "duration": "1885ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1161ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (6 steps)", "status": "passed", "duration": "0ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Terminate app: com.apple.Preferences", "status": "passed", "duration": "256ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "1218ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Tap on Text: \"Wi-Fi\"", "status": "passed", "duration": "2921ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "977ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3213ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "250ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "739ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "205ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "status": "passed", "duration": "626ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "213ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "620ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "161ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "114ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "742ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5003ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3247ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "1822ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2424ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2655ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Restart app: com.apple.mobilesafari", "status": "passed", "duration": "2197ms", "action_id": "mobilesafa", "screenshot_filename": "mobilesafa.png", "report_screenshot": "mobilesafa.png", "resolved_screenshot": "screenshots/mobilesafa.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "status": "passed", "duration": "1161ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "1781ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1930ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "status": "passed", "duration": "1116ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]\" exists", "status": "passed", "duration": "1138ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "1867ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2607ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\" (timeout: 20s) → Then click element: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "2589ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "2595ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "4748ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "1830ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "3917ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "2086ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2739ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "If exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\" (timeout: 20s) → Then click element: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "2614ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "2684ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "4737ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "1826ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "3839ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "2093ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]", "status": "passed", "duration": "2082ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]", "status": "passed", "duration": "1913ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "1923ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2266ms", "action_id": "xU3r9LL6Qs", "screenshot_filename": "xU3r9LL6Qs.png", "report_screenshot": "xU3r9LL6Qs.png", "resolved_screenshot": "screenshots/xU3r9LL6Qs.png", "clean_action_id": "xU3r9LL6Qs", "prefixed_action_id": "al_xU3r9LL6Qs", "action_id_screenshot": "screenshots/xU3r9LL6Qs.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "status": "passed", "duration": "2107ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "4125ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Home & Living\"]", "status": "passed", "duration": "1965ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "status": "passed", "duration": "1921ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]", "status": "passed", "duration": "1753ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "swipeTillVisible action", "status": "passed", "duration": "4427ms", "action_id": "swipeTillV", "screenshot_filename": "swipeTillV.png", "report_screenshot": "swipeTillV.png", "resolved_screenshot": "screenshots/swipeTillV.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"You may also like\"]/following-sibling::*[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2064ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "4955ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3289ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "1931ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "1898ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}]}, {"name": "AU - Performance\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            49 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3192ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4046ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2206ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2136ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 10%)", "status": "passed", "duration": "15794ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Swipe from (50%, 80%) to (50%, 10%)", "status": "passed", "duration": "13275ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Instruction Manual\"]", "status": "passed", "duration": "2424ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Done\"", "status": "passed", "duration": "4367ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3215ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2077ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Help\"", "status": "passed", "duration": "2776ms", "action_id": "LcYLwUffqj", "screenshot_filename": "LcYLwUffqj.png", "report_screenshot": "LcYLwUffqj.png", "resolved_screenshot": "screenshots/LcYLwUffqj.png", "clean_action_id": "LcYLwUffqj", "prefixed_action_id": "al_LcYLwUffqj", "action_id_screenshot": "screenshots/LcYLwUffqj.png"}, {"name": "Tap on Text: \"FAQ\"", "status": "passed", "duration": "2599ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "26693ms", "action_id": "xLGm9FefWE", "screenshot_filename": "xLGm9FefWE.png", "report_screenshot": "xLGm9FefWE.png", "resolved_screenshot": "screenshots/xLGm9FefWE.png", "clean_action_id": "xLGm9FefWE", "prefixed_action_id": "al_xLGm9FefWE", "action_id_screenshot": "screenshots/xLGm9FefWE.png"}, {"name": "Tap on Text: \"click\"", "status": "passed", "duration": "3238ms", "action_id": "9Jhn4eWZwR", "screenshot_filename": "9Jhn4eWZwR.png", "report_screenshot": "9Jhn4eWZwR.png", "resolved_screenshot": "screenshots/9Jhn4eWZwR.png", "clean_action_id": "9Jhn4eWZwR", "prefixed_action_id": "al_9Jhn4eWZwR", "action_id_screenshot": "screenshots/9Jhn4eWZwR.png"}, {"name": "Tap on Text: \"1800\"", "status": "passed", "duration": "2829ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on Text: \"+61\"", "status": "passed", "duration": "2551ms", "action_id": "cKNu2QoRC1", "screenshot_filename": "cKNu2QoRC1.png", "report_screenshot": "cKNu2QoRC1.png", "resolved_screenshot": "screenshots/cKNu2QoRC1.png", "clean_action_id": "cKNu2QoRC1", "prefixed_action_id": "al_cKNu2QoRC1", "action_id_screenshot": "screenshots/cKNu2QoRC1.png"}, {"name": "Launch app: env[appid]", "status": "passed", "duration": "122ms", "action_id": "qXsL3wzg6J", "screenshot_filename": "qXsL3wzg6J.png", "report_screenshot": "qXsL3wzg6J.png", "resolved_screenshot": "screenshots/qXsL3wzg6J.png", "clean_action_id": "qXsL3wzg6J", "prefixed_action_id": "al_qXsL3wzg6J", "action_id_screenshot": "screenshots/qXsL3wzg6J.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "1838ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3998ms", "action_id": "XEbZHdi0GT", "screenshot_filename": "XEbZHdi0GT.png", "report_screenshot": "XEbZHdi0GT.png", "resolved_screenshot": "screenshots/XEbZHdi0GT.png", "clean_action_id": "XEbZHdi0GT", "prefixed_action_id": "al_XEbZHdi0GT", "action_id_screenshot": "screenshots/XEbZHdi0GT.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2118ms", "action_id": "0pwZCYAtOv", "screenshot_filename": "0pwZCYAtOv.png", "report_screenshot": "0pwZCYAtOv.png", "resolved_screenshot": "screenshots/0pwZCYAtOv.png", "clean_action_id": "0pwZCYAtOv", "prefixed_action_id": "al_0pwZCYAtOv", "action_id_screenshot": "screenshots/0pwZCYAtOv.png"}, {"name": "Execute Test Case: Click_Paginations (10 steps)", "status": "passed", "duration": "0ms", "action_id": "Pagination", "screenshot_filename": "Pagination.png", "report_screenshot": "Pagination.png", "resolved_screenshot": "screenshots/Pagination.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3391ms", "action_id": "mtYqeDttRc", "screenshot_filename": "mtYqeDttRc.png", "report_screenshot": "mtYqeDttRc.png", "resolved_screenshot": "screenshots/mtYqeDttRc.png", "clean_action_id": "mtYqeDttRc", "prefixed_action_id": "al_mtYqeDttRc", "action_id_screenshot": "screenshots/mtYqeDttRc.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "1827ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2609ms", "action_id": "xU3r9LL6Qs", "screenshot_filename": "xU3r9LL6Qs.png", "report_screenshot": "xU3r9LL6Qs.png", "resolved_screenshot": "screenshots/xU3r9LL6Qs.png", "clean_action_id": "xU3r9LL6Qs", "prefixed_action_id": "al_xU3r9LL6Qs", "action_id_screenshot": "screenshots/xU3r9LL6Qs.png"}, {"name": "Tap on Text: \"Age\"", "status": "passed", "duration": "2684ms", "action_id": "aRgHcQcLDP", "screenshot_filename": "aRgHcQcLDP.png", "report_screenshot": "aRgHcQcLDP.png", "resolved_screenshot": "screenshots/aRgHcQcLDP.png", "clean_action_id": "aRgHcQcLDP", "prefixed_action_id": "al_aRgHcQcLDP", "action_id_screenshot": "screenshots/aRgHcQcLDP.png"}, {"name": "Tap on Text: \"Months\"", "status": "passed", "duration": "2595ms", "action_id": "Iab9zCfpqO", "screenshot_filename": "Iab9zCfpqO.png", "report_screenshot": "Iab9zCfpqO.png", "resolved_screenshot": "screenshots/Iab9zCfpqO.png", "clean_action_id": "Iab9zCfpqO", "prefixed_action_id": "al_Iab9zCfpqO", "action_id_screenshot": "screenshots/Iab9zCfpqO.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3628ms", "action_id": "iSckENpXrN", "screenshot_filename": "iSckENpXrN.png", "report_screenshot": "iSckENpXrN.png", "resolved_screenshot": "screenshots/iSckENpXrN.png", "clean_action_id": "iSckENpXrN", "prefixed_action_id": "al_iSckENpXrN", "action_id_screenshot": "screenshots/iSckENpXrN.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3398ms", "action_id": "0QtNHB5WEK", "screenshot_filename": "0QtNHB5WEK.png", "report_screenshot": "0QtNHB5WEK.png", "resolved_screenshot": "screenshots/0QtNHB5WEK.png", "clean_action_id": "0QtNHB5WEK", "prefixed_action_id": "al_0QtNHB5WEK", "action_id_screenshot": "screenshots/0QtNHB5WEK.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "1892ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3443ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1263ms", "action_id": "q6kSH9e0MI", "screenshot_filename": "q6kSH9e0MI.png", "report_screenshot": "q6kSH9e0MI.png", "resolved_screenshot": "screenshots/q6kSH9e0MI.png", "clean_action_id": "q6kSH9e0MI", "prefixed_action_id": "al_q6kSH9e0MI", "action_id_screenshot": "screenshots/q6kSH9e0MI.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2220ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2847ms", "action_id": "kDpsm2D3xt", "screenshot_filename": "kDpsm2D3xt.png", "report_screenshot": "kDpsm2D3xt.png", "resolved_screenshot": "screenshots/kDpsm2D3xt.png", "clean_action_id": "kDpsm2D3xt", "prefixed_action_id": "al_kDpsm2D3xt", "action_id_screenshot": "screenshots/kDpsm2D3xt.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2243ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2622ms", "action_id": "Cmvm82hiAa", "screenshot_filename": "Cmvm82hiAa.png", "report_screenshot": "Cmvm82hiAa.png", "resolved_screenshot": "screenshots/Cmvm82hiAa.png", "clean_action_id": "Cmvm82hiAa", "prefixed_action_id": "al_Cmvm82hiAa", "action_id_screenshot": "screenshots/Cmvm82hiAa.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2295ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3873ms", "action_id": "OKCHAK6HCJ", "screenshot_filename": "OKCHAK6HCJ.png", "report_screenshot": "OKCHAK6HCJ.png", "resolved_screenshot": "screenshots/OKCHAK6HCJ.png", "clean_action_id": "OKCHAK6HCJ", "prefixed_action_id": "al_OKCHAK6HCJ", "action_id_screenshot": "screenshots/OKCHAK6HCJ.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2290ms", "action_id": "3hOTINBVMf", "screenshot_filename": "3hOTINBVMf.png", "report_screenshot": "3hOTINBVMf.png", "resolved_screenshot": "screenshots/3hOTINBVMf.png", "clean_action_id": "3hOTINBVMf", "prefixed_action_id": "al_3hOTINBVMf", "action_id_screenshot": "screenshots/3hOTINBVMf.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1610ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2138ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1993ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2510ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2511ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "1803ms", "action_id": "4PZC1vVWJW", "screenshot_filename": "4PZC1vVWJW.png", "report_screenshot": "4PZC1vVWJW.png", "resolved_screenshot": "screenshots/4PZC1vVWJW.png", "clean_action_id": "4PZC1vVWJW", "prefixed_action_id": "al_4PZC1vVWJW", "action_id_screenshot": "screenshots/4PZC1vVWJW.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "1811ms", "action_id": "NcU6aex76k", "screenshot_filename": "NcU6aex76k.png", "report_screenshot": "NcU6aex76k.png", "resolved_screenshot": "screenshots/NcU6aex76k.png", "clean_action_id": "NcU6aex76k", "prefixed_action_id": "al_NcU6aex76k", "action_id_screenshot": "screenshots/NcU6aex76k.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1989ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1847ms", "action_id": "OmKfD9iBjD", "screenshot_filename": "OmKfD9iBjD.png", "report_screenshot": "OmKfD9iBjD.png", "resolved_screenshot": "screenshots/OmKfD9iBjD.png", "clean_action_id": "OmKfD9iBjD", "prefixed_action_id": "al_OmKfD9iBjD", "action_id_screenshot": "screenshots/OmKfD9iBjD.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2651ms", "action_id": "y4i304JeJj", "screenshot_filename": "y4i304JeJj.png", "report_screenshot": "y4i304JeJj.png", "resolved_screenshot": "screenshots/y4i304JeJj.png", "clean_action_id": "y4i304JeJj", "prefixed_action_id": "al_y4i304JeJj", "action_id_screenshot": "screenshots/y4i304JeJj.png"}, {"name": "Wait till accessibility_id=txtHomeAccountCtaSignIn", "status": "passed", "duration": "2625ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}]}], "passed": 7, "failed": 3, "skipped": 0, "status": "failed"}