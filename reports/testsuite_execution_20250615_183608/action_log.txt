Action Log - 2025-06-15 19:22:25
================================================================================

[[19:22:24]] [INFO] Generating execution report...
[[19:22:24]] [WARNING] 3 tests failed.
[[19:22:24]] [SUCCESS] Screenshot refreshed
[[19:22:24]] [INFO] Refreshing screenshot...
[[19:22:20]] [SUCCESS] Screenshot refreshed successfully
[[19:22:20]] [SUCCESS] Screenshot refreshed successfully
[[19:22:19]] [INFO] Executing action 512/512: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[19:22:19]] [SUCCESS] Screenshot refreshed
[[19:22:19]] [INFO] Refreshing screenshot...
[[19:22:14]] [SUCCESS] Screenshot refreshed successfully
[[19:22:14]] [SUCCESS] Screenshot refreshed successfully
[[19:22:14]] [INFO] Executing action 511/512: Tap on Text: "out"
[[19:22:13]] [SUCCESS] Screenshot refreshed
[[19:22:13]] [INFO] Refreshing screenshot...
[[19:22:10]] [SUCCESS] Screenshot refreshed successfully
[[19:22:10]] [SUCCESS] Screenshot refreshed successfully
[[19:22:10]] [INFO] Executing action 510/512: Swipe from (50%, 70%) to (50%, 30%)
[[19:22:09]] [SUCCESS] Screenshot refreshed
[[19:22:09]] [INFO] Refreshing screenshot...
[[19:22:06]] [SUCCESS] Screenshot refreshed successfully
[[19:22:06]] [SUCCESS] Screenshot refreshed successfully
[[19:22:06]] [INFO] Executing action 509/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:22:05]] [SUCCESS] Screenshot refreshed
[[19:22:05]] [INFO] Refreshing screenshot...
[[19:22:02]] [SUCCESS] Screenshot refreshed successfully
[[19:22:02]] [SUCCESS] Screenshot refreshed successfully
[[19:22:02]] [INFO] Executing action 508/512: Swipe from (90%, 20%) to (30%, 20%)
[[19:22:01]] [SUCCESS] Screenshot refreshed
[[19:22:01]] [INFO] Refreshing screenshot...
[[19:21:58]] [SUCCESS] Screenshot refreshed successfully
[[19:21:58]] [SUCCESS] Screenshot refreshed successfully
[[19:21:58]] [INFO] Executing action 507/512: Swipe from (90%, 20%) to (30%, 20%)
[[19:21:57]] [SUCCESS] Screenshot refreshed
[[19:21:57]] [INFO] Refreshing screenshot...
[[19:21:53]] [SUCCESS] Screenshot refreshed successfully
[[19:21:53]] [SUCCESS] Screenshot refreshed successfully
[[19:21:53]] [INFO] Executing action 506/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[19:21:52]] [SUCCESS] Screenshot refreshed
[[19:21:52]] [INFO] Refreshing screenshot...
[[19:21:48]] [SUCCESS] Screenshot refreshed successfully
[[19:21:48]] [SUCCESS] Screenshot refreshed successfully
[[19:21:48]] [INFO] Executing action 505/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[19:21:48]] [SUCCESS] Screenshot refreshed
[[19:21:48]] [INFO] Refreshing screenshot...
[[19:21:44]] [SUCCESS] Screenshot refreshed successfully
[[19:21:44]] [SUCCESS] Screenshot refreshed successfully
[[19:21:44]] [INFO] Executing action 504/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[19:21:43]] [SUCCESS] Screenshot refreshed
[[19:21:43]] [INFO] Refreshing screenshot...
[[19:21:40]] [SUCCESS] Screenshot refreshed successfully
[[19:21:40]] [SUCCESS] Screenshot refreshed successfully
[[19:21:39]] [INFO] Executing action 503/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[19:21:39]] [SUCCESS] Screenshot refreshed
[[19:21:39]] [INFO] Refreshing screenshot...
[[19:21:36]] [SUCCESS] Screenshot refreshed successfully
[[19:21:36]] [SUCCESS] Screenshot refreshed successfully
[[19:21:35]] [INFO] Executing action 502/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[19:21:35]] [SUCCESS] Screenshot refreshed
[[19:21:35]] [INFO] Refreshing screenshot...
[[19:21:31]] [SUCCESS] Screenshot refreshed successfully
[[19:21:31]] [SUCCESS] Screenshot refreshed successfully
[[19:21:31]] [INFO] Executing action 501/512: iOS Function: text
[[19:21:30]] [SUCCESS] Screenshot refreshed
[[19:21:30]] [INFO] Refreshing screenshot...
[[19:21:25]] [SUCCESS] Screenshot refreshed successfully
[[19:21:25]] [SUCCESS] Screenshot refreshed successfully
[[19:21:25]] [INFO] Executing action 500/512: Tap on Text: "Find"
[[19:21:24]] [SUCCESS] Screenshot refreshed
[[19:21:24]] [INFO] Refreshing screenshot...
[[19:21:20]] [SUCCESS] Screenshot refreshed successfully
[[19:21:20]] [SUCCESS] Screenshot refreshed successfully
[[19:21:20]] [INFO] Executing action 499/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[19:21:19]] [SUCCESS] Screenshot refreshed
[[19:21:19]] [INFO] Refreshing screenshot...
[[19:21:15]] [SUCCESS] Screenshot refreshed successfully
[[19:21:15]] [SUCCESS] Screenshot refreshed successfully
[[19:21:14]] [INFO] Executing action 498/512: iOS Function: text
[[19:21:14]] [SUCCESS] Screenshot refreshed
[[19:21:14]] [INFO] Refreshing screenshot...
[[19:21:10]] [SUCCESS] Screenshot refreshed successfully
[[19:21:10]] [SUCCESS] Screenshot refreshed successfully
[[19:21:10]] [INFO] Executing action 497/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[19:21:10]] [SUCCESS] Screenshot refreshed
[[19:21:10]] [INFO] Refreshing screenshot...
[[19:21:05]] [SUCCESS] Screenshot refreshed successfully
[[19:21:05]] [SUCCESS] Screenshot refreshed successfully
[[19:21:05]] [INFO] Executing action 496/512: iOS Function: text
[[19:21:05]] [SUCCESS] Screenshot refreshed
[[19:21:05]] [INFO] Refreshing screenshot...
[[19:21:01]] [SUCCESS] Screenshot refreshed successfully
[[19:21:01]] [SUCCESS] Screenshot refreshed successfully
[[19:21:01]] [INFO] Executing action 495/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[19:21:00]] [SUCCESS] Screenshot refreshed
[[19:21:00]] [INFO] Refreshing screenshot...
[[19:20:58]] [SUCCESS] Screenshot refreshed successfully
[[19:20:58]] [SUCCESS] Screenshot refreshed successfully
[[19:20:57]] [INFO] Executing action 494/512: iOS Function: alert_accept
[[19:20:57]] [SUCCESS] Screenshot refreshed
[[19:20:57]] [INFO] Refreshing screenshot...
[[19:20:52]] [SUCCESS] Screenshot refreshed successfully
[[19:20:52]] [SUCCESS] Screenshot refreshed successfully
[[19:20:51]] [INFO] Executing action 493/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[19:20:51]] [SUCCESS] Screenshot refreshed
[[19:20:51]] [INFO] Refreshing screenshot...
[[19:20:47]] [SUCCESS] Screenshot refreshed successfully
[[19:20:47]] [SUCCESS] Screenshot refreshed successfully
[[19:20:47]] [INFO] Executing action 492/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[19:20:47]] [SUCCESS] Screenshot refreshed
[[19:20:47]] [INFO] Refreshing screenshot...
[[19:20:42]] [INFO] Executing action 491/512: Swipe from (5%, 50%) to (90%, 50%)
[[19:20:41]] [SUCCESS] Screenshot refreshed successfully
[[19:20:41]] [SUCCESS] Screenshot refreshed successfully
[[19:20:41]] [SUCCESS] Screenshot refreshed
[[19:20:41]] [INFO] Refreshing screenshot...
[[19:20:36]] [SUCCESS] Screenshot refreshed successfully
[[19:20:36]] [SUCCESS] Screenshot refreshed successfully
[[19:20:36]] [INFO] Executing action 490/512: Swipe from (5%, 50%) to (90%, 50%)
[[19:20:35]] [SUCCESS] Screenshot refreshed
[[19:20:35]] [INFO] Refreshing screenshot...
[[19:20:31]] [INFO] Executing action 489/512: Tap on Text: "Months"
[[19:20:31]] [SUCCESS] Screenshot refreshed successfully
[[19:20:31]] [SUCCESS] Screenshot refreshed successfully
[[19:20:31]] [SUCCESS] Screenshot refreshed
[[19:20:31]] [INFO] Refreshing screenshot...
[[19:20:26]] [SUCCESS] Screenshot refreshed successfully
[[19:20:26]] [SUCCESS] Screenshot refreshed successfully
[[19:20:26]] [INFO] Executing action 488/512: Tap on Text: "Age"
[[19:20:26]] [SUCCESS] Screenshot refreshed
[[19:20:26]] [INFO] Refreshing screenshot...
[[19:20:22]] [SUCCESS] Screenshot refreshed successfully
[[19:20:22]] [SUCCESS] Screenshot refreshed successfully
[[19:20:22]] [INFO] Executing action 487/512: Tap on Text: "Toys"
[[19:20:21]] [SUCCESS] Screenshot refreshed
[[19:20:21]] [INFO] Refreshing screenshot...
[[19:20:18]] [SUCCESS] Screenshot refreshed successfully
[[19:20:18]] [SUCCESS] Screenshot refreshed successfully
[[19:20:18]] [INFO] Executing action 486/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[19:20:17]] [SUCCESS] Screenshot refreshed
[[19:20:17]] [INFO] Refreshing screenshot...
[[19:20:14]] [SUCCESS] Screenshot refreshed successfully
[[19:20:14]] [SUCCESS] Screenshot refreshed successfully
[[19:20:12]] [INFO] Executing action 485/512: Restart app: env[appid]
[[19:20:12]] [SUCCESS] Screenshot refreshed
[[19:20:12]] [INFO] Refreshing screenshot...
[[19:20:11]] [SUCCESS] Screenshot refreshed
[[19:20:11]] [INFO] Refreshing screenshot...
[[19:19:56]] [SUCCESS] Screenshot refreshed successfully
[[19:19:56]] [SUCCESS] Screenshot refreshed successfully
[[19:19:55]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[19:19:55]] [SUCCESS] Screenshot refreshed
[[19:19:55]] [INFO] Refreshing screenshot...
[[19:19:12]] [SUCCESS] Screenshot refreshed successfully
[[19:19:12]] [SUCCESS] Screenshot refreshed successfully
[[19:19:12]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:19:11]] [SUCCESS] Screenshot refreshed
[[19:19:11]] [INFO] Refreshing screenshot...
[[19:18:55]] [SUCCESS] Screenshot refreshed successfully
[[19:18:55]] [SUCCESS] Screenshot refreshed successfully
[[19:18:55]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[19:18:55]] [SUCCESS] Screenshot refreshed
[[19:18:55]] [INFO] Refreshing screenshot...
[[19:18:13]] [SUCCESS] Screenshot refreshed successfully
[[19:18:13]] [SUCCESS] Screenshot refreshed successfully
[[19:18:12]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:18:12]] [SUCCESS] Screenshot refreshed
[[19:18:12]] [INFO] Refreshing screenshot...
[[19:17:56]] [SUCCESS] Screenshot refreshed successfully
[[19:17:56]] [SUCCESS] Screenshot refreshed successfully
[[19:17:55]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[19:17:55]] [SUCCESS] Screenshot refreshed
[[19:17:55]] [INFO] Refreshing screenshot...
[[19:17:13]] [SUCCESS] Screenshot refreshed successfully
[[19:17:13]] [SUCCESS] Screenshot refreshed successfully
[[19:17:12]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:17:12]] [SUCCESS] Screenshot refreshed
[[19:17:12]] [INFO] Refreshing screenshot...
[[19:16:56]] [SUCCESS] Screenshot refreshed successfully
[[19:16:56]] [SUCCESS] Screenshot refreshed successfully
[[19:16:56]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[19:16:55]] [SUCCESS] Screenshot refreshed
[[19:16:55]] [INFO] Refreshing screenshot...
[[19:16:13]] [SUCCESS] Screenshot refreshed successfully
[[19:16:13]] [SUCCESS] Screenshot refreshed successfully
[[19:16:13]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:16:12]] [SUCCESS] Screenshot refreshed
[[19:16:12]] [INFO] Refreshing screenshot...
[[19:15:56]] [SUCCESS] Screenshot refreshed successfully
[[19:15:56]] [SUCCESS] Screenshot refreshed successfully
[[19:15:56]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[19:15:55]] [SUCCESS] Screenshot refreshed
[[19:15:55]] [INFO] Refreshing screenshot...
[[19:15:11]] [SUCCESS] Screenshot refreshed successfully
[[19:15:11]] [SUCCESS] Screenshot refreshed successfully
[[19:15:11]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:15:11]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[19:15:11]] [INFO] Loading steps for Multi Step action: Click_Paginations
[[19:15:11]] [INFO] Executing action 484/512: Execute Test Case: Click_Paginations (10 steps)
[[19:15:10]] [SUCCESS] Screenshot refreshed
[[19:15:10]] [INFO] Refreshing screenshot...
[[19:15:06]] [SUCCESS] Screenshot refreshed successfully
[[19:15:06]] [SUCCESS] Screenshot refreshed successfully
[[19:15:06]] [INFO] Executing action 483/512: iOS Function: text
[[19:15:05]] [SUCCESS] Screenshot refreshed
[[19:15:05]] [INFO] Refreshing screenshot...
[[19:15:00]] [SUCCESS] Screenshot refreshed successfully
[[19:15:00]] [SUCCESS] Screenshot refreshed successfully
[[19:15:00]] [INFO] Executing action 482/512: Tap on Text: "Find"
[[19:14:59]] [SUCCESS] Screenshot refreshed
[[19:14:59]] [INFO] Refreshing screenshot...
[[19:14:55]] [SUCCESS] Screenshot refreshed successfully
[[19:14:55]] [SUCCESS] Screenshot refreshed successfully
[[19:14:55]] [INFO] Executing action 481/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[19:14:55]] [SUCCESS] Screenshot refreshed
[[19:14:55]] [INFO] Refreshing screenshot...
[[19:14:53]] [INFO] Executing action 480/512: Launch app: env[appid]
[[19:14:53]] [SUCCESS] Screenshot refreshed successfully
[[19:14:53]] [SUCCESS] Screenshot refreshed successfully
[[19:14:53]] [SUCCESS] Screenshot refreshed
[[19:14:53]] [INFO] Refreshing screenshot...
[[19:14:49]] [SUCCESS] Screenshot refreshed successfully
[[19:14:49]] [SUCCESS] Screenshot refreshed successfully
[[19:14:49]] [INFO] Executing action 479/512: Tap on Text: "+61"
[[19:14:48]] [SUCCESS] Screenshot refreshed
[[19:14:48]] [INFO] Refreshing screenshot...
[[19:14:44]] [SUCCESS] Screenshot refreshed successfully
[[19:14:44]] [SUCCESS] Screenshot refreshed successfully
[[19:14:44]] [INFO] Executing action 478/512: Tap on Text: "1800"
[[19:14:43]] [SUCCESS] Screenshot refreshed
[[19:14:43]] [INFO] Refreshing screenshot...
[[19:14:39]] [SUCCESS] Screenshot refreshed successfully
[[19:14:39]] [SUCCESS] Screenshot refreshed successfully
[[19:14:38]] [INFO] Executing action 477/512: Tap on Text: "click"
[[19:14:38]] [SUCCESS] Screenshot refreshed
[[19:14:38]] [INFO] Refreshing screenshot...
[[19:14:09]] [SUCCESS] Screenshot refreshed successfully
[[19:14:09]] [SUCCESS] Screenshot refreshed successfully
[[19:14:09]] [INFO] Executing action 476/512: Swipe from (50%, 70%) to (50%, 30%)
[[19:14:09]] [SUCCESS] Screenshot refreshed
[[19:14:09]] [INFO] Refreshing screenshot...
[[19:14:05]] [SUCCESS] Screenshot refreshed successfully
[[19:14:05]] [SUCCESS] Screenshot refreshed successfully
[[19:14:05]] [INFO] Executing action 475/512: Tap on Text: "FAQ"
[[19:14:04]] [SUCCESS] Screenshot refreshed
[[19:14:04]] [INFO] Refreshing screenshot...
[[19:14:00]] [SUCCESS] Screenshot refreshed successfully
[[19:14:00]] [SUCCESS] Screenshot refreshed successfully
[[19:14:00]] [INFO] Executing action 474/512: Tap on Text: "Help"
[[19:13:59]] [SUCCESS] Screenshot refreshed
[[19:13:59]] [INFO] Refreshing screenshot...
[[19:13:56]] [SUCCESS] Screenshot refreshed successfully
[[19:13:56]] [SUCCESS] Screenshot refreshed successfully
[[19:13:56]] [INFO] Executing action 473/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:13:55]] [SUCCESS] Screenshot refreshed
[[19:13:55]] [INFO] Refreshing screenshot...
[[19:13:50]] [SUCCESS] Screenshot refreshed successfully
[[19:13:50]] [SUCCESS] Screenshot refreshed successfully
[[19:13:50]] [INFO] Executing action 472/512: Restart app: env[appid]
[[19:13:49]] [SUCCESS] Screenshot refreshed
[[19:13:49]] [INFO] Refreshing screenshot...
[[19:13:43]] [SUCCESS] Screenshot refreshed successfully
[[19:13:43]] [SUCCESS] Screenshot refreshed successfully
[[19:13:43]] [INFO] Executing action 471/512: Tap on Text: "Done"
[[19:13:43]] [SUCCESS] Screenshot refreshed
[[19:13:43]] [INFO] Refreshing screenshot...
[[19:13:39]] [SUCCESS] Screenshot refreshed successfully
[[19:13:39]] [SUCCESS] Screenshot refreshed successfully
[[19:13:39]] [INFO] Executing action 470/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"]
[[19:13:38]] [SUCCESS] Screenshot refreshed
[[19:13:38]] [INFO] Refreshing screenshot...
[[19:13:23]] [SUCCESS] Screenshot refreshed successfully
[[19:13:23]] [SUCCESS] Screenshot refreshed successfully
[[19:13:23]] [INFO] Executing action 469/512: Swipe from (50%, 80%) to (50%, 10%)
[[19:13:23]] [SUCCESS] Screenshot refreshed
[[19:13:23]] [INFO] Refreshing screenshot...
[[19:13:05]] [SUCCESS] Screenshot refreshed successfully
[[19:13:05]] [SUCCESS] Screenshot refreshed successfully
[[19:13:05]] [INFO] Executing action 468/512: Swipe from (50%, 70%) to (50%, 10%)
[[19:13:05]] [SUCCESS] Screenshot refreshed
[[19:13:05]] [INFO] Refreshing screenshot...
[[19:13:01]] [SUCCESS] Screenshot refreshed successfully
[[19:13:01]] [SUCCESS] Screenshot refreshed successfully
[[19:13:01]] [INFO] Executing action 467/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[19:13:00]] [SUCCESS] Screenshot refreshed
[[19:13:00]] [INFO] Refreshing screenshot...
[[19:12:57]] [SUCCESS] Screenshot refreshed successfully
[[19:12:57]] [SUCCESS] Screenshot refreshed successfully
[[19:12:57]] [INFO] Executing action 466/512: iOS Function: text
[[19:12:56]] [SUCCESS] Screenshot refreshed
[[19:12:56]] [INFO] Refreshing screenshot...
[[19:12:51]] [SUCCESS] Screenshot refreshed successfully
[[19:12:51]] [SUCCESS] Screenshot refreshed successfully
[[19:12:50]] [INFO] Executing action 465/512: Tap on Text: "Find"
[[19:12:50]] [SUCCESS] Screenshot refreshed
[[19:12:50]] [INFO] Refreshing screenshot...
[[19:12:35]] [SUCCESS] Screenshot refreshed successfully
[[19:12:35]] [SUCCESS] Screenshot refreshed successfully
[[19:12:34]] [INFO] Executing action 464/512: Restart app: env[appid]
[[19:12:34]] [SUCCESS] Screenshot refreshed
[[19:12:34]] [INFO] Refreshing screenshot...
[[19:12:30]] [INFO] Executing action 463/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[19:12:30]] [SUCCESS] Screenshot refreshed successfully
[[19:12:30]] [SUCCESS] Screenshot refreshed successfully
[[19:12:30]] [SUCCESS] Screenshot refreshed
[[19:12:30]] [INFO] Refreshing screenshot...
[[19:12:27]] [SUCCESS] Screenshot refreshed successfully
[[19:12:27]] [SUCCESS] Screenshot refreshed successfully
[[19:12:26]] [INFO] Executing action 462/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[19:12:26]] [SUCCESS] Screenshot refreshed
[[19:12:26]] [INFO] Refreshing screenshot...
[[19:12:21]] [SUCCESS] Screenshot refreshed successfully
[[19:12:21]] [SUCCESS] Screenshot refreshed successfully
[[19:12:21]] [INFO] Executing action 461/512: Restart app: env[appid]
[[19:12:20]] [SUCCESS] Screenshot refreshed
[[19:12:20]] [INFO] Refreshing screenshot...
[[19:12:14]] [SUCCESS] Screenshot refreshed successfully
[[19:12:14]] [SUCCESS] Screenshot refreshed successfully
[[19:12:13]] [INFO] Executing action 460/512: Tap on element with accessibility_id: Add to bag
[[19:12:13]] [SUCCESS] Screenshot refreshed
[[19:12:13]] [INFO] Refreshing screenshot...
[[19:12:09]] [SUCCESS] Screenshot refreshed successfully
[[19:12:09]] [SUCCESS] Screenshot refreshed successfully
[[19:12:09]] [INFO] Executing action 459/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[19:12:08]] [SUCCESS] Screenshot refreshed
[[19:12:08]] [INFO] Refreshing screenshot...
[[19:12:03]] [SUCCESS] Screenshot refreshed successfully
[[19:12:03]] [SUCCESS] Screenshot refreshed successfully
[[19:12:02]] [INFO] Executing action 458/512: swipeTillVisible action
[[19:12:02]] [SUCCESS] Screenshot refreshed
[[19:12:02]] [INFO] Refreshing screenshot...
[[19:11:59]] [SUCCESS] Screenshot refreshed successfully
[[19:11:59]] [SUCCESS] Screenshot refreshed successfully
[[19:11:57]] [INFO] Executing action 457/512: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[19:11:57]] [SUCCESS] Screenshot refreshed
[[19:11:57]] [INFO] Refreshing screenshot...
[[19:11:53]] [SUCCESS] Screenshot refreshed successfully
[[19:11:53]] [SUCCESS] Screenshot refreshed successfully
[[19:11:52]] [INFO] Executing action 456/512: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[19:11:51]] [SUCCESS] Screenshot refreshed
[[19:11:51]] [INFO] Refreshing screenshot...
[[19:11:48]] [SUCCESS] Screenshot refreshed successfully
[[19:11:48]] [SUCCESS] Screenshot refreshed successfully
[[19:11:47]] [INFO] Executing action 455/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[19:11:47]] [SUCCESS] Screenshot refreshed
[[19:11:47]] [INFO] Refreshing screenshot...
[[19:11:41]] [SUCCESS] Screenshot refreshed successfully
[[19:11:41]] [SUCCESS] Screenshot refreshed successfully
[[19:11:41]] [INFO] Executing action 454/512: swipeTillVisible action
[[19:11:40]] [SUCCESS] Screenshot refreshed
[[19:11:40]] [INFO] Refreshing screenshot...
[[19:11:36]] [SUCCESS] Screenshot refreshed successfully
[[19:11:36]] [SUCCESS] Screenshot refreshed successfully
[[19:11:36]] [INFO] Executing action 453/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[19:11:35]] [SUCCESS] Screenshot refreshed
[[19:11:35]] [INFO] Refreshing screenshot...
[[19:11:32]] [SUCCESS] Screenshot refreshed successfully
[[19:11:32]] [SUCCESS] Screenshot refreshed successfully
[[19:11:31]] [INFO] Executing action 452/512: Tap on image: banner-close-updated.png
[[19:11:31]] [SUCCESS] Screenshot refreshed
[[19:11:31]] [INFO] Refreshing screenshot...
[[19:11:27]] [SUCCESS] Screenshot refreshed successfully
[[19:11:27]] [SUCCESS] Screenshot refreshed successfully
[[19:11:27]] [INFO] Executing action 451/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[19:11:27]] [SUCCESS] Screenshot refreshed
[[19:11:27]] [INFO] Refreshing screenshot...
[[19:11:23]] [SUCCESS] Screenshot refreshed successfully
[[19:11:23]] [SUCCESS] Screenshot refreshed successfully
[[19:11:23]] [INFO] Executing action 450/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[19:11:23]] [SUCCESS] Screenshot refreshed
[[19:11:23]] [INFO] Refreshing screenshot...
[[19:11:19]] [INFO] Executing action 449/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[19:11:19]] [SUCCESS] Screenshot refreshed successfully
[[19:11:19]] [SUCCESS] Screenshot refreshed successfully
[[19:11:18]] [SUCCESS] Screenshot refreshed
[[19:11:18]] [INFO] Refreshing screenshot...
[[19:11:15]] [SUCCESS] Screenshot refreshed successfully
[[19:11:15]] [SUCCESS] Screenshot refreshed successfully
[[19:11:15]] [INFO] Executing action 448/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[19:11:14]] [SUCCESS] Screenshot refreshed
[[19:11:14]] [INFO] Refreshing screenshot...
[[19:11:09]] [SUCCESS] Screenshot refreshed successfully
[[19:11:09]] [SUCCESS] Screenshot refreshed successfully
[[19:11:09]] [INFO] Executing action 447/512: Tap on element with accessibility_id: Add to bag
[[19:11:08]] [SUCCESS] Screenshot refreshed
[[19:11:08]] [INFO] Refreshing screenshot...
[[19:11:05]] [SUCCESS] Screenshot refreshed successfully
[[19:11:05]] [SUCCESS] Screenshot refreshed successfully
[[19:11:05]] [INFO] Executing action 446/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[19:11:04]] [SUCCESS] Screenshot refreshed
[[19:11:04]] [INFO] Refreshing screenshot...
[[19:10:59]] [SUCCESS] Screenshot refreshed successfully
[[19:10:59]] [SUCCESS] Screenshot refreshed successfully
[[19:10:58]] [INFO] Executing action 445/512: Tap on Text: "List"
[[19:10:57]] [SUCCESS] Screenshot refreshed
[[19:10:57]] [INFO] Refreshing screenshot...
[[19:10:53]] [SUCCESS] Screenshot refreshed successfully
[[19:10:53]] [SUCCESS] Screenshot refreshed successfully
[[19:10:52]] [INFO] Executing action 444/512: Tap on image: env[catalogue-menu-img]
[[19:10:51]] [SUCCESS] Screenshot refreshed
[[19:10:51]] [INFO] Refreshing screenshot...
[[19:10:46]] [SUCCESS] Screenshot refreshed successfully
[[19:10:46]] [SUCCESS] Screenshot refreshed successfully
[[19:10:46]] [INFO] Executing action 443/512: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[19:10:45]] [SUCCESS] Screenshot refreshed
[[19:10:45]] [INFO] Refreshing screenshot...
[[19:10:41]] [SUCCESS] Screenshot refreshed successfully
[[19:10:41]] [SUCCESS] Screenshot refreshed successfully
[[19:10:41]] [INFO] Executing action 442/512: Tap on Text: "Catalogue"
[[19:10:40]] [SUCCESS] Screenshot refreshed
[[19:10:40]] [INFO] Refreshing screenshot...
[[19:10:37]] [SUCCESS] Screenshot refreshed successfully
[[19:10:37]] [SUCCESS] Screenshot refreshed successfully
[[19:10:36]] [INFO] Executing action 441/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[19:10:36]] [SUCCESS] Screenshot refreshed
[[19:10:36]] [INFO] Refreshing screenshot...
[[19:10:30]] [SUCCESS] Screenshot refreshed successfully
[[19:10:30]] [SUCCESS] Screenshot refreshed successfully
[[19:10:30]] [INFO] Executing action 440/512: Tap on element with accessibility_id: Add to bag
[[19:10:30]] [SUCCESS] Screenshot refreshed
[[19:10:30]] [INFO] Refreshing screenshot...
[[19:10:26]] [SUCCESS] Screenshot refreshed successfully
[[19:10:26]] [SUCCESS] Screenshot refreshed successfully
[[19:10:26]] [INFO] Executing action 439/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[19:10:26]] [SUCCESS] Screenshot refreshed
[[19:10:26]] [INFO] Refreshing screenshot...
[[19:10:21]] [SUCCESS] Screenshot refreshed successfully
[[19:10:21]] [SUCCESS] Screenshot refreshed successfully
[[19:10:19]] [INFO] Executing action 438/512: Tap on Text: "List"
[[19:10:19]] [SUCCESS] Screenshot refreshed
[[19:10:19]] [INFO] Refreshing screenshot...
[[19:10:15]] [SUCCESS] Screenshot refreshed successfully
[[19:10:15]] [SUCCESS] Screenshot refreshed successfully
[[19:10:13]] [INFO] Executing action 437/512: Tap on image: env[catalogue-menu-img]
[[19:10:13]] [SUCCESS] Screenshot refreshed
[[19:10:13]] [INFO] Refreshing screenshot...
[[19:10:08]] [SUCCESS] Screenshot refreshed successfully
[[19:10:08]] [SUCCESS] Screenshot refreshed successfully
[[19:10:08]] [INFO] Executing action 436/512: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[19:10:07]] [SUCCESS] Screenshot refreshed
[[19:10:07]] [INFO] Refreshing screenshot...
[[19:10:03]] [SUCCESS] Screenshot refreshed successfully
[[19:10:03]] [SUCCESS] Screenshot refreshed successfully
[[19:10:02]] [INFO] Executing action 435/512: Tap on Text: "Catalogue"
[[19:10:02]] [SUCCESS] Screenshot refreshed
[[19:10:02]] [INFO] Refreshing screenshot...
[[19:09:59]] [SUCCESS] Screenshot refreshed successfully
[[19:09:59]] [SUCCESS] Screenshot refreshed successfully
[[19:09:59]] [INFO] Executing action 434/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[19:09:58]] [SUCCESS] Screenshot refreshed
[[19:09:58]] [INFO] Refreshing screenshot...
[[19:09:55]] [SUCCESS] Screenshot refreshed successfully
[[19:09:55]] [SUCCESS] Screenshot refreshed successfully
[[19:09:55]] [INFO] Executing action 433/512: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[19:09:54]] [SUCCESS] Screenshot refreshed
[[19:09:54]] [INFO] Refreshing screenshot...
[[19:09:51]] [SUCCESS] Screenshot refreshed successfully
[[19:09:51]] [SUCCESS] Screenshot refreshed successfully
[[19:09:51]] [INFO] Executing action 432/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[19:09:51]] [SUCCESS] Screenshot refreshed
[[19:09:51]] [INFO] Refreshing screenshot...
[[19:09:47]] [SUCCESS] Screenshot refreshed successfully
[[19:09:47]] [SUCCESS] Screenshot refreshed successfully
[[19:09:47]] [INFO] Executing action 431/512: Swipe from (50%, 70%) to (50%, 30%)
[[19:09:47]] [SUCCESS] Screenshot refreshed
[[19:09:47]] [INFO] Refreshing screenshot...
[[19:09:43]] [INFO] Executing action 430/512: iOS Function: text
[[19:09:43]] [SUCCESS] Screenshot refreshed successfully
[[19:09:43]] [SUCCESS] Screenshot refreshed successfully
[[19:09:43]] [SUCCESS] Screenshot refreshed
[[19:09:43]] [INFO] Refreshing screenshot...
[[19:09:40]] [SUCCESS] Screenshot refreshed successfully
[[19:09:40]] [SUCCESS] Screenshot refreshed successfully
[[19:09:40]] [INFO] Executing action 429/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[19:09:39]] [SUCCESS] Screenshot refreshed
[[19:09:39]] [INFO] Refreshing screenshot...
[[19:09:36]] [SUCCESS] Screenshot refreshed successfully
[[19:09:36]] [SUCCESS] Screenshot refreshed successfully
[[19:09:36]] [INFO] Executing action 428/512: Restart app: com.apple.mobilesafari
[[19:09:35]] [SUCCESS] Screenshot refreshed
[[19:09:35]] [INFO] Refreshing screenshot...
[[19:09:30]] [SUCCESS] Screenshot refreshed successfully
[[19:09:30]] [SUCCESS] Screenshot refreshed successfully
[[19:09:30]] [INFO] Executing action 427/512: Tap on Text: "out"
[[19:09:30]] [SUCCESS] Screenshot refreshed
[[19:09:30]] [INFO] Refreshing screenshot...
[[19:09:26]] [SUCCESS] Screenshot refreshed successfully
[[19:09:26]] [SUCCESS] Screenshot refreshed successfully
[[19:09:26]] [INFO] Executing action 426/512: Swipe from (50%, 70%) to (50%, 30%)
[[19:09:25]] [SUCCESS] Screenshot refreshed
[[19:09:25]] [INFO] Refreshing screenshot...
[[19:09:22]] [SUCCESS] Screenshot refreshed successfully
[[19:09:22]] [SUCCESS] Screenshot refreshed successfully
[[19:09:22]] [INFO] Executing action 425/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[19:09:21]] [SUCCESS] Screenshot refreshed
[[19:09:21]] [INFO] Refreshing screenshot...
[[19:09:16]] [INFO] Executing action 424/512: Restart app: env[appid]
[[19:09:16]] [SUCCESS] Screenshot refreshed successfully
[[19:09:16]] [SUCCESS] Screenshot refreshed successfully
[[19:09:16]] [SUCCESS] Screenshot refreshed
[[19:09:16]] [INFO] Refreshing screenshot...
[[19:09:09]] [INFO] Executing action 423/512: Wait for 5 ms
[[19:09:09]] [SUCCESS] Screenshot refreshed successfully
[[19:09:09]] [SUCCESS] Screenshot refreshed successfully
[[19:09:09]] [SUCCESS] Screenshot refreshed
[[19:09:09]] [INFO] Refreshing screenshot...
[[19:09:06]] [INFO] Executing action 422/512: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[19:09:06]] [SUCCESS] Screenshot refreshed successfully
[[19:09:06]] [SUCCESS] Screenshot refreshed successfully
[[19:09:06]] [SUCCESS] Screenshot refreshed
[[19:09:06]] [INFO] Refreshing screenshot...
[[19:09:04]] [SUCCESS] Screenshot refreshed successfully
[[19:09:04]] [SUCCESS] Screenshot refreshed successfully
[[19:09:04]] [INFO] Executing action 421/512: Launch app: com.apple.Preferences
[[19:09:04]] [SUCCESS] Screenshot refreshed
[[19:09:04]] [INFO] Refreshing screenshot...
[[19:09:02]] [INFO] Executing action 420/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[19:09:02]] [SUCCESS] Screenshot refreshed successfully
[[19:09:02]] [SUCCESS] Screenshot refreshed successfully
[[19:09:02]] [SUCCESS] Screenshot refreshed
[[19:09:02]] [INFO] Refreshing screenshot...
[[19:09:00]] [SUCCESS] Screenshot refreshed successfully
[[19:09:00]] [SUCCESS] Screenshot refreshed successfully
[[19:09:00]] [INFO] Executing action 419/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[19:08:59]] [SUCCESS] Screenshot refreshed
[[19:08:59]] [INFO] Refreshing screenshot...
[[19:08:57]] [SUCCESS] Screenshot refreshed successfully
[[19:08:57]] [SUCCESS] Screenshot refreshed successfully
[[19:08:57]] [INFO] Executing action 418/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[19:08:57]] [SUCCESS] Screenshot refreshed
[[19:08:57]] [INFO] Refreshing screenshot...
[[19:08:55]] [SUCCESS] Screenshot refreshed successfully
[[19:08:55]] [SUCCESS] Screenshot refreshed successfully
[[19:08:55]] [INFO] Executing action 417/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[19:08:54]] [SUCCESS] Screenshot refreshed
[[19:08:54]] [INFO] Refreshing screenshot...
[[19:08:52]] [SUCCESS] Screenshot refreshed successfully
[[19:08:52]] [SUCCESS] Screenshot refreshed successfully
[[19:08:52]] [INFO] Executing action 416/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[19:08:52]] [SUCCESS] Screenshot refreshed
[[19:08:52]] [INFO] Refreshing screenshot...
[[19:08:50]] [SUCCESS] Screenshot refreshed successfully
[[19:08:50]] [SUCCESS] Screenshot refreshed successfully
[[19:08:49]] [INFO] Executing action 415/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[19:08:49]] [SUCCESS] Screenshot refreshed
[[19:08:49]] [INFO] Refreshing screenshot...
[[19:08:47]] [SUCCESS] Screenshot refreshed successfully
[[19:08:47]] [SUCCESS] Screenshot refreshed successfully
[[19:08:47]] [INFO] Executing action 414/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[19:08:47]] [SUCCESS] Screenshot refreshed
[[19:08:47]] [INFO] Refreshing screenshot...
[[19:08:42]] [INFO] Executing action 413/512: Restart app: env[appid]
[[19:08:42]] [SUCCESS] Screenshot refreshed successfully
[[19:08:42]] [SUCCESS] Screenshot refreshed successfully
[[19:08:41]] [SUCCESS] Screenshot refreshed
[[19:08:41]] [INFO] Refreshing screenshot...
[[19:08:39]] [INFO] Executing action 412/512: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[19:08:39]] [SUCCESS] Screenshot refreshed successfully
[[19:08:39]] [SUCCESS] Screenshot refreshed successfully
[[19:08:38]] [SUCCESS] Screenshot refreshed
[[19:08:38]] [INFO] Refreshing screenshot...
[[19:08:34]] [INFO] Executing action 411/512: Tap on Text: "Wi-Fi"
[[19:08:34]] [SUCCESS] Screenshot refreshed successfully
[[19:08:34]] [SUCCESS] Screenshot refreshed successfully
[[19:08:33]] [SUCCESS] Screenshot refreshed
[[19:08:33]] [INFO] Refreshing screenshot...
[[19:08:31]] [SUCCESS] Screenshot refreshed successfully
[[19:08:31]] [SUCCESS] Screenshot refreshed successfully
[[19:08:31]] [INFO] Executing action 410/512: Launch app: com.apple.Preferences
[[19:08:30]] [SUCCESS] Screenshot refreshed
[[19:08:30]] [INFO] Refreshing screenshot...
[[19:08:29]] [SUCCESS] Screenshot refreshed successfully
[[19:08:29]] [SUCCESS] Screenshot refreshed successfully
[[19:08:28]] [INFO] Executing action 409/512: Terminate app: com.apple.Preferences
[[19:08:28]] [SUCCESS] Screenshot refreshed
[[19:08:28]] [INFO] Refreshing screenshot...
[[19:08:27]] [SUCCESS] Screenshot refreshed
[[19:08:27]] [INFO] Refreshing screenshot...
[[19:08:25]] [SUCCESS] Screenshot refreshed successfully
[[19:08:25]] [SUCCESS] Screenshot refreshed successfully
[[19:08:24]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[19:08:24]] [SUCCESS] Screenshot refreshed
[[19:08:24]] [INFO] Refreshing screenshot...
[[19:08:19]] [SUCCESS] Screenshot refreshed successfully
[[19:08:19]] [SUCCESS] Screenshot refreshed successfully
[[19:08:19]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[19:08:19]] [SUCCESS] Screenshot refreshed
[[19:08:19]] [INFO] Refreshing screenshot...
[[19:08:15]] [SUCCESS] Screenshot refreshed successfully
[[19:08:15]] [SUCCESS] Screenshot refreshed successfully
[[19:08:15]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[19:08:14]] [SUCCESS] Screenshot refreshed
[[19:08:14]] [INFO] Refreshing screenshot...
[[19:08:10]] [SUCCESS] Screenshot refreshed successfully
[[19:08:10]] [SUCCESS] Screenshot refreshed successfully
[[19:08:10]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[19:08:09]] [SUCCESS] Screenshot refreshed
[[19:08:09]] [INFO] Refreshing screenshot...
[[19:08:06]] [SUCCESS] Screenshot refreshed successfully
[[19:08:06]] [SUCCESS] Screenshot refreshed successfully
[[19:08:06]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[19:08:05]] [SUCCESS] Screenshot refreshed
[[19:08:05]] [INFO] Refreshing screenshot...
[[19:08:00]] [SUCCESS] Screenshot refreshed successfully
[[19:08:00]] [SUCCESS] Screenshot refreshed successfully
[[19:08:00]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[19:08:00]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[19:08:00]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[19:08:00]] [INFO] Executing action 408/512: Execute Test Case: Kmart-Signin (6 steps)
[[19:07:59]] [SUCCESS] Screenshot refreshed
[[19:07:59]] [INFO] Refreshing screenshot...
[[19:07:57]] [SUCCESS] Screenshot refreshed successfully
[[19:07:57]] [SUCCESS] Screenshot refreshed successfully
[[19:07:56]] [INFO] Executing action 407/512: iOS Function: alert_accept
[[19:07:56]] [SUCCESS] Screenshot refreshed
[[19:07:56]] [INFO] Refreshing screenshot...
[[19:07:53]] [SUCCESS] Screenshot refreshed successfully
[[19:07:53]] [SUCCESS] Screenshot refreshed successfully
[[19:07:52]] [INFO] Executing action 406/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[19:07:52]] [SUCCESS] Screenshot refreshed
[[19:07:52]] [INFO] Refreshing screenshot...
[[19:07:36]] [INFO] Executing action 405/512: Restart app: env[appid]
[[19:07:36]] [INFO] Skipping remaining steps in failed test case (moving from action 353 to next test case at 404)
[[19:07:36]] [INFO] Moving to the next test case after failure (server will handle retry)
[[19:07:36]] [ERROR] Multi Step action step 1 failed: Element with xpath '//XCUIElementTypeTextField[@name="Email"]' not found within timeout of 10.0 seconds
[[19:07:22]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[19:07:22]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[19:07:22]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[19:07:22]] [INFO] Executing action 353/512: Execute Test Case: Kmart-Signin (6 steps)
[[19:07:22]] [ERROR] Action 352 failed even after recovery: Element with xpath '//XCUIElementTypeTextField[@name="Email"]' not found within timeout of 30.0 seconds
[[19:06:49]] [INFO] Retrying failed action...
[[19:06:49]] [SUCCESS] Hook Action 1: tap - ✅ Tapped at (136, 803) using OpenCV image recognition
[[19:06:43]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[19:06:43]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[19:06:43]] [ERROR] Action 352 failed: Element with xpath '//XCUIElementTypeTextField[@name="Email"]' not found within timeout of 30.0 seconds
[[19:06:10]] [SUCCESS] Screenshot refreshed successfully
[[19:06:10]] [SUCCESS] Screenshot refreshed successfully
[[19:06:10]] [INFO] Executing action 352/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[19:06:10]] [SUCCESS] Screenshot refreshed
[[19:06:10]] [INFO] Refreshing screenshot...
[[19:06:07]] [INFO] Executing action 351/512: iOS Function: alert_accept
[[19:06:07]] [ERROR] Action 350 failed even after recovery: All locators failed (primary + 1 fallbacks)
[[19:05:46]] [INFO] Retrying failed action...
[[19:05:46]] [SUCCESS] Hook Action 1: tap - ✅ Tapped at (218, 803) using OpenCV image recognition
[[19:05:39]] [INFO] Executing 1 Hook Actions via dedicated handler...
[[19:05:39]] [INFO] Action failed. Executing 1 Hook Actions for recovery...
[[19:05:39]] [ERROR] Action 350 failed: All locators failed (primary + 1 fallbacks)
[[19:05:19]] [SUCCESS] Screenshot refreshed successfully
[[19:05:19]] [SUCCESS] Screenshot refreshed successfully
[[19:05:18]] [INFO] Executing action 350/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[19:05:18]] [SUCCESS] Screenshot refreshed
[[19:05:18]] [INFO] Refreshing screenshot...
[[19:05:03]] [INFO] Executing action 349/512: Restart app: env[appid]
[[19:05:03]] [INFO] Skipping remaining steps in failed test case (moving from action 307 to next test case at 348)
[[19:05:03]] [ERROR] Action 307 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[@name="txtTrack My Order"]'
[[19:04:49]] [SUCCESS] Screenshot refreshed successfully
[[19:04:49]] [SUCCESS] Screenshot refreshed successfully
[[19:04:49]] [INFO] Executing action 307/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[19:04:48]] [SUCCESS] Screenshot refreshed
[[19:04:48]] [INFO] Refreshing screenshot...
[[19:04:45]] [SUCCESS] Screenshot refreshed successfully
[[19:04:45]] [SUCCESS] Screenshot refreshed successfully
[[19:04:45]] [INFO] Executing action 306/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:04:44]] [SUCCESS] Screenshot refreshed
[[19:04:44]] [INFO] Refreshing screenshot...
[[19:04:41]] [SUCCESS] Screenshot refreshed successfully
[[19:04:41]] [SUCCESS] Screenshot refreshed successfully
[[19:04:39]] [INFO] Executing action 305/512: Tap on image: env[device-back-img]
[[19:04:39]] [SUCCESS] Screenshot refreshed
[[19:04:39]] [INFO] Refreshing screenshot...
[[19:04:36]] [SUCCESS] Screenshot refreshed successfully
[[19:04:36]] [SUCCESS] Screenshot refreshed successfully
[[19:04:35]] [INFO] Executing action 304/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[19:04:35]] [SUCCESS] Screenshot refreshed
[[19:04:35]] [INFO] Refreshing screenshot...
[[19:04:32]] [SUCCESS] Screenshot refreshed successfully
[[19:04:32]] [SUCCESS] Screenshot refreshed successfully
[[19:04:31]] [INFO] Executing action 303/512: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[19:04:30]] [SUCCESS] Screenshot refreshed
[[19:04:30]] [INFO] Refreshing screenshot...
[[19:04:28]] [SUCCESS] Screenshot refreshed successfully
[[19:04:28]] [SUCCESS] Screenshot refreshed successfully
[[19:04:27]] [INFO] Executing action 302/512: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[19:04:26]] [SUCCESS] Screenshot refreshed
[[19:04:26]] [INFO] Refreshing screenshot...
[[19:04:24]] [SUCCESS] Screenshot refreshed successfully
[[19:04:24]] [SUCCESS] Screenshot refreshed successfully
[[19:04:23]] [INFO] Executing action 301/512: iOS Function: alert_accept
[[19:04:22]] [SUCCESS] Screenshot refreshed
[[19:04:22]] [INFO] Refreshing screenshot...
[[19:04:18]] [SUCCESS] Screenshot refreshed successfully
[[19:04:18]] [SUCCESS] Screenshot refreshed successfully
[[19:04:18]] [INFO] Executing action 300/512: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[19:04:17]] [SUCCESS] Screenshot refreshed
[[19:04:17]] [INFO] Refreshing screenshot...
[[19:04:02]] [INFO] Executing action 299/512: Restart app: env[appid]
[[19:04:02]] [INFO] Skipping remaining steps in failed test case (moving from action 273 to next test case at 298)
[[19:04:02]] [ERROR] Action 273 failed: Element with accessibility_id 'btneditFlybuysCard' not found within timeout of 15.0 seconds
[[19:03:45]] [SUCCESS] Screenshot refreshed successfully
[[19:03:45]] [SUCCESS] Screenshot refreshed successfully
[[19:03:45]] [INFO] Executing action 273/512: Wait till accessibility_id=btneditFlybuysCard
[[19:03:45]] [SUCCESS] Screenshot refreshed
[[19:03:45]] [INFO] Refreshing screenshot...
[[19:03:40]] [SUCCESS] Screenshot refreshed successfully
[[19:03:40]] [SUCCESS] Screenshot refreshed successfully
[[19:03:40]] [INFO] Executing action 272/512: Tap on Text: "Flybuys"
[[19:03:40]] [SUCCESS] Screenshot refreshed
[[19:03:40]] [INFO] Refreshing screenshot...
[[19:03:36]] [SUCCESS] Screenshot refreshed successfully
[[19:03:36]] [SUCCESS] Screenshot refreshed successfully
[[19:03:36]] [INFO] Executing action 271/512: Tap on image: env[device-back-img]
[[19:03:35]] [SUCCESS] Screenshot refreshed
[[19:03:35]] [INFO] Refreshing screenshot...
[[19:03:32]] [SUCCESS] Screenshot refreshed successfully
[[19:03:32]] [SUCCESS] Screenshot refreshed successfully
[[19:03:31]] [INFO] Executing action 270/512: Tap on image: env[device-back-img]
[[19:03:31]] [SUCCESS] Screenshot refreshed
[[19:03:31]] [INFO] Refreshing screenshot...
[[19:03:27]] [SUCCESS] Screenshot refreshed successfully
[[19:03:27]] [SUCCESS] Screenshot refreshed successfully
[[19:03:27]] [INFO] Executing action 269/512: Tap on Text: "payment"
[[19:03:26]] [SUCCESS] Screenshot refreshed
[[19:03:26]] [INFO] Refreshing screenshot...
[[19:03:22]] [SUCCESS] Screenshot refreshed successfully
[[19:03:22]] [SUCCESS] Screenshot refreshed successfully
[[19:03:22]] [INFO] Executing action 268/512: Tap on image: env[device-back-img]
[[19:03:22]] [SUCCESS] Screenshot refreshed
[[19:03:22]] [INFO] Refreshing screenshot...
[[19:03:18]] [SUCCESS] Screenshot refreshed successfully
[[19:03:18]] [SUCCESS] Screenshot refreshed successfully
[[19:03:17]] [INFO] Executing action 267/512: Tap on Text: "address"
[[19:03:17]] [SUCCESS] Screenshot refreshed
[[19:03:17]] [INFO] Refreshing screenshot...
[[19:03:13]] [SUCCESS] Screenshot refreshed successfully
[[19:03:13]] [SUCCESS] Screenshot refreshed successfully
[[19:03:13]] [INFO] Executing action 266/512: Tap on image: env[device-back-img]
[[19:03:13]] [SUCCESS] Screenshot refreshed
[[19:03:13]] [INFO] Refreshing screenshot...
[[19:03:08]] [SUCCESS] Screenshot refreshed successfully
[[19:03:08]] [SUCCESS] Screenshot refreshed successfully
[[19:03:08]] [INFO] Executing action 265/512: Tap on Text: "details"
[[19:03:08]] [SUCCESS] Screenshot refreshed
[[19:03:08]] [INFO] Refreshing screenshot...
[[19:03:04]] [SUCCESS] Screenshot refreshed successfully
[[19:03:04]] [SUCCESS] Screenshot refreshed successfully
[[19:03:04]] [INFO] Executing action 264/512: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[19:03:04]] [SUCCESS] Screenshot refreshed
[[19:03:04]] [INFO] Refreshing screenshot...
[[19:03:01]] [SUCCESS] Screenshot refreshed successfully
[[19:03:01]] [SUCCESS] Screenshot refreshed successfully
[[19:03:00]] [INFO] Executing action 263/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:03:00]] [SUCCESS] Screenshot refreshed
[[19:03:00]] [INFO] Refreshing screenshot...
[[19:02:56]] [SUCCESS] Screenshot refreshed successfully
[[19:02:56]] [SUCCESS] Screenshot refreshed successfully
[[19:02:56]] [INFO] Executing action 262/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[19:02:55]] [SUCCESS] Screenshot refreshed
[[19:02:55]] [INFO] Refreshing screenshot...
[[19:02:51]] [SUCCESS] Screenshot refreshed successfully
[[19:02:51]] [SUCCESS] Screenshot refreshed successfully
[[19:02:51]] [INFO] Executing action 261/512: Tap on Text: "Return"
[[19:02:50]] [SUCCESS] Screenshot refreshed
[[19:02:50]] [INFO] Refreshing screenshot...
[[19:02:44]] [SUCCESS] Screenshot refreshed successfully
[[19:02:44]] [SUCCESS] Screenshot refreshed successfully
[[19:02:43]] [INFO] Executing action 260/512: Wait for 5 ms
[[19:02:43]] [SUCCESS] Screenshot refreshed
[[19:02:43]] [INFO] Refreshing screenshot...
[[19:02:31]] [SUCCESS] Screenshot refreshed successfully
[[19:02:31]] [SUCCESS] Screenshot refreshed successfully
[[19:02:31]] [INFO] Executing action 259/512: Swipe from (50%, 70%) to (50%, 30%)
[[19:02:31]] [SUCCESS] Screenshot refreshed
[[19:02:31]] [INFO] Refreshing screenshot...
[[19:02:27]] [SUCCESS] Screenshot refreshed successfully
[[19:02:27]] [SUCCESS] Screenshot refreshed successfully
[[19:02:27]] [INFO] Executing action 258/512: Tap on image: env[device-back-img]
[[19:02:26]] [SUCCESS] Screenshot refreshed
[[19:02:26]] [INFO] Refreshing screenshot...
[[19:02:24]] [SUCCESS] Screenshot refreshed successfully
[[19:02:24]] [SUCCESS] Screenshot refreshed successfully
[[19:02:24]] [INFO] Executing action 257/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[19:02:23]] [SUCCESS] Screenshot refreshed
[[19:02:23]] [INFO] Refreshing screenshot...
[[19:02:19]] [SUCCESS] Screenshot refreshed successfully
[[19:02:19]] [SUCCESS] Screenshot refreshed successfully
[[19:02:19]] [INFO] Executing action 256/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[19:02:19]] [SUCCESS] Screenshot refreshed
[[19:02:19]] [INFO] Refreshing screenshot...
[[19:02:16]] [SUCCESS] Screenshot refreshed successfully
[[19:02:16]] [SUCCESS] Screenshot refreshed successfully
[[19:02:16]] [INFO] Executing action 255/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[19:02:16]] [SUCCESS] Screenshot refreshed
[[19:02:16]] [INFO] Refreshing screenshot...
[[19:02:12]] [SUCCESS] Screenshot refreshed successfully
[[19:02:12]] [SUCCESS] Screenshot refreshed successfully
[[19:02:12]] [INFO] Executing action 254/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[19:02:12]] [SUCCESS] Screenshot refreshed
[[19:02:12]] [INFO] Refreshing screenshot...
[[19:02:05]] [SUCCESS] Screenshot refreshed successfully
[[19:02:05]] [SUCCESS] Screenshot refreshed successfully
[[19:02:05]] [INFO] Executing action 253/512: Wait for 5 ms
[[19:02:04]] [SUCCESS] Screenshot refreshed
[[19:02:04]] [INFO] Refreshing screenshot...
[[19:02:01]] [SUCCESS] Screenshot refreshed successfully
[[19:02:01]] [SUCCESS] Screenshot refreshed successfully
[[19:02:01]] [INFO] Executing action 252/512: Tap on image: env[device-back-img]
[[19:02:00]] [SUCCESS] Screenshot refreshed
[[19:02:00]] [INFO] Refreshing screenshot...
[[19:01:56]] [INFO] Executing action 251/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[19:01:56]] [SUCCESS] Screenshot refreshed successfully
[[19:01:56]] [SUCCESS] Screenshot refreshed successfully
[[19:01:56]] [SUCCESS] Screenshot refreshed
[[19:01:56]] [INFO] Refreshing screenshot...
[[19:01:51]] [SUCCESS] Screenshot refreshed successfully
[[19:01:51]] [SUCCESS] Screenshot refreshed successfully
[[19:01:51]] [INFO] Executing action 250/512: Tap on element with accessibility_id: Print order details
[[19:01:50]] [SUCCESS] Screenshot refreshed
[[19:01:50]] [INFO] Refreshing screenshot...
[[19:01:47]] [SUCCESS] Screenshot refreshed successfully
[[19:01:47]] [SUCCESS] Screenshot refreshed successfully
[[19:01:47]] [INFO] Executing action 249/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[19:01:46]] [SUCCESS] Screenshot refreshed
[[19:01:46]] [INFO] Refreshing screenshot...
[[19:01:32]] [SUCCESS] Screenshot refreshed successfully
[[19:01:32]] [SUCCESS] Screenshot refreshed successfully
[[19:01:32]] [INFO] Executing action 248/512: swipeTillVisible action
[[19:01:31]] [SUCCESS] Screenshot refreshed
[[19:01:31]] [INFO] Refreshing screenshot...
[[19:01:28]] [SUCCESS] Screenshot refreshed successfully
[[19:01:28]] [SUCCESS] Screenshot refreshed successfully
[[19:01:28]] [INFO] Executing action 247/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[19:01:27]] [SUCCESS] Screenshot refreshed
[[19:01:27]] [INFO] Refreshing screenshot...
[[19:01:21]] [SUCCESS] Screenshot refreshed successfully
[[19:01:21]] [SUCCESS] Screenshot refreshed successfully
[[19:01:21]] [INFO] Executing action 246/512: Wait for 5 ms
[[19:01:20]] [SUCCESS] Screenshot refreshed
[[19:01:20]] [INFO] Refreshing screenshot...
[[19:01:16]] [SUCCESS] Screenshot refreshed successfully
[[19:01:16]] [SUCCESS] Screenshot refreshed successfully
[[19:01:16]] [INFO] Executing action 245/512: Tap on Text: "receipts"
[[19:01:15]] [SUCCESS] Screenshot refreshed
[[19:01:15]] [INFO] Refreshing screenshot...
[[19:01:12]] [SUCCESS] Screenshot refreshed successfully
[[19:01:12]] [SUCCESS] Screenshot refreshed successfully
[[19:01:12]] [INFO] Executing action 244/512: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[19:01:12]] [SUCCESS] Screenshot refreshed
[[19:01:12]] [INFO] Refreshing screenshot...
[[19:01:10]] [SUCCESS] Screenshot refreshed successfully
[[19:01:10]] [SUCCESS] Screenshot refreshed successfully
[[19:01:08]] [INFO] Executing action 243/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:01:08]] [SUCCESS] Screenshot refreshed
[[19:01:08]] [INFO] Refreshing screenshot...
[[19:01:07]] [SUCCESS] Screenshot refreshed
[[19:01:07]] [INFO] Refreshing screenshot...
[[19:01:05]] [SUCCESS] Screenshot refreshed successfully
[[19:01:05]] [SUCCESS] Screenshot refreshed successfully
[[19:01:04]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[19:01:04]] [SUCCESS] Screenshot refreshed
[[19:01:04]] [INFO] Refreshing screenshot...
[[19:00:59]] [SUCCESS] Screenshot refreshed successfully
[[19:00:59]] [SUCCESS] Screenshot refreshed successfully
[[19:00:59]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[19:00:59]] [SUCCESS] Screenshot refreshed
[[19:00:59]] [INFO] Refreshing screenshot...
[[19:00:55]] [SUCCESS] Screenshot refreshed successfully
[[19:00:55]] [SUCCESS] Screenshot refreshed successfully
[[19:00:55]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[19:00:54]] [SUCCESS] Screenshot refreshed
[[19:00:54]] [INFO] Refreshing screenshot...
[[19:00:50]] [SUCCESS] Screenshot refreshed successfully
[[19:00:50]] [SUCCESS] Screenshot refreshed successfully
[[19:00:50]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[19:00:49]] [SUCCESS] Screenshot refreshed
[[19:00:49]] [INFO] Refreshing screenshot...
[[19:00:46]] [SUCCESS] Screenshot refreshed successfully
[[19:00:46]] [SUCCESS] Screenshot refreshed successfully
[[19:00:46]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[19:00:45]] [SUCCESS] Screenshot refreshed
[[19:00:45]] [INFO] Refreshing screenshot...
[[19:00:40]] [SUCCESS] Screenshot refreshed successfully
[[19:00:40]] [SUCCESS] Screenshot refreshed successfully
[[19:00:40]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[19:00:40]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[19:00:40]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[19:00:40]] [INFO] Executing action 242/512: Execute Test Case: Kmart-Signin (8 steps)
[[19:00:39]] [SUCCESS] Screenshot refreshed
[[19:00:39]] [INFO] Refreshing screenshot...
[[19:00:37]] [SUCCESS] Screenshot refreshed successfully
[[19:00:37]] [SUCCESS] Screenshot refreshed successfully
[[19:00:36]] [INFO] Executing action 241/512: iOS Function: alert_accept
[[19:00:36]] [SUCCESS] Screenshot refreshed
[[19:00:36]] [INFO] Refreshing screenshot...
[[19:00:30]] [SUCCESS] Screenshot refreshed successfully
[[19:00:30]] [SUCCESS] Screenshot refreshed successfully
[[19:00:29]] [INFO] Executing action 240/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[19:00:29]] [SUCCESS] Screenshot refreshed
[[19:00:29]] [INFO] Refreshing screenshot...
[[19:00:22]] [SUCCESS] Screenshot refreshed successfully
[[19:00:22]] [SUCCESS] Screenshot refreshed successfully
[[19:00:22]] [INFO] Executing action 239/512: Wait for 5 ms
[[19:00:21]] [SUCCESS] Screenshot refreshed
[[19:00:21]] [INFO] Refreshing screenshot...
[[19:00:08]] [SUCCESS] Screenshot refreshed successfully
[[19:00:08]] [SUCCESS] Screenshot refreshed successfully
[[19:00:07]] [INFO] Executing action 238/512: Restart app: env[appid]
[[19:00:07]] [SUCCESS] Screenshot refreshed
[[19:00:07]] [INFO] Refreshing screenshot...
[[19:00:07]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[19:00:06]] [INFO] Executing action 237/512: Hook Action: tap on image: banner-close-updated.png (Recovery)
[[19:00:06]] [SUCCESS] Screenshot refreshed
[[19:00:06]] [INFO] Refreshing screenshot...
[[19:00:03]] [SUCCESS] Screenshot refreshed successfully
[[19:00:03]] [SUCCESS] Screenshot refreshed successfully
[[19:00:03]] [INFO] Executing action 236/512: Terminate app: env[appid]
[[19:00:02]] [SUCCESS] Screenshot refreshed
[[19:00:02]] [INFO] Refreshing screenshot...
[[18:59:58]] [SUCCESS] Screenshot refreshed successfully
[[18:59:58]] [SUCCESS] Screenshot refreshed successfully
[[18:59:58]] [INFO] Executing action 235/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:59:58]] [SUCCESS] Screenshot refreshed
[[18:59:58]] [INFO] Refreshing screenshot...
[[18:59:51]] [SUCCESS] Screenshot refreshed successfully
[[18:59:51]] [SUCCESS] Screenshot refreshed successfully
[[18:59:51]] [INFO] Executing action 234/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:59:51]] [SUCCESS] Screenshot refreshed
[[18:59:51]] [INFO] Refreshing screenshot...
[[18:59:48]] [SUCCESS] Screenshot refreshed successfully
[[18:59:48]] [SUCCESS] Screenshot refreshed successfully
[[18:59:47]] [INFO] Executing action 233/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:59:47]] [SUCCESS] Screenshot refreshed
[[18:59:47]] [INFO] Refreshing screenshot...
[[18:59:42]] [INFO] Executing action 232/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:59:42]] [SUCCESS] Screenshot refreshed successfully
[[18:59:42]] [SUCCESS] Screenshot refreshed successfully
[[18:59:42]] [SUCCESS] Screenshot refreshed
[[18:59:42]] [INFO] Refreshing screenshot...
[[18:59:38]] [SUCCESS] Screenshot refreshed successfully
[[18:59:38]] [SUCCESS] Screenshot refreshed successfully
[[18:59:38]] [INFO] Executing action 231/512: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[18:59:37]] [SUCCESS] Screenshot refreshed
[[18:59:37]] [INFO] Refreshing screenshot...
[[18:59:33]] [SUCCESS] Screenshot refreshed successfully
[[18:59:33]] [SUCCESS] Screenshot refreshed successfully
[[18:59:33]] [INFO] Executing action 230/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[18:59:33]] [SUCCESS] Screenshot refreshed
[[18:59:33]] [INFO] Refreshing screenshot...
[[18:59:27]] [SUCCESS] Screenshot refreshed successfully
[[18:59:27]] [SUCCESS] Screenshot refreshed successfully
[[18:59:26]] [INFO] Executing action 229/512: swipeTillVisible action
[[18:59:26]] [SUCCESS] Screenshot refreshed
[[18:59:26]] [INFO] Refreshing screenshot...
[[18:59:23]] [SUCCESS] Screenshot refreshed successfully
[[18:59:23]] [SUCCESS] Screenshot refreshed successfully
[[18:59:23]] [INFO] Executing action 228/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:59:22]] [SUCCESS] Screenshot refreshed
[[18:59:22]] [INFO] Refreshing screenshot...
[[18:59:20]] [SUCCESS] Screenshot refreshed successfully
[[18:59:20]] [SUCCESS] Screenshot refreshed successfully
[[18:59:19]] [INFO] Executing action 227/512: iOS Function: alert_accept
[[18:59:19]] [SUCCESS] Screenshot refreshed
[[18:59:19]] [INFO] Refreshing screenshot...
[[18:59:14]] [SUCCESS] Screenshot refreshed successfully
[[18:59:14]] [SUCCESS] Screenshot refreshed successfully
[[18:59:13]] [INFO] Executing action 226/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:59:13]] [SUCCESS] Screenshot refreshed
[[18:59:13]] [INFO] Refreshing screenshot...
[[18:59:10]] [SUCCESS] Screenshot refreshed successfully
[[18:59:10]] [SUCCESS] Screenshot refreshed successfully
[[18:59:09]] [INFO] Executing action 225/512: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:59:08]] [SUCCESS] Screenshot refreshed
[[18:59:08]] [INFO] Refreshing screenshot...
[[18:59:05]] [SUCCESS] Screenshot refreshed successfully
[[18:59:05]] [SUCCESS] Screenshot refreshed successfully
[[18:59:05]] [INFO] Executing action 224/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:59:04]] [SUCCESS] Screenshot refreshed
[[18:59:04]] [INFO] Refreshing screenshot...
[[18:58:58]] [SUCCESS] Screenshot refreshed successfully
[[18:58:58]] [SUCCESS] Screenshot refreshed successfully
[[18:58:58]] [INFO] Executing action 223/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:58:57]] [SUCCESS] Screenshot refreshed
[[18:58:57]] [INFO] Refreshing screenshot...
[[18:58:54]] [SUCCESS] Screenshot refreshed successfully
[[18:58:54]] [SUCCESS] Screenshot refreshed successfully
[[18:58:54]] [INFO] Executing action 222/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:58:53]] [SUCCESS] Screenshot refreshed
[[18:58:53]] [INFO] Refreshing screenshot...
[[18:58:46]] [INFO] Executing action 221/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:58:46]] [SUCCESS] Screenshot refreshed successfully
[[18:58:46]] [SUCCESS] Screenshot refreshed successfully
[[18:58:46]] [SUCCESS] Screenshot refreshed
[[18:58:46]] [INFO] Refreshing screenshot...
[[18:58:43]] [INFO] Executing action 220/512: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[18:58:42]] [SUCCESS] Screenshot refreshed successfully
[[18:58:42]] [SUCCESS] Screenshot refreshed successfully
[[18:58:42]] [SUCCESS] Screenshot refreshed
[[18:58:42]] [INFO] Refreshing screenshot...
[[18:58:39]] [INFO] Executing action 219/512: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[18:58:39]] [SUCCESS] Screenshot refreshed successfully
[[18:58:39]] [SUCCESS] Screenshot refreshed successfully
[[18:58:39]] [SUCCESS] Screenshot refreshed
[[18:58:39]] [INFO] Refreshing screenshot...
[[18:58:36]] [INFO] Executing action 218/512: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[18:58:35]] [SUCCESS] Screenshot refreshed successfully
[[18:58:35]] [SUCCESS] Screenshot refreshed successfully
[[18:58:35]] [SUCCESS] Screenshot refreshed
[[18:58:35]] [INFO] Refreshing screenshot...
[[18:58:32]] [INFO] Executing action 217/512: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[18:58:32]] [SUCCESS] Screenshot refreshed successfully
[[18:58:32]] [SUCCESS] Screenshot refreshed successfully
[[18:58:32]] [SUCCESS] Screenshot refreshed
[[18:58:32]] [INFO] Refreshing screenshot...
[[18:58:29]] [INFO] Executing action 216/512: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[18:58:28]] [SUCCESS] Screenshot refreshed successfully
[[18:58:28]] [SUCCESS] Screenshot refreshed successfully
[[18:58:28]] [SUCCESS] Screenshot refreshed
[[18:58:28]] [INFO] Refreshing screenshot...
[[18:58:25]] [INFO] Executing action 215/512: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[18:58:25]] [SUCCESS] Screenshot refreshed successfully
[[18:58:25]] [SUCCESS] Screenshot refreshed successfully
[[18:58:25]] [SUCCESS] Screenshot refreshed
[[18:58:25]] [INFO] Refreshing screenshot...
[[18:58:20]] [INFO] Executing action 214/512: Tap on Text: "Passcode"
[[18:58:20]] [SUCCESS] Screenshot refreshed successfully
[[18:58:20]] [SUCCESS] Screenshot refreshed successfully
[[18:58:20]] [SUCCESS] Screenshot refreshed
[[18:58:20]] [INFO] Refreshing screenshot...
[[18:58:08]] [SUCCESS] Screenshot refreshed successfully
[[18:58:08]] [SUCCESS] Screenshot refreshed successfully
[[18:58:08]] [INFO] Executing action 213/512: Wait for 10 ms
[[18:58:08]] [SUCCESS] Screenshot refreshed
[[18:58:08]] [INFO] Refreshing screenshot...
[[18:58:03]] [SUCCESS] Screenshot refreshed successfully
[[18:58:03]] [SUCCESS] Screenshot refreshed successfully
[[18:58:03]] [INFO] Executing action 212/512: Tap on Text: "Apple"
[[18:58:02]] [SUCCESS] Screenshot refreshed
[[18:58:02]] [INFO] Refreshing screenshot...
[[18:57:58]] [SUCCESS] Screenshot refreshed successfully
[[18:57:58]] [SUCCESS] Screenshot refreshed successfully
[[18:57:58]] [INFO] Executing action 211/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:57:58]] [SUCCESS] Screenshot refreshed
[[18:57:58]] [INFO] Refreshing screenshot...
[[18:57:54]] [SUCCESS] Screenshot refreshed successfully
[[18:57:54]] [SUCCESS] Screenshot refreshed successfully
[[18:57:54]] [INFO] Executing action 210/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:57:54]] [SUCCESS] Screenshot refreshed
[[18:57:54]] [INFO] Refreshing screenshot...
[[18:57:51]] [SUCCESS] Screenshot refreshed successfully
[[18:57:51]] [SUCCESS] Screenshot refreshed successfully
[[18:57:51]] [INFO] Executing action 209/512: iOS Function: alert_accept
[[18:57:50]] [SUCCESS] Screenshot refreshed
[[18:57:50]] [INFO] Refreshing screenshot...
[[18:57:45]] [SUCCESS] Screenshot refreshed successfully
[[18:57:45]] [SUCCESS] Screenshot refreshed successfully
[[18:57:45]] [INFO] Executing action 208/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:57:44]] [SUCCESS] Screenshot refreshed
[[18:57:44]] [INFO] Refreshing screenshot...
[[18:57:41]] [SUCCESS] Screenshot refreshed successfully
[[18:57:41]] [SUCCESS] Screenshot refreshed successfully
[[18:57:41]] [INFO] Executing action 207/512: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:57:40]] [SUCCESS] Screenshot refreshed
[[18:57:40]] [INFO] Refreshing screenshot...
[[18:57:36]] [SUCCESS] Screenshot refreshed successfully
[[18:57:36]] [SUCCESS] Screenshot refreshed successfully
[[18:57:36]] [INFO] Executing action 206/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:57:36]] [SUCCESS] Screenshot refreshed
[[18:57:36]] [INFO] Refreshing screenshot...
[[18:57:31]] [SUCCESS] Screenshot refreshed successfully
[[18:57:31]] [SUCCESS] Screenshot refreshed successfully
[[18:57:31]] [INFO] Executing action 205/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:57:30]] [SUCCESS] Screenshot refreshed
[[18:57:30]] [INFO] Refreshing screenshot...
[[18:57:27]] [SUCCESS] Screenshot refreshed successfully
[[18:57:27]] [SUCCESS] Screenshot refreshed successfully
[[18:57:27]] [INFO] Executing action 204/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:57:26]] [SUCCESS] Screenshot refreshed
[[18:57:26]] [INFO] Refreshing screenshot...
[[18:57:21]] [SUCCESS] Screenshot refreshed successfully
[[18:57:21]] [SUCCESS] Screenshot refreshed successfully
[[18:57:21]] [INFO] Executing action 203/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:57:20]] [SUCCESS] Screenshot refreshed
[[18:57:20]] [INFO] Refreshing screenshot...
[[18:57:16]] [SUCCESS] Screenshot refreshed successfully
[[18:57:16]] [SUCCESS] Screenshot refreshed successfully
[[18:57:16]] [INFO] Executing action 202/512: iOS Function: text
[[18:57:15]] [SUCCESS] Screenshot refreshed
[[18:57:15]] [INFO] Refreshing screenshot...
[[18:57:12]] [SUCCESS] Screenshot refreshed successfully
[[18:57:12]] [SUCCESS] Screenshot refreshed successfully
[[18:57:11]] [INFO] Executing action 201/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[18:57:11]] [SUCCESS] Screenshot refreshed
[[18:57:11]] [INFO] Refreshing screenshot...
[[18:57:07]] [SUCCESS] Screenshot refreshed successfully
[[18:57:07]] [SUCCESS] Screenshot refreshed successfully
[[18:57:06]] [INFO] Executing action 200/512: iOS Function: text
[[18:57:06]] [SUCCESS] Screenshot refreshed
[[18:57:06]] [INFO] Refreshing screenshot...
[[18:57:02]] [SUCCESS] Screenshot refreshed successfully
[[18:57:02]] [SUCCESS] Screenshot refreshed successfully
[[18:57:02]] [INFO] Executing action 199/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[18:57:02]] [SUCCESS] Screenshot refreshed
[[18:57:02]] [INFO] Refreshing screenshot...
[[18:56:57]] [SUCCESS] Screenshot refreshed successfully
[[18:56:57]] [SUCCESS] Screenshot refreshed successfully
[[18:56:57]] [INFO] Executing action 198/512: Tap on Text: "OnePass"
[[18:56:56]] [SUCCESS] Screenshot refreshed
[[18:56:56]] [INFO] Refreshing screenshot...
[[18:56:52]] [SUCCESS] Screenshot refreshed successfully
[[18:56:52]] [SUCCESS] Screenshot refreshed successfully
[[18:56:52]] [INFO] Executing action 197/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:56:51]] [SUCCESS] Screenshot refreshed
[[18:56:51]] [INFO] Refreshing screenshot...
[[18:56:48]] [SUCCESS] Screenshot refreshed successfully
[[18:56:48]] [SUCCESS] Screenshot refreshed successfully
[[18:56:48]] [INFO] Executing action 196/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:56:48]] [SUCCESS] Screenshot refreshed
[[18:56:48]] [INFO] Refreshing screenshot...
[[18:56:45]] [SUCCESS] Screenshot refreshed successfully
[[18:56:45]] [SUCCESS] Screenshot refreshed successfully
[[18:56:45]] [INFO] Executing action 195/512: iOS Function: alert_accept
[[18:56:44]] [SUCCESS] Screenshot refreshed
[[18:56:44]] [INFO] Refreshing screenshot...
[[18:56:39]] [SUCCESS] Screenshot refreshed successfully
[[18:56:39]] [SUCCESS] Screenshot refreshed successfully
[[18:56:39]] [INFO] Executing action 194/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:56:38]] [SUCCESS] Screenshot refreshed
[[18:56:38]] [INFO] Refreshing screenshot...
[[18:56:35]] [SUCCESS] Screenshot refreshed successfully
[[18:56:35]] [SUCCESS] Screenshot refreshed successfully
[[18:56:35]] [INFO] Executing action 193/512: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:56:34]] [SUCCESS] Screenshot refreshed
[[18:56:34]] [INFO] Refreshing screenshot...
[[18:56:30]] [SUCCESS] Screenshot refreshed successfully
[[18:56:30]] [SUCCESS] Screenshot refreshed successfully
[[18:56:30]] [INFO] Executing action 192/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:56:30]] [SUCCESS] Screenshot refreshed
[[18:56:30]] [INFO] Refreshing screenshot...
[[18:56:23]] [SUCCESS] Screenshot refreshed successfully
[[18:56:23]] [SUCCESS] Screenshot refreshed successfully
[[18:56:23]] [INFO] Executing action 191/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:56:23]] [SUCCESS] Screenshot refreshed
[[18:56:23]] [INFO] Refreshing screenshot...
[[18:56:20]] [SUCCESS] Screenshot refreshed successfully
[[18:56:20]] [SUCCESS] Screenshot refreshed successfully
[[18:56:19]] [INFO] Executing action 190/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:56:19]] [SUCCESS] Screenshot refreshed
[[18:56:19]] [INFO] Refreshing screenshot...
[[18:56:16]] [SUCCESS] Screenshot refreshed successfully
[[18:56:16]] [SUCCESS] Screenshot refreshed successfully
[[18:56:16]] [INFO] Executing action 189/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:56:15]] [SUCCESS] Screenshot refreshed
[[18:56:15]] [INFO] Refreshing screenshot...
[[18:56:10]] [SUCCESS] Screenshot refreshed successfully
[[18:56:10]] [SUCCESS] Screenshot refreshed successfully
[[18:56:10]] [INFO] Executing action 188/512: iOS Function: text
[[18:56:10]] [SUCCESS] Screenshot refreshed
[[18:56:10]] [INFO] Refreshing screenshot...
[[18:56:06]] [SUCCESS] Screenshot refreshed successfully
[[18:56:06]] [SUCCESS] Screenshot refreshed successfully
[[18:56:06]] [INFO] Executing action 187/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:56:06]] [SUCCESS] Screenshot refreshed
[[18:56:06]] [INFO] Refreshing screenshot...
[[18:56:01]] [SUCCESS] Screenshot refreshed successfully
[[18:56:01]] [SUCCESS] Screenshot refreshed successfully
[[18:56:01]] [INFO] Executing action 186/512: iOS Function: text
[[18:56:01]] [SUCCESS] Screenshot refreshed
[[18:56:01]] [INFO] Refreshing screenshot...
[[18:55:57]] [SUCCESS] Screenshot refreshed successfully
[[18:55:57]] [SUCCESS] Screenshot refreshed successfully
[[18:55:57]] [INFO] Executing action 185/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:55:56]] [SUCCESS] Screenshot refreshed
[[18:55:56]] [INFO] Refreshing screenshot...
[[18:55:53]] [SUCCESS] Screenshot refreshed successfully
[[18:55:53]] [SUCCESS] Screenshot refreshed successfully
[[18:55:53]] [INFO] Executing action 184/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:55:53]] [SUCCESS] Screenshot refreshed
[[18:55:53]] [INFO] Refreshing screenshot...
[[18:55:50]] [SUCCESS] Screenshot refreshed successfully
[[18:55:50]] [SUCCESS] Screenshot refreshed successfully
[[18:55:50]] [INFO] Executing action 183/512: iOS Function: alert_accept
[[18:55:49]] [SUCCESS] Screenshot refreshed
[[18:55:49]] [INFO] Refreshing screenshot...
[[18:55:43]] [SUCCESS] Screenshot refreshed successfully
[[18:55:43]] [SUCCESS] Screenshot refreshed successfully
[[18:55:43]] [INFO] Executing action 182/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:55:42]] [SUCCESS] Screenshot refreshed
[[18:55:42]] [INFO] Refreshing screenshot...
[[18:55:28]] [SUCCESS] Screenshot refreshed successfully
[[18:55:28]] [SUCCESS] Screenshot refreshed successfully
[[18:55:27]] [INFO] Executing action 181/512: Restart app: env[appid]
[[18:55:27]] [SUCCESS] Screenshot refreshed
[[18:55:27]] [INFO] Refreshing screenshot...
[[18:55:23]] [SUCCESS] Screenshot refreshed successfully
[[18:55:23]] [SUCCESS] Screenshot refreshed successfully
[[18:55:23]] [INFO] Executing action 180/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:55:22]] [SUCCESS] Screenshot refreshed
[[18:55:22]] [INFO] Refreshing screenshot...
[[18:55:16]] [SUCCESS] Screenshot refreshed successfully
[[18:55:16]] [SUCCESS] Screenshot refreshed successfully
[[18:55:16]] [INFO] Executing action 179/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:55:15]] [SUCCESS] Screenshot refreshed
[[18:55:15]] [INFO] Refreshing screenshot...
[[18:55:12]] [SUCCESS] Screenshot refreshed successfully
[[18:55:12]] [SUCCESS] Screenshot refreshed successfully
[[18:55:12]] [INFO] Executing action 178/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:55:11]] [SUCCESS] Screenshot refreshed
[[18:55:11]] [INFO] Refreshing screenshot...
[[18:55:07]] [SUCCESS] Screenshot refreshed successfully
[[18:55:07]] [SUCCESS] Screenshot refreshed successfully
[[18:55:07]] [INFO] Executing action 177/512: Tap on Text: "Remove"
[[18:55:07]] [SUCCESS] Screenshot refreshed
[[18:55:07]] [INFO] Refreshing screenshot...
[[18:55:03]] [SUCCESS] Screenshot refreshed successfully
[[18:55:03]] [SUCCESS] Screenshot refreshed successfully
[[18:55:03]] [INFO] Executing action 176/512: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:55:02]] [SUCCESS] Screenshot refreshed
[[18:55:02]] [INFO] Refreshing screenshot...
[[18:54:58]] [SUCCESS] Screenshot refreshed successfully
[[18:54:58]] [SUCCESS] Screenshot refreshed successfully
[[18:54:58]] [INFO] Executing action 175/512: Tap on Text: "Remove"
[[18:54:57]] [SUCCESS] Screenshot refreshed
[[18:54:57]] [INFO] Refreshing screenshot...
[[18:54:53]] [SUCCESS] Screenshot refreshed successfully
[[18:54:53]] [SUCCESS] Screenshot refreshed successfully
[[18:54:53]] [INFO] Executing action 174/512: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:54:53]] [SUCCESS] Screenshot refreshed
[[18:54:53]] [INFO] Refreshing screenshot...
[[18:54:49]] [SUCCESS] Screenshot refreshed successfully
[[18:54:49]] [SUCCESS] Screenshot refreshed successfully
[[18:54:48]] [INFO] Executing action 173/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:54:48]] [SUCCESS] Screenshot refreshed
[[18:54:48]] [INFO] Refreshing screenshot...
[[18:54:44]] [SUCCESS] Screenshot refreshed successfully
[[18:54:44]] [SUCCESS] Screenshot refreshed successfully
[[18:54:44]] [INFO] Executing action 172/512: Tap on image: banner-close-updated.png
[[18:54:43]] [SUCCESS] Screenshot refreshed
[[18:54:43]] [INFO] Refreshing screenshot...
[[18:54:40]] [SUCCESS] Screenshot refreshed successfully
[[18:54:40]] [SUCCESS] Screenshot refreshed successfully
[[18:54:40]] [INFO] Executing action 171/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[18:54:39]] [SUCCESS] Screenshot refreshed
[[18:54:39]] [INFO] Refreshing screenshot...
[[18:54:36]] [SUCCESS] Screenshot refreshed successfully
[[18:54:36]] [SUCCESS] Screenshot refreshed successfully
[[18:54:36]] [INFO] Executing action 170/512: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[18:54:35]] [SUCCESS] Screenshot refreshed
[[18:54:35]] [INFO] Refreshing screenshot...
[[18:54:31]] [SUCCESS] Screenshot refreshed successfully
[[18:54:31]] [SUCCESS] Screenshot refreshed successfully
[[18:54:31]] [INFO] Executing action 169/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:54:31]] [SUCCESS] Screenshot refreshed
[[18:54:31]] [INFO] Refreshing screenshot...
[[18:54:27]] [SUCCESS] Screenshot refreshed successfully
[[18:54:27]] [SUCCESS] Screenshot refreshed successfully
[[18:54:27]] [INFO] Executing action 168/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:54:26]] [SUCCESS] Screenshot refreshed
[[18:54:26]] [INFO] Refreshing screenshot...
[[18:54:23]] [SUCCESS] Screenshot refreshed successfully
[[18:54:23]] [SUCCESS] Screenshot refreshed successfully
[[18:54:22]] [INFO] Executing action 167/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:54:22]] [SUCCESS] Screenshot refreshed
[[18:54:22]] [INFO] Refreshing screenshot...
[[18:54:18]] [SUCCESS] Screenshot refreshed successfully
[[18:54:18]] [SUCCESS] Screenshot refreshed successfully
[[18:54:18]] [INFO] Executing action 166/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[18:54:17]] [SUCCESS] Screenshot refreshed
[[18:54:17]] [INFO] Refreshing screenshot...
[[18:54:13]] [SUCCESS] Screenshot refreshed successfully
[[18:54:13]] [SUCCESS] Screenshot refreshed successfully
[[18:54:13]] [INFO] Executing action 165/512: Tap on Text: "Remove"
[[18:54:13]] [SUCCESS] Screenshot refreshed
[[18:54:13]] [INFO] Refreshing screenshot...
[[18:54:10]] [SUCCESS] Screenshot refreshed successfully
[[18:54:10]] [SUCCESS] Screenshot refreshed successfully
[[18:54:09]] [INFO] Executing action 164/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[18:54:09]] [SUCCESS] Screenshot refreshed
[[18:54:09]] [INFO] Refreshing screenshot...
[[18:54:05]] [SUCCESS] Screenshot refreshed successfully
[[18:54:05]] [SUCCESS] Screenshot refreshed successfully
[[18:54:04]] [INFO] Executing action 163/512: Tap on Text: "Move"
[[18:54:04]] [SUCCESS] Screenshot refreshed
[[18:54:04]] [INFO] Refreshing screenshot...
[[18:54:01]] [SUCCESS] Screenshot refreshed successfully
[[18:54:01]] [SUCCESS] Screenshot refreshed successfully
[[18:54:00]] [INFO] Executing action 162/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:54:00]] [SUCCESS] Screenshot refreshed
[[18:54:00]] [INFO] Refreshing screenshot...
[[18:53:57]] [SUCCESS] Screenshot refreshed successfully
[[18:53:57]] [SUCCESS] Screenshot refreshed successfully
[[18:53:57]] [INFO] Executing action 161/512: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:53:57]] [SUCCESS] Screenshot refreshed
[[18:53:57]] [INFO] Refreshing screenshot...
[[18:53:53]] [SUCCESS] Screenshot refreshed successfully
[[18:53:53]] [SUCCESS] Screenshot refreshed successfully
[[18:53:52]] [INFO] Executing action 160/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:53:52]] [SUCCESS] Screenshot refreshed
[[18:53:52]] [INFO] Refreshing screenshot...
[[18:53:48]] [SUCCESS] Screenshot refreshed successfully
[[18:53:48]] [SUCCESS] Screenshot refreshed successfully
[[18:53:48]] [INFO] Executing action 159/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:53:47]] [SUCCESS] Screenshot refreshed
[[18:53:47]] [INFO] Refreshing screenshot...
[[18:53:41]] [SUCCESS] Screenshot refreshed successfully
[[18:53:41]] [SUCCESS] Screenshot refreshed successfully
[[18:53:41]] [INFO] Executing action 158/512: swipeTillVisible action
[[18:53:40]] [SUCCESS] Screenshot refreshed
[[18:53:40]] [INFO] Refreshing screenshot...
[[18:53:36]] [SUCCESS] Screenshot refreshed successfully
[[18:53:36]] [SUCCESS] Screenshot refreshed successfully
[[18:53:36]] [INFO] Executing action 157/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:53:36]] [SUCCESS] Screenshot refreshed
[[18:53:36]] [INFO] Refreshing screenshot...
[[18:53:33]] [SUCCESS] Screenshot refreshed successfully
[[18:53:33]] [SUCCESS] Screenshot refreshed successfully
[[18:53:32]] [INFO] Executing action 156/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:53:32]] [SUCCESS] Screenshot refreshed
[[18:53:32]] [INFO] Refreshing screenshot...
[[18:53:28]] [SUCCESS] Screenshot refreshed successfully
[[18:53:28]] [SUCCESS] Screenshot refreshed successfully
[[18:53:28]] [INFO] Executing action 155/512: iOS Function: text
[[18:53:28]] [SUCCESS] Screenshot refreshed
[[18:53:28]] [INFO] Refreshing screenshot...
[[18:53:22]] [SUCCESS] Screenshot refreshed successfully
[[18:53:22]] [SUCCESS] Screenshot refreshed successfully
[[18:53:22]] [INFO] Executing action 154/512: Tap on Text: "Find"
[[18:53:21]] [SUCCESS] Screenshot refreshed
[[18:53:21]] [INFO] Refreshing screenshot...
[[18:53:16]] [SUCCESS] Screenshot refreshed successfully
[[18:53:16]] [SUCCESS] Screenshot refreshed successfully
[[18:53:16]] [INFO] Executing action 153/512: Restart app: env[appid]
[[18:53:16]] [SUCCESS] Screenshot refreshed
[[18:53:16]] [INFO] Refreshing screenshot...
[[18:53:12]] [SUCCESS] Screenshot refreshed successfully
[[18:53:12]] [SUCCESS] Screenshot refreshed successfully
[[18:53:11]] [INFO] Executing action 152/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:53:11]] [SUCCESS] Screenshot refreshed
[[18:53:11]] [INFO] Refreshing screenshot...
[[18:53:05]] [SUCCESS] Screenshot refreshed successfully
[[18:53:05]] [SUCCESS] Screenshot refreshed successfully
[[18:53:04]] [INFO] Executing action 151/512: swipeTillVisible action
[[18:53:04]] [SUCCESS] Screenshot refreshed
[[18:53:04]] [INFO] Refreshing screenshot...
[[18:53:00]] [SUCCESS] Screenshot refreshed successfully
[[18:53:00]] [SUCCESS] Screenshot refreshed successfully
[[18:53:00]] [INFO] Executing action 150/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:52:59]] [SUCCESS] Screenshot refreshed
[[18:52:59]] [INFO] Refreshing screenshot...
[[18:52:44]] [SUCCESS] Screenshot refreshed successfully
[[18:52:44]] [SUCCESS] Screenshot refreshed successfully
[[18:52:43]] [INFO] Executing action 149/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[18:52:43]] [SUCCESS] Screenshot refreshed
[[18:52:43]] [INFO] Refreshing screenshot...
[[18:52:39]] [SUCCESS] Screenshot refreshed successfully
[[18:52:39]] [SUCCESS] Screenshot refreshed successfully
[[18:52:39]] [INFO] Executing action 148/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:52:38]] [SUCCESS] Screenshot refreshed
[[18:52:38]] [INFO] Refreshing screenshot...
[[18:52:32]] [SUCCESS] Screenshot refreshed successfully
[[18:52:32]] [SUCCESS] Screenshot refreshed successfully
[[18:52:32]] [INFO] Executing action 147/512: swipeTillVisible action
[[18:52:31]] [SUCCESS] Screenshot refreshed
[[18:52:31]] [INFO] Refreshing screenshot...
[[18:52:28]] [SUCCESS] Screenshot refreshed successfully
[[18:52:28]] [SUCCESS] Screenshot refreshed successfully
[[18:52:27]] [INFO] Executing action 146/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:52:27]] [SUCCESS] Screenshot refreshed
[[18:52:27]] [INFO] Refreshing screenshot...
[[18:52:23]] [SUCCESS] Screenshot refreshed successfully
[[18:52:23]] [SUCCESS] Screenshot refreshed successfully
[[18:52:23]] [INFO] Executing action 145/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:52:22]] [SUCCESS] Screenshot refreshed
[[18:52:22]] [INFO] Refreshing screenshot...
[[18:52:19]] [SUCCESS] Screenshot refreshed successfully
[[18:52:19]] [SUCCESS] Screenshot refreshed successfully
[[18:52:19]] [INFO] Executing action 144/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:52:18]] [SUCCESS] Screenshot refreshed
[[18:52:18]] [INFO] Refreshing screenshot...
[[18:52:14]] [SUCCESS] Screenshot refreshed successfully
[[18:52:14]] [SUCCESS] Screenshot refreshed successfully
[[18:52:14]] [INFO] Executing action 143/512: iOS Function: text
[[18:52:14]] [SUCCESS] Screenshot refreshed
[[18:52:14]] [INFO] Refreshing screenshot...
[[18:52:09]] [SUCCESS] Screenshot refreshed successfully
[[18:52:09]] [SUCCESS] Screenshot refreshed successfully
[[18:52:08]] [INFO] Executing action 142/512: Tap on Text: "Find"
[[18:52:07]] [SUCCESS] Screenshot refreshed
[[18:52:07]] [INFO] Refreshing screenshot...
[[18:52:05]] [SUCCESS] Screenshot refreshed successfully
[[18:52:05]] [SUCCESS] Screenshot refreshed successfully
[[18:52:04]] [INFO] Executing action 141/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:52:04]] [SUCCESS] Screenshot refreshed
[[18:52:04]] [INFO] Refreshing screenshot...
[[18:51:59]] [SUCCESS] Screenshot refreshed successfully
[[18:51:59]] [SUCCESS] Screenshot refreshed successfully
[[18:51:59]] [INFO] Executing action 140/512: iOS Function: text
[[18:51:59]] [SUCCESS] Screenshot refreshed
[[18:51:59]] [INFO] Refreshing screenshot...
[[18:51:55]] [SUCCESS] Screenshot refreshed successfully
[[18:51:55]] [SUCCESS] Screenshot refreshed successfully
[[18:51:55]] [INFO] Executing action 139/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:51:54]] [SUCCESS] Screenshot refreshed
[[18:51:54]] [INFO] Refreshing screenshot...
[[18:51:50]] [SUCCESS] Screenshot refreshed successfully
[[18:51:50]] [SUCCESS] Screenshot refreshed successfully
[[18:51:50]] [INFO] Executing action 138/512: iOS Function: text
[[18:51:49]] [SUCCESS] Screenshot refreshed
[[18:51:49]] [INFO] Refreshing screenshot...
[[18:51:46]] [SUCCESS] Screenshot refreshed successfully
[[18:51:46]] [SUCCESS] Screenshot refreshed successfully
[[18:51:46]] [INFO] Executing action 137/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:51:45]] [SUCCESS] Screenshot refreshed
[[18:51:45]] [INFO] Refreshing screenshot...
[[18:51:42]] [SUCCESS] Screenshot refreshed successfully
[[18:51:42]] [SUCCESS] Screenshot refreshed successfully
[[18:51:42]] [INFO] Executing action 136/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:51:41]] [SUCCESS] Screenshot refreshed
[[18:51:41]] [INFO] Refreshing screenshot...
[[18:51:39]] [SUCCESS] Screenshot refreshed successfully
[[18:51:39]] [SUCCESS] Screenshot refreshed successfully
[[18:51:39]] [INFO] Executing action 135/512: iOS Function: alert_accept
[[18:51:38]] [SUCCESS] Screenshot refreshed
[[18:51:38]] [INFO] Refreshing screenshot...
[[18:51:32]] [SUCCESS] Screenshot refreshed successfully
[[18:51:32]] [SUCCESS] Screenshot refreshed successfully
[[18:51:32]] [INFO] Executing action 134/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:51:31]] [SUCCESS] Screenshot refreshed
[[18:51:31]] [INFO] Refreshing screenshot...
[[18:51:16]] [SUCCESS] Screenshot refreshed successfully
[[18:51:16]] [SUCCESS] Screenshot refreshed successfully
[[18:51:16]] [INFO] Executing action 133/512: Restart app: env[appid]
[[18:51:15]] [SUCCESS] Screenshot refreshed
[[18:51:15]] [INFO] Refreshing screenshot...
[[18:51:15]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[18:51:15]] [INFO] Executing action 132/512: Hook Action: tap on image: banner-close-updated.png (Recovery)
[[18:51:15]] [SUCCESS] Screenshot refreshed
[[18:51:15]] [INFO] Refreshing screenshot...
[[18:51:11]] [SUCCESS] Screenshot refreshed successfully
[[18:51:11]] [SUCCESS] Screenshot refreshed successfully
[[18:51:11]] [INFO] Executing action 131/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:51:10]] [SUCCESS] Screenshot refreshed
[[18:51:10]] [INFO] Refreshing screenshot...
[[18:51:04]] [SUCCESS] Screenshot refreshed successfully
[[18:51:04]] [SUCCESS] Screenshot refreshed successfully
[[18:51:04]] [INFO] Executing action 130/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:51:03]] [SUCCESS] Screenshot refreshed
[[18:51:03]] [INFO] Refreshing screenshot...
[[18:51:00]] [SUCCESS] Screenshot refreshed successfully
[[18:51:00]] [SUCCESS] Screenshot refreshed successfully
[[18:51:00]] [INFO] Executing action 129/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:50:59]] [SUCCESS] Screenshot refreshed
[[18:50:59]] [INFO] Refreshing screenshot...
[[18:50:58]] [SUCCESS] Screenshot refreshed successfully
[[18:50:58]] [SUCCESS] Screenshot refreshed successfully
[[18:50:57]] [INFO] Executing action 128/512: Add Log: Sign in from universal login page successful (with screenshot)
[[18:50:57]] [SUCCESS] Screenshot refreshed
[[18:50:57]] [INFO] Refreshing screenshot...
[[18:50:57]] [SUCCESS] Screenshot refreshed
[[18:50:57]] [INFO] Refreshing screenshot...
[[18:50:54]] [SUCCESS] Screenshot refreshed successfully
[[18:50:54]] [SUCCESS] Screenshot refreshed successfully
[[18:50:54]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:50:53]] [SUCCESS] Screenshot refreshed
[[18:50:53]] [INFO] Refreshing screenshot...
[[18:50:49]] [SUCCESS] Screenshot refreshed successfully
[[18:50:49]] [SUCCESS] Screenshot refreshed successfully
[[18:50:48]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[18:50:48]] [SUCCESS] Screenshot refreshed
[[18:50:48]] [INFO] Refreshing screenshot...
[[18:50:44]] [SUCCESS] Screenshot refreshed successfully
[[18:50:44]] [SUCCESS] Screenshot refreshed successfully
[[18:50:44]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:50:44]] [SUCCESS] Screenshot refreshed
[[18:50:44]] [INFO] Refreshing screenshot...
[[18:50:39]] [SUCCESS] Screenshot refreshed successfully
[[18:50:39]] [SUCCESS] Screenshot refreshed successfully
[[18:50:39]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[18:50:39]] [SUCCESS] Screenshot refreshed
[[18:50:39]] [INFO] Refreshing screenshot...
[[18:50:35]] [SUCCESS] Screenshot refreshed successfully
[[18:50:35]] [SUCCESS] Screenshot refreshed successfully
[[18:50:35]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:50:35]] [SUCCESS] Screenshot refreshed
[[18:50:35]] [INFO] Refreshing screenshot...
[[18:50:29]] [SUCCESS] Screenshot refreshed successfully
[[18:50:29]] [SUCCESS] Screenshot refreshed successfully
[[18:50:29]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:50:29]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[18:50:29]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[18:50:29]] [INFO] Executing action 127/512: Execute Test Case: Kmart-Signin (6 steps)
[[18:50:29]] [SUCCESS] Screenshot refreshed
[[18:50:29]] [INFO] Refreshing screenshot...
[[18:50:26]] [SUCCESS] Screenshot refreshed successfully
[[18:50:26]] [SUCCESS] Screenshot refreshed successfully
[[18:50:26]] [INFO] Executing action 126/512: iOS Function: alert_accept
[[18:50:25]] [SUCCESS] Screenshot refreshed
[[18:50:25]] [INFO] Refreshing screenshot...
[[18:50:20]] [SUCCESS] Screenshot refreshed successfully
[[18:50:20]] [SUCCESS] Screenshot refreshed successfully
[[18:50:20]] [INFO] Executing action 125/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:50:19]] [SUCCESS] Screenshot refreshed
[[18:50:19]] [INFO] Refreshing screenshot...
[[18:50:16]] [SUCCESS] Screenshot refreshed successfully
[[18:50:16]] [SUCCESS] Screenshot refreshed successfully
[[18:50:15]] [INFO] Executing action 124/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:50:15]] [SUCCESS] Screenshot refreshed
[[18:50:15]] [INFO] Refreshing screenshot...
[[18:50:11]] [INFO] Executing action 123/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:50:11]] [SUCCESS] Screenshot refreshed successfully
[[18:50:11]] [SUCCESS] Screenshot refreshed successfully
[[18:50:11]] [SUCCESS] Screenshot refreshed
[[18:50:11]] [INFO] Refreshing screenshot...
[[18:50:04]] [SUCCESS] Screenshot refreshed successfully
[[18:50:04]] [SUCCESS] Screenshot refreshed successfully
[[18:50:04]] [INFO] Executing action 122/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:50:03]] [SUCCESS] Screenshot refreshed
[[18:50:03]] [INFO] Refreshing screenshot...
[[18:49:59]] [SUCCESS] Screenshot refreshed successfully
[[18:49:59]] [SUCCESS] Screenshot refreshed successfully
[[18:49:59]] [INFO] Executing action 121/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:49:59]] [SUCCESS] Screenshot refreshed
[[18:49:59]] [INFO] Refreshing screenshot...
[[18:49:58]] [SUCCESS] Screenshot refreshed successfully
[[18:49:58]] [SUCCESS] Screenshot refreshed successfully
[[18:49:57]] [INFO] Executing action 120/512: Add Log: Sign in from bag page is successfully (with screenshot)
[[18:49:57]] [SUCCESS] Screenshot refreshed
[[18:49:57]] [INFO] Refreshing screenshot...
[[18:49:53]] [SUCCESS] Screenshot refreshed successfully
[[18:49:53]] [SUCCESS] Screenshot refreshed successfully
[[18:49:52]] [INFO] Executing action 119/512: iOS Function: text
[[18:49:52]] [SUCCESS] Screenshot refreshed
[[18:49:52]] [INFO] Refreshing screenshot...
[[18:49:48]] [SUCCESS] Screenshot refreshed successfully
[[18:49:48]] [SUCCESS] Screenshot refreshed successfully
[[18:49:48]] [INFO] Executing action 118/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[18:49:47]] [SUCCESS] Screenshot refreshed
[[18:49:47]] [INFO] Refreshing screenshot...
[[18:49:43]] [SUCCESS] Screenshot refreshed successfully
[[18:49:43]] [SUCCESS] Screenshot refreshed successfully
[[18:49:43]] [INFO] Executing action 117/512: iOS Function: text
[[18:49:42]] [SUCCESS] Screenshot refreshed
[[18:49:42]] [INFO] Refreshing screenshot...
[[18:49:39]] [SUCCESS] Screenshot refreshed successfully
[[18:49:39]] [SUCCESS] Screenshot refreshed successfully
[[18:49:39]] [INFO] Executing action 116/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[18:49:38]] [SUCCESS] Screenshot refreshed
[[18:49:38]] [INFO] Refreshing screenshot...
[[18:49:36]] [SUCCESS] Screenshot refreshed successfully
[[18:49:36]] [SUCCESS] Screenshot refreshed successfully
[[18:49:35]] [INFO] Executing action 115/512: iOS Function: alert_accept
[[18:49:35]] [SUCCESS] Screenshot refreshed
[[18:49:35]] [INFO] Refreshing screenshot...
[[18:49:31]] [SUCCESS] Screenshot refreshed successfully
[[18:49:31]] [SUCCESS] Screenshot refreshed successfully
[[18:49:31]] [INFO] Executing action 114/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign In"]
[[18:49:30]] [SUCCESS] Screenshot refreshed
[[18:49:30]] [INFO] Refreshing screenshot...
[[18:49:20]] [SUCCESS] Screenshot refreshed successfully
[[18:49:20]] [SUCCESS] Screenshot refreshed successfully
[[18:49:19]] [INFO] Executing action 113/512: swipeTillVisible action
[[18:49:19]] [SUCCESS] Screenshot refreshed
[[18:49:19]] [INFO] Refreshing screenshot...
[[18:49:16]] [SUCCESS] Screenshot refreshed successfully
[[18:49:16]] [SUCCESS] Screenshot refreshed successfully
[[18:49:15]] [INFO] Executing action 112/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:49:15]] [SUCCESS] Screenshot refreshed
[[18:49:15]] [INFO] Refreshing screenshot...
[[18:49:11]] [SUCCESS] Screenshot refreshed successfully
[[18:49:11]] [SUCCESS] Screenshot refreshed successfully
[[18:49:10]] [INFO] Executing action 111/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:49:10]] [SUCCESS] Screenshot refreshed
[[18:49:10]] [INFO] Refreshing screenshot...
[[18:49:07]] [SUCCESS] Screenshot refreshed successfully
[[18:49:07]] [SUCCESS] Screenshot refreshed successfully
[[18:49:06]] [INFO] Executing action 110/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:49:06]] [SUCCESS] Screenshot refreshed
[[18:49:06]] [INFO] Refreshing screenshot...
[[18:49:02]] [SUCCESS] Screenshot refreshed successfully
[[18:49:02]] [SUCCESS] Screenshot refreshed successfully
[[18:49:02]] [INFO] Executing action 109/512: iOS Function: text
[[18:49:01]] [SUCCESS] Screenshot refreshed
[[18:49:01]] [INFO] Refreshing screenshot...
[[18:48:56]] [SUCCESS] Screenshot refreshed successfully
[[18:48:56]] [SUCCESS] Screenshot refreshed successfully
[[18:48:56]] [INFO] Executing action 108/512: Tap on Text: "Find"
[[18:48:55]] [SUCCESS] Screenshot refreshed
[[18:48:55]] [INFO] Refreshing screenshot...
[[18:48:50]] [SUCCESS] Screenshot refreshed successfully
[[18:48:50]] [SUCCESS] Screenshot refreshed successfully
[[18:48:50]] [INFO] Executing action 107/512: Restart app: env[appid]
[[18:48:49]] [SUCCESS] Screenshot refreshed
[[18:48:49]] [INFO] Refreshing screenshot...
[[18:48:46]] [SUCCESS] Screenshot refreshed successfully
[[18:48:46]] [SUCCESS] Screenshot refreshed successfully
[[18:48:46]] [INFO] Executing action 106/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:48:45]] [SUCCESS] Screenshot refreshed
[[18:48:45]] [INFO] Refreshing screenshot...
[[18:48:39]] [SUCCESS] Screenshot refreshed successfully
[[18:48:39]] [SUCCESS] Screenshot refreshed successfully
[[18:48:39]] [INFO] Executing action 105/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:48:38]] [SUCCESS] Screenshot refreshed
[[18:48:38]] [INFO] Refreshing screenshot...
[[18:48:35]] [SUCCESS] Screenshot refreshed successfully
[[18:48:35]] [SUCCESS] Screenshot refreshed successfully
[[18:48:35]] [INFO] Executing action 104/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:48:34]] [SUCCESS] Screenshot refreshed
[[18:48:34]] [INFO] Refreshing screenshot...
[[18:48:33]] [SUCCESS] Screenshot refreshed successfully
[[18:48:33]] [SUCCESS] Screenshot refreshed successfully
[[18:48:32]] [INFO] Executing action 103/512: Add Log: Sign in from wishlist page successfully (with screenshot)
[[18:48:31]] [SUCCESS] Screenshot refreshed
[[18:48:31]] [INFO] Refreshing screenshot...
[[18:48:28]] [SUCCESS] Screenshot refreshed successfully
[[18:48:28]] [SUCCESS] Screenshot refreshed successfully
[[18:48:28]] [INFO] Executing action 102/512: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[18:48:27]] [SUCCESS] Screenshot refreshed
[[18:48:27]] [INFO] Refreshing screenshot...
[[18:48:24]] [SUCCESS] Screenshot refreshed successfully
[[18:48:24]] [SUCCESS] Screenshot refreshed successfully
[[18:48:24]] [INFO] Executing action 101/512: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:48:23]] [SUCCESS] Screenshot refreshed
[[18:48:23]] [INFO] Refreshing screenshot...
[[18:48:19]] [SUCCESS] Screenshot refreshed successfully
[[18:48:19]] [SUCCESS] Screenshot refreshed successfully
[[18:48:19]] [INFO] Executing action 100/512: iOS Function: text
[[18:48:18]] [SUCCESS] Screenshot refreshed
[[18:48:18]] [INFO] Refreshing screenshot...
[[18:48:15]] [SUCCESS] Screenshot refreshed successfully
[[18:48:15]] [SUCCESS] Screenshot refreshed successfully
[[18:48:15]] [INFO] Executing action 99/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:48:14]] [SUCCESS] Screenshot refreshed
[[18:48:14]] [INFO] Refreshing screenshot...
[[18:48:10]] [SUCCESS] Screenshot refreshed successfully
[[18:48:10]] [SUCCESS] Screenshot refreshed successfully
[[18:48:10]] [INFO] Executing action 98/512: iOS Function: text
[[18:48:09]] [SUCCESS] Screenshot refreshed
[[18:48:09]] [INFO] Refreshing screenshot...
[[18:48:06]] [SUCCESS] Screenshot refreshed successfully
[[18:48:06]] [SUCCESS] Screenshot refreshed successfully
[[18:48:05]] [INFO] Executing action 97/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:48:05]] [SUCCESS] Screenshot refreshed
[[18:48:05]] [INFO] Refreshing screenshot...
[[18:48:02]] [SUCCESS] Screenshot refreshed successfully
[[18:48:02]] [SUCCESS] Screenshot refreshed successfully
[[18:48:02]] [INFO] Executing action 96/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:48:01]] [SUCCESS] Screenshot refreshed
[[18:48:01]] [INFO] Refreshing screenshot...
[[18:47:58]] [SUCCESS] Screenshot refreshed successfully
[[18:47:58]] [SUCCESS] Screenshot refreshed successfully
[[18:47:58]] [INFO] Executing action 95/512: iOS Function: alert_accept
[[18:47:58]] [SUCCESS] Screenshot refreshed
[[18:47:58]] [INFO] Refreshing screenshot...
[[18:47:54]] [SUCCESS] Screenshot refreshed successfully
[[18:47:54]] [SUCCESS] Screenshot refreshed successfully
[[18:47:54]] [INFO] Executing action 94/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[18:47:54]] [SUCCESS] Screenshot refreshed
[[18:47:54]] [INFO] Refreshing screenshot...
[[18:47:51]] [SUCCESS] Screenshot refreshed successfully
[[18:47:51]] [SUCCESS] Screenshot refreshed successfully
[[18:47:50]] [INFO] Executing action 93/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:47:50]] [SUCCESS] Screenshot refreshed
[[18:47:50]] [INFO] Refreshing screenshot...
[[18:47:46]] [SUCCESS] Screenshot refreshed successfully
[[18:47:46]] [SUCCESS] Screenshot refreshed successfully
[[18:47:46]] [INFO] Executing action 92/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:47:45]] [SUCCESS] Screenshot refreshed
[[18:47:45]] [INFO] Refreshing screenshot...
[[18:47:39]] [SUCCESS] Screenshot refreshed successfully
[[18:47:39]] [SUCCESS] Screenshot refreshed successfully
[[18:47:39]] [INFO] Executing action 91/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:47:39]] [SUCCESS] Screenshot refreshed
[[18:47:39]] [INFO] Refreshing screenshot...
[[18:47:36]] [SUCCESS] Screenshot refreshed successfully
[[18:47:36]] [SUCCESS] Screenshot refreshed successfully
[[18:47:35]] [INFO] Executing action 90/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:47:35]] [SUCCESS] Screenshot refreshed
[[18:47:35]] [INFO] Refreshing screenshot...
[[18:47:33]] [SUCCESS] Screenshot refreshed successfully
[[18:47:33]] [SUCCESS] Screenshot refreshed successfully
[[18:47:33]] [INFO] Executing action 89/512: Add Log: Sign in from home page sign in button successfully (with screenshot)
[[18:47:32]] [SUCCESS] Screenshot refreshed
[[18:47:32]] [INFO] Refreshing screenshot...
[[18:47:29]] [SUCCESS] Screenshot refreshed successfully
[[18:47:29]] [SUCCESS] Screenshot refreshed successfully
[[18:47:29]] [INFO] Executing action 88/512: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[18:47:28]] [SUCCESS] Screenshot refreshed
[[18:47:28]] [INFO] Refreshing screenshot...
[[18:47:23]] [SUCCESS] Screenshot refreshed successfully
[[18:47:23]] [SUCCESS] Screenshot refreshed successfully
[[18:47:23]] [INFO] Executing action 87/512: iOS Function: text
[[18:47:23]] [SUCCESS] Screenshot refreshed
[[18:47:23]] [INFO] Refreshing screenshot...
[[18:47:19]] [SUCCESS] Screenshot refreshed successfully
[[18:47:19]] [SUCCESS] Screenshot refreshed successfully
[[18:47:19]] [INFO] Executing action 86/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:47:19]] [SUCCESS] Screenshot refreshed
[[18:47:19]] [INFO] Refreshing screenshot...
[[18:47:14]] [SUCCESS] Screenshot refreshed successfully
[[18:47:14]] [SUCCESS] Screenshot refreshed successfully
[[18:47:14]] [INFO] Executing action 85/512: iOS Function: text
[[18:47:14]] [SUCCESS] Screenshot refreshed
[[18:47:14]] [INFO] Refreshing screenshot...
[[18:47:10]] [SUCCESS] Screenshot refreshed successfully
[[18:47:10]] [SUCCESS] Screenshot refreshed successfully
[[18:47:10]] [INFO] Executing action 84/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:47:09]] [SUCCESS] Screenshot refreshed
[[18:47:09]] [INFO] Refreshing screenshot...
[[18:47:06]] [SUCCESS] Screenshot refreshed successfully
[[18:47:06]] [SUCCESS] Screenshot refreshed successfully
[[18:47:06]] [INFO] Executing action 83/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:47:05]] [SUCCESS] Screenshot refreshed
[[18:47:05]] [INFO] Refreshing screenshot...
[[18:47:03]] [SUCCESS] Screenshot refreshed successfully
[[18:47:03]] [SUCCESS] Screenshot refreshed successfully
[[18:47:03]] [INFO] Executing action 82/512: iOS Function: alert_accept
[[18:47:02]] [SUCCESS] Screenshot refreshed
[[18:47:02]] [INFO] Refreshing screenshot...
[[18:46:56]] [SUCCESS] Screenshot refreshed successfully
[[18:46:56]] [SUCCESS] Screenshot refreshed successfully
[[18:46:56]] [INFO] Executing action 81/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:46:55]] [SUCCESS] Screenshot refreshed
[[18:46:55]] [INFO] Refreshing screenshot...
[[18:46:41]] [SUCCESS] Screenshot refreshed successfully
[[18:46:41]] [SUCCESS] Screenshot refreshed successfully
[[18:46:40]] [INFO] Executing action 80/512: Restart app: env[appid]
[[18:46:40]] [SUCCESS] Screenshot refreshed
[[18:46:40]] [INFO] Refreshing screenshot...
[[18:46:39]] [SUCCESS] Screenshot refreshed
[[18:46:39]] [INFO] Refreshing screenshot...
[[18:46:36]] [SUCCESS] Screenshot refreshed successfully
[[18:46:36]] [SUCCESS] Screenshot refreshed successfully
[[18:46:35]] [INFO] Executing Multi Step action step 34/34: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[18:46:35]] [SUCCESS] Screenshot refreshed
[[18:46:35]] [INFO] Refreshing screenshot...
[[18:46:31]] [INFO] Executing Multi Step action step 33/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:46:31]] [SUCCESS] Screenshot refreshed successfully
[[18:46:31]] [SUCCESS] Screenshot refreshed successfully
[[18:46:31]] [SUCCESS] Screenshot refreshed
[[18:46:31]] [INFO] Refreshing screenshot...
[[18:46:28]] [SUCCESS] Screenshot refreshed successfully
[[18:46:28]] [SUCCESS] Screenshot refreshed successfully
[[18:46:27]] [INFO] Executing Multi Step action step 32/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:46:27]] [SUCCESS] Screenshot refreshed
[[18:46:27]] [INFO] Refreshing screenshot...
[[18:46:22]] [SUCCESS] Screenshot refreshed successfully
[[18:46:22]] [SUCCESS] Screenshot refreshed successfully
[[18:46:22]] [INFO] Executing Multi Step action step 31/34: Tap on image: banner-close-updated.png
[[18:46:21]] [SUCCESS] Screenshot refreshed
[[18:46:21]] [INFO] Refreshing screenshot...
[[18:46:12]] [SUCCESS] Screenshot refreshed successfully
[[18:46:12]] [SUCCESS] Screenshot refreshed successfully
[[18:46:12]] [INFO] Executing Multi Step action step 30/34: Swipe from (50%, 70%) to (50%, 30%)
[[18:46:11]] [SUCCESS] Screenshot refreshed
[[18:46:11]] [INFO] Refreshing screenshot...
[[18:46:07]] [SUCCESS] Screenshot refreshed successfully
[[18:46:07]] [SUCCESS] Screenshot refreshed successfully
[[18:46:07]] [INFO] Executing Multi Step action step 29/34: Tap on image: env[delivery-address-img]
[[18:46:07]] [SUCCESS] Screenshot refreshed
[[18:46:07]] [INFO] Refreshing screenshot...
[[18:46:03]] [SUCCESS] Screenshot refreshed successfully
[[18:46:03]] [SUCCESS] Screenshot refreshed successfully
[[18:46:03]] [INFO] Executing Multi Step action step 28/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[18:46:02]] [SUCCESS] Screenshot refreshed
[[18:46:02]] [INFO] Refreshing screenshot...
[[18:45:56]] [SUCCESS] Screenshot refreshed successfully
[[18:45:56]] [SUCCESS] Screenshot refreshed successfully
[[18:45:55]] [INFO] Executing Multi Step action step 27/34: Tap and Type at (54, 314): "305 238 Flinders"
[[18:45:55]] [SUCCESS] Screenshot refreshed
[[18:45:55]] [INFO] Refreshing screenshot...
[[18:45:50]] [SUCCESS] Screenshot refreshed successfully
[[18:45:50]] [SUCCESS] Screenshot refreshed successfully
[[18:45:50]] [INFO] Executing Multi Step action step 26/34: Tap on Text: "address"
[[18:45:49]] [SUCCESS] Screenshot refreshed
[[18:45:49]] [INFO] Refreshing screenshot...
[[18:45:45]] [SUCCESS] Screenshot refreshed successfully
[[18:45:45]] [SUCCESS] Screenshot refreshed successfully
[[18:45:45]] [INFO] Executing Multi Step action step 25/34: iOS Function: text
[[18:45:45]] [SUCCESS] Screenshot refreshed
[[18:45:45]] [INFO] Refreshing screenshot...
[[18:45:41]] [SUCCESS] Screenshot refreshed successfully
[[18:45:41]] [SUCCESS] Screenshot refreshed successfully
[[18:45:41]] [INFO] Executing Multi Step action step 24/34: textClear action
[[18:45:40]] [SUCCESS] Screenshot refreshed
[[18:45:40]] [INFO] Refreshing screenshot...
[[18:45:37]] [SUCCESS] Screenshot refreshed successfully
[[18:45:37]] [SUCCESS] Screenshot refreshed successfully
[[18:45:37]] [INFO] Executing Multi Step action step 23/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[18:45:36]] [SUCCESS] Screenshot refreshed
[[18:45:36]] [INFO] Refreshing screenshot...
[[18:45:32]] [SUCCESS] Screenshot refreshed successfully
[[18:45:32]] [SUCCESS] Screenshot refreshed successfully
[[18:45:32]] [INFO] Executing Multi Step action step 22/34: textClear action
[[18:45:32]] [SUCCESS] Screenshot refreshed
[[18:45:32]] [INFO] Refreshing screenshot...
[[18:45:28]] [SUCCESS] Screenshot refreshed successfully
[[18:45:28]] [SUCCESS] Screenshot refreshed successfully
[[18:45:28]] [INFO] Executing Multi Step action step 21/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:45:28]] [SUCCESS] Screenshot refreshed
[[18:45:28]] [INFO] Refreshing screenshot...
[[18:45:23]] [SUCCESS] Screenshot refreshed successfully
[[18:45:23]] [SUCCESS] Screenshot refreshed successfully
[[18:45:23]] [INFO] Executing Multi Step action step 20/34: textClear action
[[18:45:23]] [SUCCESS] Screenshot refreshed
[[18:45:23]] [INFO] Refreshing screenshot...
[[18:45:19]] [SUCCESS] Screenshot refreshed successfully
[[18:45:19]] [SUCCESS] Screenshot refreshed successfully
[[18:45:19]] [INFO] Executing Multi Step action step 19/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[18:45:19]] [SUCCESS] Screenshot refreshed
[[18:45:19]] [INFO] Refreshing screenshot...
[[18:45:15]] [SUCCESS] Screenshot refreshed successfully
[[18:45:15]] [SUCCESS] Screenshot refreshed successfully
[[18:45:15]] [INFO] Executing Multi Step action step 18/34: textClear action
[[18:45:14]] [SUCCESS] Screenshot refreshed
[[18:45:14]] [INFO] Refreshing screenshot...
[[18:45:11]] [SUCCESS] Screenshot refreshed successfully
[[18:45:11]] [SUCCESS] Screenshot refreshed successfully
[[18:45:11]] [INFO] Executing Multi Step action step 17/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[18:45:10]] [SUCCESS] Screenshot refreshed
[[18:45:10]] [INFO] Refreshing screenshot...
[[18:45:07]] [SUCCESS] Screenshot refreshed successfully
[[18:45:07]] [SUCCESS] Screenshot refreshed successfully
[[18:45:07]] [INFO] Executing Multi Step action step 16/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[18:45:06]] [SUCCESS] Screenshot refreshed
[[18:45:06]] [INFO] Refreshing screenshot...
[[18:44:56]] [SUCCESS] Screenshot refreshed successfully
[[18:44:56]] [SUCCESS] Screenshot refreshed successfully
[[18:44:56]] [INFO] Executing Multi Step action step 15/34: swipeTillVisible action
[[18:44:56]] [SUCCESS] Screenshot refreshed
[[18:44:56]] [INFO] Refreshing screenshot...
[[18:44:52]] [SUCCESS] Screenshot refreshed successfully
[[18:44:52]] [SUCCESS] Screenshot refreshed successfully
[[18:44:52]] [INFO] Executing Multi Step action step 14/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:44:51]] [SUCCESS] Screenshot refreshed
[[18:44:51]] [INFO] Refreshing screenshot...
[[18:44:48]] [INFO] Executing Multi Step action step 13/34: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:44:48]] [SUCCESS] Screenshot refreshed successfully
[[18:44:48]] [SUCCESS] Screenshot refreshed successfully
[[18:44:48]] [SUCCESS] Screenshot refreshed
[[18:44:48]] [INFO] Refreshing screenshot...
[[18:44:45]] [SUCCESS] Screenshot refreshed successfully
[[18:44:45]] [SUCCESS] Screenshot refreshed successfully
[[18:44:44]] [INFO] Executing Multi Step action step 12/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:44:44]] [SUCCESS] Screenshot refreshed
[[18:44:44]] [INFO] Refreshing screenshot...
[[18:44:38]] [SUCCESS] Screenshot refreshed successfully
[[18:44:38]] [SUCCESS] Screenshot refreshed successfully
[[18:44:38]] [INFO] Executing Multi Step action step 11/34: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[18:44:37]] [SUCCESS] Screenshot refreshed
[[18:44:37]] [INFO] Refreshing screenshot...
[[18:44:34]] [SUCCESS] Screenshot refreshed successfully
[[18:44:34]] [SUCCESS] Screenshot refreshed successfully
[[18:44:34]] [INFO] Executing Multi Step action step 10/34: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:44:33]] [SUCCESS] Screenshot refreshed
[[18:44:33]] [INFO] Refreshing screenshot...
[[18:44:30]] [SUCCESS] Screenshot refreshed successfully
[[18:44:30]] [SUCCESS] Screenshot refreshed successfully
[[18:44:29]] [INFO] Executing Multi Step action step 9/34: iOS Function: text
[[18:44:29]] [SUCCESS] Screenshot refreshed
[[18:44:29]] [INFO] Refreshing screenshot...
[[18:44:24]] [SUCCESS] Screenshot refreshed successfully
[[18:44:24]] [SUCCESS] Screenshot refreshed successfully
[[18:44:23]] [INFO] Executing Multi Step action step 8/34: Tap on Text: "Find"
[[18:44:23]] [SUCCESS] Screenshot refreshed
[[18:44:23]] [INFO] Refreshing screenshot...
[[18:44:00]] [SUCCESS] Screenshot refreshed successfully
[[18:44:00]] [SUCCESS] Screenshot refreshed successfully
[[18:44:00]] [INFO] Executing Multi Step action step 7/34: If exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]" (timeout: 20s) → Then tap at (0, 0)
[[18:43:59]] [SUCCESS] Screenshot refreshed
[[18:43:59]] [INFO] Refreshing screenshot...
[[18:43:48]] [SUCCESS] Screenshot refreshed successfully
[[18:43:48]] [SUCCESS] Screenshot refreshed successfully
[[18:43:47]] [INFO] Executing Multi Step action step 6/34: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[18:43:46]] [SUCCESS] Screenshot refreshed
[[18:43:46]] [INFO] Refreshing screenshot...
[[18:43:19]] [SUCCESS] Screenshot refreshed successfully
[[18:43:19]] [SUCCESS] Screenshot refreshed successfully
[[18:43:19]] [INFO] Executing Multi Step action step 5/34: If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 25s) → Then click element: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[18:43:18]] [SUCCESS] Screenshot refreshed
[[18:43:18]] [INFO] Refreshing screenshot...
[[18:43:13]] [SUCCESS] Screenshot refreshed successfully
[[18:43:13]] [SUCCESS] Screenshot refreshed successfully
[[18:43:13]] [INFO] Executing Multi Step action step 4/34: Tap on Text: "Save"
[[18:43:13]] [SUCCESS] Screenshot refreshed
[[18:43:13]] [INFO] Refreshing screenshot...
[[18:43:08]] [SUCCESS] Screenshot refreshed successfully
[[18:43:08]] [SUCCESS] Screenshot refreshed successfully
[[18:43:08]] [INFO] Executing Multi Step action step 3/34: Tap on element with accessibility_id: btnCurrentLocationButton
[[18:43:07]] [SUCCESS] Screenshot refreshed
[[18:43:07]] [INFO] Refreshing screenshot...
[[18:43:04]] [SUCCESS] Screenshot refreshed successfully
[[18:43:04]] [SUCCESS] Screenshot refreshed successfully
[[18:43:03]] [INFO] Executing Multi Step action step 2/34: Wait till accessibility_id=btnCurrentLocationButton
[[18:43:03]] [SUCCESS] Screenshot refreshed
[[18:43:03]] [INFO] Refreshing screenshot...
[[18:42:56]] [SUCCESS] Screenshot refreshed successfully
[[18:42:56]] [SUCCESS] Screenshot refreshed successfully
[[18:42:56]] [INFO] Executing Multi Step action step 1/34: Tap on Text: "Edit"
[[18:42:56]] [INFO] Loaded 34 steps from test case: Delivery  Buy
[[18:42:56]] [INFO] Loading steps for Multi Step action: Delivery  Buy
[[18:42:56]] [INFO] Executing action 79/512: Execute Test Case: Delivery  Buy (34 steps)
[[18:42:55]] [SUCCESS] Screenshot refreshed
[[18:42:55]] [INFO] Refreshing screenshot...
[[18:42:52]] [SUCCESS] Screenshot refreshed successfully
[[18:42:52]] [SUCCESS] Screenshot refreshed successfully
[[18:42:51]] [INFO] Executing action 78/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:42:51]] [SUCCESS] Screenshot refreshed
[[18:42:51]] [INFO] Refreshing screenshot...
[[18:42:47]] [SUCCESS] Screenshot refreshed successfully
[[18:42:47]] [SUCCESS] Screenshot refreshed successfully
[[18:42:47]] [INFO] Executing action 77/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:42:46]] [SUCCESS] Screenshot refreshed
[[18:42:46]] [INFO] Refreshing screenshot...
[[18:42:40]] [SUCCESS] Screenshot refreshed successfully
[[18:42:40]] [SUCCESS] Screenshot refreshed successfully
[[18:42:40]] [INFO] Executing action 76/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:42:39]] [SUCCESS] Screenshot refreshed
[[18:42:39]] [INFO] Refreshing screenshot...
[[18:42:36]] [SUCCESS] Screenshot refreshed successfully
[[18:42:36]] [SUCCESS] Screenshot refreshed successfully
[[18:42:35]] [INFO] Executing action 75/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:42:35]] [SUCCESS] Screenshot refreshed
[[18:42:35]] [INFO] Refreshing screenshot...
[[18:42:31]] [SUCCESS] Screenshot refreshed successfully
[[18:42:31]] [SUCCESS] Screenshot refreshed successfully
[[18:42:31]] [INFO] Executing action 74/512: Tap on image: banner-close-updated.png
[[18:42:30]] [SUCCESS] Screenshot refreshed
[[18:42:30]] [INFO] Refreshing screenshot...
[[18:42:27]] [SUCCESS] Screenshot refreshed successfully
[[18:42:27]] [SUCCESS] Screenshot refreshed successfully
[[18:42:27]] [INFO] Executing action 73/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:42:26]] [SUCCESS] Screenshot refreshed
[[18:42:26]] [INFO] Refreshing screenshot...
[[18:42:22]] [SUCCESS] Screenshot refreshed successfully
[[18:42:22]] [SUCCESS] Screenshot refreshed successfully
[[18:42:22]] [INFO] Executing action 72/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:42:21]] [SUCCESS] Screenshot refreshed
[[18:42:21]] [INFO] Refreshing screenshot...
[[18:42:18]] [SUCCESS] Screenshot refreshed successfully
[[18:42:18]] [SUCCESS] Screenshot refreshed successfully
[[18:42:18]] [INFO] Executing action 71/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[18:42:17]] [SUCCESS] Screenshot refreshed
[[18:42:17]] [INFO] Refreshing screenshot...
[[18:42:14]] [SUCCESS] Screenshot refreshed successfully
[[18:42:14]] [SUCCESS] Screenshot refreshed successfully
[[18:42:14]] [INFO] Executing action 70/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:42:13]] [SUCCESS] Screenshot refreshed
[[18:42:13]] [INFO] Refreshing screenshot...
[[18:42:09]] [SUCCESS] Screenshot refreshed successfully
[[18:42:09]] [SUCCESS] Screenshot refreshed successfully
[[18:42:09]] [INFO] Executing action 69/512: Tap on image: banner-close-updated.png
[[18:42:08]] [SUCCESS] Screenshot refreshed
[[18:42:08]] [INFO] Refreshing screenshot...
[[18:41:57]] [SUCCESS] Screenshot refreshed successfully
[[18:41:57]] [SUCCESS] Screenshot refreshed successfully
[[18:41:57]] [INFO] Executing action 68/512: Wait for 10 ms
[[18:41:56]] [SUCCESS] Screenshot refreshed
[[18:41:56]] [INFO] Refreshing screenshot...
[[18:41:52]] [SUCCESS] Screenshot refreshed successfully
[[18:41:52]] [SUCCESS] Screenshot refreshed successfully
[[18:41:51]] [INFO] Executing action 67/512: Tap on Text: "Brunswick"
[[18:41:51]] [SUCCESS] Screenshot refreshed
[[18:41:51]] [INFO] Refreshing screenshot...
[[18:41:47]] [SUCCESS] Screenshot refreshed successfully
[[18:41:47]] [SUCCESS] Screenshot refreshed successfully
[[18:41:47]] [INFO] Executing action 66/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:41:46]] [SUCCESS] Screenshot refreshed
[[18:41:46]] [INFO] Refreshing screenshot...
[[18:41:43]] [SUCCESS] Screenshot refreshed successfully
[[18:41:43]] [SUCCESS] Screenshot refreshed successfully
[[18:41:43]] [INFO] Executing action 65/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[18:41:42]] [SUCCESS] Screenshot refreshed
[[18:41:42]] [INFO] Refreshing screenshot...
[[18:41:39]] [INFO] Executing action 64/512: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[18:41:39]] [SUCCESS] Screenshot refreshed successfully
[[18:41:39]] [SUCCESS] Screenshot refreshed successfully
[[18:41:39]] [SUCCESS] Screenshot refreshed
[[18:41:39]] [INFO] Refreshing screenshot...
[[18:41:36]] [SUCCESS] Screenshot refreshed successfully
[[18:41:36]] [SUCCESS] Screenshot refreshed successfully
[[18:41:35]] [INFO] Executing action 63/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:41:35]] [SUCCESS] Screenshot refreshed
[[18:41:35]] [INFO] Refreshing screenshot...
[[18:41:32]] [SUCCESS] Screenshot refreshed successfully
[[18:41:32]] [SUCCESS] Screenshot refreshed successfully
[[18:41:31]] [INFO] Executing action 62/512: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[18:41:31]] [SUCCESS] Screenshot refreshed
[[18:41:31]] [INFO] Refreshing screenshot...
[[18:41:25]] [SUCCESS] Screenshot refreshed successfully
[[18:41:25]] [SUCCESS] Screenshot refreshed successfully
[[18:41:25]] [INFO] Executing action 61/512: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[18:41:24]] [SUCCESS] Screenshot refreshed
[[18:41:24]] [INFO] Refreshing screenshot...
[[18:41:21]] [SUCCESS] Screenshot refreshed successfully
[[18:41:21]] [SUCCESS] Screenshot refreshed successfully
[[18:41:21]] [INFO] Executing action 60/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:41:20]] [SUCCESS] Screenshot refreshed
[[18:41:20]] [INFO] Refreshing screenshot...
[[18:41:16]] [SUCCESS] Screenshot refreshed successfully
[[18:41:16]] [SUCCESS] Screenshot refreshed successfully
[[18:41:16]] [INFO] Executing action 59/512: iOS Function: text
[[18:41:16]] [SUCCESS] Screenshot refreshed
[[18:41:16]] [INFO] Refreshing screenshot...
[[18:41:11]] [SUCCESS] Screenshot refreshed successfully
[[18:41:11]] [SUCCESS] Screenshot refreshed successfully
[[18:41:10]] [INFO] Executing action 58/512: Tap on Text: "Find"
[[18:41:10]] [SUCCESS] Screenshot refreshed
[[18:41:10]] [INFO] Refreshing screenshot...
[[18:41:09]] [SUCCESS] Screenshot refreshed
[[18:41:09]] [INFO] Refreshing screenshot...
[[18:41:07]] [SUCCESS] Screenshot refreshed successfully
[[18:41:07]] [SUCCESS] Screenshot refreshed successfully
[[18:41:06]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:41:06]] [SUCCESS] Screenshot refreshed
[[18:41:06]] [INFO] Refreshing screenshot...
[[18:41:01]] [SUCCESS] Screenshot refreshed successfully
[[18:41:01]] [SUCCESS] Screenshot refreshed successfully
[[18:41:01]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[18:41:00]] [SUCCESS] Screenshot refreshed
[[18:41:00]] [INFO] Refreshing screenshot...
[[18:40:57]] [SUCCESS] Screenshot refreshed successfully
[[18:40:57]] [SUCCESS] Screenshot refreshed successfully
[[18:40:57]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:40:56]] [SUCCESS] Screenshot refreshed
[[18:40:56]] [INFO] Refreshing screenshot...
[[18:40:52]] [SUCCESS] Screenshot refreshed successfully
[[18:40:52]] [SUCCESS] Screenshot refreshed successfully
[[18:40:52]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[18:40:51]] [SUCCESS] Screenshot refreshed
[[18:40:51]] [INFO] Refreshing screenshot...
[[18:40:47]] [SUCCESS] Screenshot refreshed successfully
[[18:40:47]] [SUCCESS] Screenshot refreshed successfully
[[18:40:47]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:40:47]] [SUCCESS] Screenshot refreshed
[[18:40:47]] [INFO] Refreshing screenshot...
[[18:40:42]] [SUCCESS] Screenshot refreshed successfully
[[18:40:42]] [SUCCESS] Screenshot refreshed successfully
[[18:40:41]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:40:41]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[18:40:41]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[18:40:41]] [INFO] Executing action 57/512: Execute Test Case: Kmart-Signin (8 steps)
[[18:40:41]] [SUCCESS] Screenshot refreshed
[[18:40:41]] [INFO] Refreshing screenshot...
[[18:40:38]] [SUCCESS] Screenshot refreshed successfully
[[18:40:38]] [SUCCESS] Screenshot refreshed successfully
[[18:40:38]] [INFO] Executing action 56/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:40:37]] [SUCCESS] Screenshot refreshed
[[18:40:37]] [INFO] Refreshing screenshot...
[[18:40:35]] [SUCCESS] Screenshot refreshed successfully
[[18:40:35]] [SUCCESS] Screenshot refreshed successfully
[[18:40:34]] [INFO] Executing action 55/512: iOS Function: alert_accept
[[18:40:34]] [SUCCESS] Screenshot refreshed
[[18:40:34]] [INFO] Refreshing screenshot...
[[18:40:28]] [SUCCESS] Screenshot refreshed successfully
[[18:40:28]] [SUCCESS] Screenshot refreshed successfully
[[18:40:27]] [INFO] Executing action 54/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:40:27]] [SUCCESS] Screenshot refreshed
[[18:40:27]] [INFO] Refreshing screenshot...
[[18:40:13]] [SUCCESS] Screenshot refreshed successfully
[[18:40:13]] [SUCCESS] Screenshot refreshed successfully
[[18:40:13]] [INFO] Executing action 53/512: Restart app: env[appid]
[[18:40:12]] [SUCCESS] Screenshot refreshed
[[18:40:12]] [INFO] Refreshing screenshot...
[[18:40:09]] [SUCCESS] Screenshot refreshed successfully
[[18:40:09]] [SUCCESS] Screenshot refreshed successfully
[[18:40:09]] [INFO] Executing action 52/512: Terminate app: env[appid]
[[18:40:08]] [SUCCESS] Screenshot refreshed
[[18:40:08]] [INFO] Refreshing screenshot...
[[18:40:05]] [SUCCESS] Screenshot refreshed successfully
[[18:40:05]] [SUCCESS] Screenshot refreshed successfully
[[18:40:05]] [INFO] Executing action 51/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:40:04]] [SUCCESS] Screenshot refreshed
[[18:40:04]] [INFO] Refreshing screenshot...
[[18:40:01]] [SUCCESS] Screenshot refreshed successfully
[[18:40:01]] [SUCCESS] Screenshot refreshed successfully
[[18:40:01]] [INFO] Executing action 50/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:40:00]] [SUCCESS] Screenshot refreshed
[[18:40:00]] [INFO] Refreshing screenshot...
[[18:39:54]] [INFO] Executing action 49/512: swipeTillVisible action
[[18:39:54]] [SUCCESS] Screenshot refreshed successfully
[[18:39:54]] [SUCCESS] Screenshot refreshed successfully
[[18:39:53]] [SUCCESS] Screenshot refreshed
[[18:39:53]] [INFO] Refreshing screenshot...
[[18:39:49]] [SUCCESS] Screenshot refreshed successfully
[[18:39:49]] [SUCCESS] Screenshot refreshed successfully
[[18:39:49]] [INFO] Executing action 48/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:39:49]] [SUCCESS] Screenshot refreshed
[[18:39:49]] [INFO] Refreshing screenshot...
[[18:39:42]] [SUCCESS] Screenshot refreshed successfully
[[18:39:42]] [SUCCESS] Screenshot refreshed successfully
[[18:39:42]] [INFO] Executing action 47/512: Tap on element with accessibility_id: Add to bag
[[18:39:41]] [SUCCESS] Screenshot refreshed
[[18:39:41]] [INFO] Refreshing screenshot...
[[18:39:38]] [SUCCESS] Screenshot refreshed successfully
[[18:39:38]] [SUCCESS] Screenshot refreshed successfully
[[18:39:38]] [INFO] Executing action 46/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:39:37]] [SUCCESS] Screenshot refreshed
[[18:39:37]] [INFO] Refreshing screenshot...
[[18:39:33]] [SUCCESS] Screenshot refreshed successfully
[[18:39:33]] [SUCCESS] Screenshot refreshed successfully
[[18:39:33]] [INFO] Executing action 45/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[18:39:33]] [SUCCESS] Screenshot refreshed
[[18:39:33]] [INFO] Refreshing screenshot...
[[18:39:29]] [SUCCESS] Screenshot refreshed successfully
[[18:39:29]] [SUCCESS] Screenshot refreshed successfully
[[18:39:29]] [INFO] Executing action 44/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:39:28]] [SUCCESS] Screenshot refreshed
[[18:39:28]] [INFO] Refreshing screenshot...
[[18:39:25]] [SUCCESS] Screenshot refreshed successfully
[[18:39:25]] [SUCCESS] Screenshot refreshed successfully
[[18:39:24]] [INFO] Executing action 43/512: iOS Function: text
[[18:39:24]] [SUCCESS] Screenshot refreshed
[[18:39:24]] [INFO] Refreshing screenshot...
[[18:39:20]] [SUCCESS] Screenshot refreshed successfully
[[18:39:20]] [SUCCESS] Screenshot refreshed successfully
[[18:39:20]] [INFO] Executing action 42/512: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[18:39:20]] [SUCCESS] Screenshot refreshed
[[18:39:20]] [INFO] Refreshing screenshot...
[[18:39:16]] [SUCCESS] Screenshot refreshed successfully
[[18:39:16]] [SUCCESS] Screenshot refreshed successfully
[[18:39:16]] [INFO] Executing action 41/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[18:39:16]] [SUCCESS] Screenshot refreshed
[[18:39:16]] [INFO] Refreshing screenshot...
[[18:39:12]] [SUCCESS] Screenshot refreshed successfully
[[18:39:12]] [SUCCESS] Screenshot refreshed successfully
[[18:39:11]] [INFO] Executing action 40/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[18:39:11]] [SUCCESS] Screenshot refreshed
[[18:39:11]] [INFO] Refreshing screenshot...
[[18:39:08]] [SUCCESS] Screenshot refreshed successfully
[[18:39:08]] [SUCCESS] Screenshot refreshed successfully
[[18:39:07]] [INFO] Executing action 39/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:39:07]] [SUCCESS] Screenshot refreshed
[[18:39:07]] [INFO] Refreshing screenshot...
[[18:39:03]] [SUCCESS] Screenshot refreshed successfully
[[18:39:03]] [SUCCESS] Screenshot refreshed successfully
[[18:39:03]] [INFO] Executing action 38/512: iOS Function: text
[[18:39:02]] [SUCCESS] Screenshot refreshed
[[18:39:02]] [INFO] Refreshing screenshot...
[[18:38:59]] [SUCCESS] Screenshot refreshed successfully
[[18:38:59]] [SUCCESS] Screenshot refreshed successfully
[[18:38:59]] [INFO] Executing action 37/512: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[18:38:58]] [SUCCESS] Screenshot refreshed
[[18:38:58]] [INFO] Refreshing screenshot...
[[18:38:56]] [SUCCESS] Screenshot refreshed successfully
[[18:38:56]] [SUCCESS] Screenshot refreshed successfully
[[18:38:55]] [INFO] Executing action 36/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[18:38:55]] [SUCCESS] Screenshot refreshed
[[18:38:55]] [INFO] Refreshing screenshot...
[[18:38:50]] [SUCCESS] Screenshot refreshed successfully
[[18:38:50]] [SUCCESS] Screenshot refreshed successfully
[[18:38:49]] [INFO] Executing action 35/512: Restart app: env[appid]
[[18:38:49]] [SUCCESS] Screenshot refreshed
[[18:38:49]] [INFO] Refreshing screenshot...
[[18:38:45]] [SUCCESS] Screenshot refreshed successfully
[[18:38:45]] [SUCCESS] Screenshot refreshed successfully
[[18:38:45]] [INFO] Executing action 34/512: Tap on image: env[device-back-img]
[[18:38:45]] [SUCCESS] Screenshot refreshed
[[18:38:45]] [INFO] Refreshing screenshot...
[[18:38:42]] [SUCCESS] Screenshot refreshed successfully
[[18:38:42]] [SUCCESS] Screenshot refreshed successfully
[[18:38:42]] [INFO] Executing action 33/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[18:38:41]] [SUCCESS] Screenshot refreshed
[[18:38:41]] [INFO] Refreshing screenshot...
[[18:38:38]] [SUCCESS] Screenshot refreshed successfully
[[18:38:38]] [SUCCESS] Screenshot refreshed successfully
[[18:38:37]] [INFO] Executing action 32/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[18:38:37]] [SUCCESS] Screenshot refreshed
[[18:38:37]] [INFO] Refreshing screenshot...
[[18:38:33]] [SUCCESS] Screenshot refreshed successfully
[[18:38:33]] [SUCCESS] Screenshot refreshed successfully
[[18:38:33]] [INFO] Executing action 31/512: Tap on image: env[paypal-close-img]
[[18:38:32]] [SUCCESS] Screenshot refreshed
[[18:38:32]] [INFO] Refreshing screenshot...
[[18:38:27]] [SUCCESS] Screenshot refreshed successfully
[[18:38:27]] [SUCCESS] Screenshot refreshed successfully
[[18:38:27]] [INFO] Executing action 30/512: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[18:38:26]] [SUCCESS] Screenshot refreshed
[[18:38:26]] [INFO] Refreshing screenshot...
[[18:38:22]] [SUCCESS] Screenshot refreshed successfully
[[18:38:22]] [SUCCESS] Screenshot refreshed successfully
[[18:38:22]] [INFO] Executing action 29/512: Tap on image: env[device-back-img]
[[18:38:22]] [SUCCESS] Screenshot refreshed
[[18:38:22]] [INFO] Refreshing screenshot...
[[18:38:19]] [SUCCESS] Screenshot refreshed successfully
[[18:38:19]] [SUCCESS] Screenshot refreshed successfully
[[18:38:19]] [INFO] Executing action 28/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[18:38:18]] [SUCCESS] Screenshot refreshed
[[18:38:18]] [INFO] Refreshing screenshot...
[[18:38:13]] [SUCCESS] Screenshot refreshed successfully
[[18:38:13]] [SUCCESS] Screenshot refreshed successfully
[[18:38:12]] [INFO] Executing action 27/512: Tap on element with accessibility_id: Learn more about Zip
[[18:38:12]] [SUCCESS] Screenshot refreshed
[[18:38:12]] [INFO] Refreshing screenshot...
[[18:38:08]] [SUCCESS] Screenshot refreshed successfully
[[18:38:08]] [SUCCESS] Screenshot refreshed successfully
[[18:38:08]] [INFO] Executing action 26/512: Tap on image: env[device-back-img]
[[18:38:08]] [SUCCESS] Screenshot refreshed
[[18:38:08]] [INFO] Refreshing screenshot...
[[18:38:05]] [SUCCESS] Screenshot refreshed successfully
[[18:38:05]] [SUCCESS] Screenshot refreshed successfully
[[18:38:05]] [INFO] Executing action 25/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[18:38:04]] [SUCCESS] Screenshot refreshed
[[18:38:04]] [INFO] Refreshing screenshot...
[[18:37:58]] [SUCCESS] Screenshot refreshed successfully
[[18:37:58]] [SUCCESS] Screenshot refreshed successfully
[[18:37:58]] [INFO] Executing action 24/512: Tap on element with accessibility_id: Learn more about AfterPay
[[18:37:58]] [SUCCESS] Screenshot refreshed
[[18:37:58]] [INFO] Refreshing screenshot...
[[18:37:51]] [SUCCESS] Screenshot refreshed successfully
[[18:37:51]] [SUCCESS] Screenshot refreshed successfully
[[18:37:51]] [INFO] Executing action 23/512: swipeTillVisible action
[[18:37:50]] [SUCCESS] Screenshot refreshed
[[18:37:50]] [INFO] Refreshing screenshot...
[[18:37:46]] [SUCCESS] Screenshot refreshed successfully
[[18:37:46]] [SUCCESS] Screenshot refreshed successfully
[[18:37:46]] [INFO] Executing action 22/512: Tap on image: env[closebtnimage]
[[18:37:45]] [SUCCESS] Screenshot refreshed
[[18:37:45]] [INFO] Refreshing screenshot...
[[18:37:42]] [SUCCESS] Screenshot refreshed successfully
[[18:37:42]] [SUCCESS] Screenshot refreshed successfully
[[18:37:41]] [INFO] Executing action 21/512: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[18:37:41]] [SUCCESS] Screenshot refreshed
[[18:37:41]] [INFO] Refreshing screenshot...
[[18:37:37]] [SUCCESS] Screenshot refreshed successfully
[[18:37:37]] [SUCCESS] Screenshot refreshed successfully
[[18:37:36]] [INFO] Executing action 20/512: Tap on image: env[product-share-img]
[[18:37:36]] [SUCCESS] Screenshot refreshed
[[18:37:36]] [INFO] Refreshing screenshot...
[[18:37:33]] [SUCCESS] Screenshot refreshed successfully
[[18:37:33]] [SUCCESS] Screenshot refreshed successfully
[[18:37:32]] [INFO] Executing action 19/512: Tap on element with xpath:  (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* with fallback: Coordinates (98, 308)
[[18:37:32]] [SUCCESS] Screenshot refreshed
[[18:37:32]] [INFO] Refreshing screenshot...
[[18:37:29]] [SUCCESS] Screenshot refreshed successfully
[[18:37:29]] [SUCCESS] Screenshot refreshed successfully
[[18:37:28]] [INFO] Executing action 18/512: Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*
[[18:37:27]] [SUCCESS] Screenshot refreshed
[[18:37:27]] [INFO] Refreshing screenshot...
[[18:37:16]] [SUCCESS] Screenshot refreshed successfully
[[18:37:16]] [SUCCESS] Screenshot refreshed successfully
[[18:37:15]] [INFO] Executing action 17/512: Wait for 10 ms
[[18:37:15]] [SUCCESS] Screenshot refreshed
[[18:37:15]] [INFO] Refreshing screenshot...
[[18:37:11]] [SUCCESS] Screenshot refreshed successfully
[[18:37:11]] [SUCCESS] Screenshot refreshed successfully
[[18:37:10]] [INFO] Executing action 16/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:37:10]] [SUCCESS] Screenshot refreshed
[[18:37:10]] [INFO] Refreshing screenshot...
[[18:37:06]] [SUCCESS] Screenshot refreshed successfully
[[18:37:06]] [SUCCESS] Screenshot refreshed successfully
[[18:37:06]] [INFO] Executing action 15/512: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[18:37:05]] [SUCCESS] Screenshot refreshed
[[18:37:05]] [INFO] Refreshing screenshot...
[[18:37:03]] [SUCCESS] Screenshot refreshed successfully
[[18:37:03]] [SUCCESS] Screenshot refreshed successfully
[[18:37:02]] [INFO] Executing action 14/512: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[18:37:02]] [SUCCESS] Screenshot refreshed
[[18:37:02]] [INFO] Refreshing screenshot...
[[18:36:58]] [SUCCESS] Screenshot refreshed successfully
[[18:36:58]] [SUCCESS] Screenshot refreshed successfully
[[18:36:58]] [INFO] Executing action 13/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show")]
[[18:36:58]] [SUCCESS] Screenshot refreshed
[[18:36:58]] [INFO] Refreshing screenshot...
[[18:36:54]] [SUCCESS] Screenshot refreshed successfully
[[18:36:54]] [SUCCESS] Screenshot refreshed successfully
[[18:36:54]] [INFO] Executing action 12/512: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[18:36:54]] [SUCCESS] Screenshot refreshed
[[18:36:54]] [INFO] Refreshing screenshot...
[[18:36:50]] [SUCCESS] Screenshot refreshed successfully
[[18:36:50]] [SUCCESS] Screenshot refreshed successfully
[[18:36:50]] [INFO] Executing action 11/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[18:36:49]] [SUCCESS] Screenshot refreshed
[[18:36:49]] [INFO] Refreshing screenshot...
[[18:36:46]] [SUCCESS] Screenshot refreshed successfully
[[18:36:46]] [SUCCESS] Screenshot refreshed successfully
[[18:36:46]] [INFO] Executing action 10/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:36:45]] [SUCCESS] Screenshot refreshed
[[18:36:45]] [INFO] Refreshing screenshot...
[[18:36:41]] [SUCCESS] Screenshot refreshed successfully
[[18:36:41]] [SUCCESS] Screenshot refreshed successfully
[[18:36:41]] [INFO] Executing action 9/512: Tap on Text: "Latest"
[[18:36:41]] [SUCCESS] Screenshot refreshed
[[18:36:41]] [INFO] Refreshing screenshot...
[[18:36:36]] [SUCCESS] Screenshot refreshed successfully
[[18:36:36]] [SUCCESS] Screenshot refreshed successfully
[[18:36:36]] [INFO] Executing action 8/512: Tap on Text: "Toys"
[[18:36:36]] [SUCCESS] Screenshot refreshed
[[18:36:36]] [INFO] Refreshing screenshot...
[[18:36:32]] [SUCCESS] Screenshot refreshed successfully
[[18:36:32]] [SUCCESS] Screenshot refreshed successfully
[[18:36:32]] [INFO] Executing action 7/512: Tap on image: env[device-back-img]
[[18:36:31]] [SUCCESS] Screenshot refreshed
[[18:36:31]] [INFO] Refreshing screenshot...
[[18:36:29]] [SUCCESS] Screenshot refreshed successfully
[[18:36:29]] [SUCCESS] Screenshot refreshed successfully
[[18:36:28]] [INFO] Executing action 6/512: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[18:36:28]] [SUCCESS] Screenshot refreshed
[[18:36:28]] [INFO] Refreshing screenshot...
[[18:36:25]] [SUCCESS] Screenshot refreshed successfully
[[18:36:25]] [SUCCESS] Screenshot refreshed successfully
[[18:36:25]] [INFO] Executing action 5/512: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[18:36:24]] [SUCCESS] Screenshot refreshed
[[18:36:24]] [INFO] Refreshing screenshot...
[[18:36:21]] [SUCCESS] Screenshot refreshed successfully
[[18:36:21]] [SUCCESS] Screenshot refreshed successfully
[[18:36:21]] [INFO] Executing action 4/512: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[18:36:20]] [SUCCESS] Screenshot refreshed
[[18:36:20]] [INFO] Refreshing screenshot...
[[18:36:17]] [SUCCESS] Screenshot refreshed successfully
[[18:36:17]] [SUCCESS] Screenshot refreshed successfully
[[18:36:17]] [INFO] Executing action 3/512: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[18:36:17]] [SUCCESS] Screenshot refreshed
[[18:36:17]] [INFO] Refreshing screenshot...
[[18:36:14]] [SUCCESS] Screenshot refreshed successfully
[[18:36:14]] [SUCCESS] Screenshot refreshed successfully
[[18:36:13]] [INFO] Executing action 2/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[18:36:13]] [SUCCESS] Screenshot refreshed
[[18:36:13]] [INFO] Refreshing screenshot...
[[18:36:08]] [INFO] Executing action 1/512: Restart app: env[appid]
[[18:36:08]] [INFO] ExecutionManager: Starting execution of 512 actions...
[[18:36:08]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[18:36:08]] [INFO] Clearing screenshots from database before execution...
[[18:36:08]] [SUCCESS] All screenshots deleted successfully
[[18:36:08]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[18:36:08]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_183608/screenshots
[[18:36:08]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_183608
[[18:36:08]] [SUCCESS] Report directory initialized successfully
[[18:36:08]] [INFO] Initializing report directory and screenshots folder...
[[18:36:06]] [SUCCESS] All screenshots deleted successfully
[[18:36:06]] [INFO] All actions cleared
[[18:36:06]] [INFO] Cleaning up screenshots...
[[18:36:06]] [SUCCESS] Screenshot refreshed successfully
[[18:36:05]] [SUCCESS] Screenshot refreshed
[[18:36:05]] [INFO] Refreshing screenshot...
[[18:36:04]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[18:36:04]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[18:35:58]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[18:35:56]] [SUCCESS] Found 1 device(s)
[[18:35:55]] [INFO] Refreshing device list...
