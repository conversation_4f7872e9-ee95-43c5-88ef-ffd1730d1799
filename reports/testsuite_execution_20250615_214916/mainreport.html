<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/15/2025, 9:51:49 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">2</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 15/06/2025, 21:51:49
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">1334ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="Successful.png" data-action-id="Successful" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Launched App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: Successful">Successful</span>
                            </div>
                            <span class="test-step-duration">564ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1216ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Edit link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">431ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1629ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Done link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">554ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="vjBGuN5y9x.png" data-action-id="vjBGuN5y9x" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 1 ms <span class="action-id-badge" title="Action ID: vjBGuN5y9x">vjBGuN5y9x</span>
                            </div>
                            <span class="test-step-duration">1002ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1049ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">1106ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #2 apple health (Copy)
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            7 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">1165ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">882ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1668ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="vjBGuN5y9x.png" data-action-id="vjBGuN5y9x" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: vjBGuN5y9x">vjBGuN5y9x</span>
                            </div>
                            <span class="test-step-duration">1044ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="Successful.png" data-action-id="Successful" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Closed App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: Successful">Successful</span>
                            </div>
                            <span class="test-step-duration">1107ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="failed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Execute Test Case: health2 (9 steps) <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery) <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #3 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-2-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">49ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2672ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1575ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1229ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="successful.png" data-action-id="successful" onclick="showStepDetails('step-2-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Clicked on Edit link successfully (with screenshot) <span class="action-id-badge" title="Action ID: successful">successful</span>
                            </div>
                            <span class="test-step-duration">426ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-2-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1592ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="vjBGuN5y9x.png" data-action-id="vjBGuN5y9x" onclick="showStepDetails('step-2-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: vjBGuN5y9x">vjBGuN5y9x</span>
                            </div>
                            <span class="test-step-duration">1054ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-2-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: apple health (8 steps) <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="screenshot.png" data-action-id="screenshot" onclick="showStepDetails('step-2-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: screenshot">screenshot</span>
                            </div>
                            <span class="test-step-duration">8ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 15/06/2025, 21:51:49","testCases":[{"name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1334ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Add Log: Launched App Successfully (with screenshot)","status":"passed","duration":"564ms","action_id":"Successful","screenshot_filename":"Successful.png","report_screenshot":"Successful.png","resolved_screenshot":"screenshots/Successful.png","action_id_screenshot":"screenshots/Successful.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1216ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Edit link is clicked (with screenshot)","status":"passed","duration":"431ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1629ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Done link is clicked (with screenshot)","status":"passed","duration":"554ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"},{"name":"Wait for 1 ms","status":"passed","duration":"1002ms","action_id":"vjBGuN5y9x","screenshot_filename":"vjBGuN5y9x.png","report_screenshot":"vjBGuN5y9x.png","resolved_screenshot":"screenshots/vjBGuN5y9x.png","clean_action_id":"vjBGuN5y9x","prefixed_action_id":"al_vjBGuN5y9x","action_id_screenshot":"screenshots/vjBGuN5y9x.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1049ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","clean_action_id":"4kBvNvFi5i","prefixed_action_id":"al_4kBvNvFi5i","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Add Log: App is closed (with screenshot)","status":"passed","duration":"1106ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"}]},{"name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1165ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"882ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1668ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1044ms","action_id":"vjBGuN5y9x","screenshot_filename":"vjBGuN5y9x.png","report_screenshot":"vjBGuN5y9x.png","resolved_screenshot":"screenshots/vjBGuN5y9x.png","clean_action_id":"vjBGuN5y9x","prefixed_action_id":"al_vjBGuN5y9x","action_id_screenshot":"screenshots/vjBGuN5y9x.png"},{"name":"Add Log: Closed App Successfully (with screenshot)","status":"passed","duration":"1107ms","action_id":"Successful","screenshot_filename":"Successful.png","report_screenshot":"Successful.png","resolved_screenshot":"screenshots/Successful.png","action_id_screenshot":"screenshots/Successful.png"},{"name":"Execute Test Case: health2 (9 steps)","status":"failed","duration":"0ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","clean_action_id":"4kBvNvFi5i","prefixed_action_id":"al_4kBvNvFi5i","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)","status":"unknown","duration":"0ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"}]},{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"49ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"2672ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1575ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1229ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Add Log: Clicked on Edit link successfully (with screenshot)","status":"passed","duration":"426ms","action_id":"successful","screenshot_filename":"successful.png","report_screenshot":"successful.png","resolved_screenshot":"screenshots/successful.png","action_id_screenshot":"screenshots/successful.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1592ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1054ms","action_id":"vjBGuN5y9x","screenshot_filename":"vjBGuN5y9x.png","report_screenshot":"vjBGuN5y9x.png","resolved_screenshot":"screenshots/vjBGuN5y9x.png","clean_action_id":"vjBGuN5y9x","prefixed_action_id":"al_vjBGuN5y9x","action_id_screenshot":"screenshots/vjBGuN5y9x.png"},{"name":"Execute Test Case: apple health (8 steps)","status":"passed","duration":"0ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","clean_action_id":"4kBvNvFi5i","prefixed_action_id":"al_4kBvNvFi5i","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Add Log: App is closed (with screenshot)","status":"passed","duration":"8ms","action_id":"screenshot","screenshot_filename":"screenshot.png","report_screenshot":"screenshot.png","resolved_screenshot":"screenshots/screenshot.png","action_id_screenshot":"screenshots/screenshot.png"}]}],"passed":2,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["4kBvNvFi5i.png","7MOUNxtPJz.png","AoLct5ZYWj.png","E5An5BbVuK.png","HphRLWPfSD.png","KfOSdvcOkk.png","KzjZOcsLuC.png","OqYf9xF3oX.png","SaJtvXOGlT.png","To6rgFtm9R.png","UppP3ZuqY6.png","ee5KkVz90e.png","f5C7GOVKXJ.png","jE4eZaRFK6.png","jF4jRny1iE.png","mOoxO3pBlm.png","mmT4QEfEZD.png","muJaxWLMoU.png","oIAtyQB5wY.png","rxDTLvtHmR.png","vjBGuN5y9x.png","wp1dY1wJ58.png","yvWe991wY2.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>