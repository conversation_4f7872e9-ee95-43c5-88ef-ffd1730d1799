Action Log - 2025-06-15 21:51:49
================================================================================

[[21:51:49]] [INFO] Generating execution report...
[[21:51:49]] [WARNING] 1 test failed.
[[21:51:48]] [SUCCESS] Screenshot refreshed
[[21:51:48]] [INFO] Refreshing screenshot...
[[21:51:47]] [SUCCESS] Screenshot refreshed successfully
[[21:51:47]] [SUCCESS] Screenshot refreshed successfully
[[21:51:46]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[21:51:46]] [SUCCESS] Screenshot refreshed
[[21:51:46]] [INFO] Refreshing screenshot...
[[21:51:46]] [SUCCESS] Screenshot refreshed
[[21:51:46]] [INFO] Refreshing screenshot...
[[21:51:44]] [SUCCESS] Screenshot refreshed successfully
[[21:51:44]] [SUCCESS] Screenshot refreshed successfully
[[21:51:43]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[21:51:43]] [SUCCESS] Screenshot refreshed
[[21:51:43]] [INFO] Refreshing screenshot...
[[21:51:29]] [SUCCESS] Screenshot refreshed successfully
[[21:51:29]] [SUCCESS] Screenshot refreshed successfully
[[21:51:28]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[21:51:27]] [SUCCESS] Screenshot refreshed
[[21:51:27]] [INFO] Refreshing screenshot...
[[21:51:24]] [SUCCESS] Screenshot refreshed successfully
[[21:51:24]] [SUCCESS] Screenshot refreshed successfully
[[21:51:24]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[21:51:24]] [SUCCESS] Screenshot refreshed
[[21:51:24]] [INFO] Refreshing screenshot...
[[21:51:21]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:51:21]] [SUCCESS] Screenshot refreshed successfully
[[21:51:21]] [SUCCESS] Screenshot refreshed successfully
[[21:51:20]] [SUCCESS] Screenshot refreshed
[[21:51:20]] [INFO] Refreshing screenshot...
[[21:51:19]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[21:51:19]] [SUCCESS] Screenshot refreshed successfully
[[21:51:19]] [SUCCESS] Screenshot refreshed successfully
[[21:51:18]] [SUCCESS] Screenshot refreshed
[[21:51:18]] [INFO] Refreshing screenshot...
[[21:51:16]] [SUCCESS] Screenshot refreshed successfully
[[21:51:16]] [SUCCESS] Screenshot refreshed successfully
[[21:51:15]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:51:15]] [SUCCESS] Screenshot refreshed
[[21:51:15]] [INFO] Refreshing screenshot...
[[21:51:12]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:51:12]] [SUCCESS] Screenshot refreshed successfully
[[21:51:12]] [SUCCESS] Screenshot refreshed successfully
[[21:51:11]] [SUCCESS] Screenshot refreshed
[[21:51:11]] [INFO] Refreshing screenshot...
[[21:51:09]] [SUCCESS] Screenshot refreshed successfully
[[21:51:09]] [SUCCESS] Screenshot refreshed successfully
[[21:51:09]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:51:08]] [SUCCESS] Screenshot refreshed
[[21:51:08]] [INFO] Refreshing screenshot...
[[21:51:04]] [SUCCESS] Screenshot refreshed successfully
[[21:51:04]] [SUCCESS] Screenshot refreshed successfully
[[21:51:04]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[21:51:04]] [INFO] Loaded 9 steps from test case: apple health
[[21:51:04]] [INFO] Loading steps for Multi Step action: apple health
[[21:51:04]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[21:51:03]] [SUCCESS] Screenshot refreshed
[[21:51:03]] [INFO] Refreshing screenshot...
[[21:51:00]] [SUCCESS] Screenshot refreshed successfully
[[21:51:00]] [SUCCESS] Screenshot refreshed successfully
[[21:51:00]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[21:50:59]] [SUCCESS] Screenshot refreshed
[[21:50:59]] [INFO] Refreshing screenshot...
[[21:50:56]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:50:56]] [SUCCESS] Screenshot refreshed successfully
[[21:50:56]] [SUCCESS] Screenshot refreshed successfully
[[21:50:56]] [SUCCESS] Screenshot refreshed
[[21:50:56]] [INFO] Refreshing screenshot...
[[21:50:54]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[21:50:54]] [SUCCESS] Screenshot refreshed successfully
[[21:50:54]] [SUCCESS] Screenshot refreshed successfully
[[21:50:53]] [SUCCESS] Screenshot refreshed
[[21:50:53]] [INFO] Refreshing screenshot...
[[21:50:51]] [SUCCESS] Screenshot refreshed successfully
[[21:50:51]] [SUCCESS] Screenshot refreshed successfully
[[21:50:51]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:50:50]] [SUCCESS] Screenshot refreshed
[[21:50:50]] [INFO] Refreshing screenshot...
[[21:50:47]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:50:47]] [SUCCESS] Screenshot refreshed successfully
[[21:50:47]] [SUCCESS] Screenshot refreshed successfully
[[21:50:47]] [SUCCESS] Screenshot refreshed
[[21:50:47]] [INFO] Refreshing screenshot...
[[21:50:42]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:50:42]] [SUCCESS] Screenshot refreshed successfully
[[21:50:42]] [SUCCESS] Screenshot refreshed successfully
[[21:50:42]] [SUCCESS] Screenshot refreshed
[[21:50:42]] [INFO] Refreshing screenshot...
[[21:50:30]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[21:50:30]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[21:50:30]] [INFO] Moving to the next test case after failure (server will handle retry)
[[21:50:30]] [ERROR] Multi Step action step 3 failed: Element with xpath '//XCUIElementTypeStaticText[@name="Edit"]' not clickable within timeout of 10 seconds
[[21:50:18]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:50:18]] [SUCCESS] Screenshot refreshed successfully
[[21:50:18]] [SUCCESS] Screenshot refreshed successfully
[[21:50:18]] [SUCCESS] Screenshot refreshed
[[21:50:18]] [INFO] Refreshing screenshot...
[[21:50:17]] [SUCCESS] Screenshot refreshed successfully
[[21:50:17]] [SUCCESS] Screenshot refreshed successfully
[[21:50:17]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[21:50:16]] [SUCCESS] Screenshot refreshed
[[21:50:16]] [INFO] Refreshing screenshot...
[[21:50:12]] [SUCCESS] Screenshot refreshed successfully
[[21:50:12]] [SUCCESS] Screenshot refreshed successfully
[[21:50:11]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[21:50:11]] [INFO] Loaded 9 steps from test case: health2
[[21:50:11]] [INFO] Loading steps for Multi Step action: health2
[[21:50:11]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[21:50:11]] [SUCCESS] Screenshot refreshed
[[21:50:11]] [INFO] Refreshing screenshot...
[[21:50:08]] [SUCCESS] Screenshot refreshed successfully
[[21:50:08]] [SUCCESS] Screenshot refreshed successfully
[[21:50:08]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[21:50:07]] [SUCCESS] Screenshot refreshed
[[21:50:07]] [INFO] Refreshing screenshot...
[[21:50:04]] [SUCCESS] Screenshot refreshed successfully
[[21:50:04]] [SUCCESS] Screenshot refreshed successfully
[[21:50:04]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[21:50:03]] [SUCCESS] Screenshot refreshed
[[21:50:03]] [INFO] Refreshing screenshot...
[[21:50:00]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:50:00]] [SUCCESS] Screenshot refreshed successfully
[[21:50:00]] [SUCCESS] Screenshot refreshed successfully
[[21:50:00]] [SUCCESS] Screenshot refreshed
[[21:50:00]] [INFO] Refreshing screenshot...
[[21:49:57]] [SUCCESS] Screenshot refreshed successfully
[[21:49:57]] [SUCCESS] Screenshot refreshed successfully
[[21:49:57]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[21:49:57]] [SUCCESS] Screenshot refreshed
[[21:49:57]] [INFO] Refreshing screenshot...
[[21:49:45]] [SUCCESS] Screenshot refreshed successfully
[[21:49:45]] [SUCCESS] Screenshot refreshed successfully
[[21:49:44]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[21:49:44]] [SUCCESS] Screenshot refreshed
[[21:49:44]] [INFO] Refreshing screenshot...
[[21:49:41]] [SUCCESS] Screenshot refreshed successfully
[[21:49:41]] [SUCCESS] Screenshot refreshed successfully
[[21:49:40]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[21:49:40]] [SUCCESS] Screenshot refreshed
[[21:49:40]] [INFO] Refreshing screenshot...
[[21:49:37]] [SUCCESS] Screenshot refreshed successfully
[[21:49:37]] [SUCCESS] Screenshot refreshed successfully
[[21:49:37]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[21:49:36]] [SUCCESS] Screenshot refreshed
[[21:49:36]] [INFO] Refreshing screenshot...
[[21:49:34]] [SUCCESS] Screenshot refreshed successfully
[[21:49:34]] [SUCCESS] Screenshot refreshed successfully
[[21:49:34]] [INFO] Executing action 7/25: Wait for 1 ms
[[21:49:33]] [SUCCESS] Screenshot refreshed
[[21:49:33]] [INFO] Refreshing screenshot...
[[21:49:31]] [SUCCESS] Screenshot refreshed successfully
[[21:49:31]] [SUCCESS] Screenshot refreshed successfully
[[21:49:31]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[21:49:31]] [SUCCESS] Screenshot refreshed
[[21:49:31]] [INFO] Refreshing screenshot...
[[21:49:27]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:49:27]] [SUCCESS] Screenshot refreshed successfully
[[21:49:27]] [SUCCESS] Screenshot refreshed successfully
[[21:49:27]] [SUCCESS] Screenshot refreshed
[[21:49:27]] [INFO] Refreshing screenshot...
[[21:49:25]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[21:49:25]] [SUCCESS] Screenshot refreshed successfully
[[21:49:25]] [SUCCESS] Screenshot refreshed successfully
[[21:49:24]] [SUCCESS] Screenshot refreshed
[[21:49:24]] [INFO] Refreshing screenshot...
[[21:49:22]] [SUCCESS] Screenshot refreshed successfully
[[21:49:22]] [SUCCESS] Screenshot refreshed successfully
[[21:49:22]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:49:21]] [SUCCESS] Screenshot refreshed
[[21:49:21]] [INFO] Refreshing screenshot...
[[21:49:19]] [SUCCESS] Screenshot refreshed successfully
[[21:49:19]] [SUCCESS] Screenshot refreshed successfully
[[21:49:19]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[21:49:19]] [SUCCESS] Screenshot refreshed
[[21:49:19]] [INFO] Refreshing screenshot...
[[21:49:16]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[21:49:16]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[21:49:16]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[21:49:16]] [INFO] Clearing screenshots from database before execution...
[[21:49:16]] [SUCCESS] All screenshots deleted successfully
[[21:49:16]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:49:16]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_214916/screenshots
[[21:49:16]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_214916
[[21:49:16]] [SUCCESS] Report directory initialized successfully
[[21:49:16]] [INFO] Initializing report directory and screenshots folder...
[[21:49:13]] [SUCCESS] All screenshots deleted successfully
[[21:49:13]] [INFO] All actions cleared
[[21:49:13]] [INFO] Cleaning up screenshots...
[[21:49:10]] [SUCCESS] Screenshot refreshed successfully
[[21:49:09]] [SUCCESS] Screenshot refreshed
[[21:49:09]] [INFO] Refreshing screenshot...
[[21:49:08]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[21:49:08]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[21:49:05]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[21:49:04]] [SUCCESS] Found 1 device(s)
[[21:49:03]] [INFO] Refreshing device list...
