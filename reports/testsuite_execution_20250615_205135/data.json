{"name": "UI Execution 15/06/2025, 20:53:43", "testCases": [{"name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1389ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "screenshot": "screenshots/1ocjQweraX.png", "clean_action_id": "1ocjQweraX", "prefixed_action_id": "al_1ocjQweraX", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Add Log: Launched App Successfully (with screenshot)", "status": "passed", "duration": "6ms", "action_id": "DgM3bVs1gq", "screenshot_filename": "DgM3bVs1gq.png", "report_screenshot": "DgM3bVs1gq.png", "resolved_screenshot": "screenshots/DgM3bVs1gq.png", "screenshot": "screenshots/DgM3bVs1gq.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1181ms", "action_id": "xEkmNPZIcm", "screenshot_filename": "xEkmNPZIcm.png", "report_screenshot": "xEkmNPZIcm.png", "resolved_screenshot": "screenshots/xEkmNPZIcm.png", "screenshot": "screenshots/xEkmNPZIcm.png"}, {"name": "Add Log: Edit link is clicked (with screenshot)", "status": "passed", "duration": "7ms", "action_id": "kvP8mTn5NE", "screenshot_filename": "kvP8mTn5NE.png", "report_screenshot": "kvP8mTn5NE.png", "resolved_screenshot": "screenshots/kvP8mTn5NE.png", "screenshot": "screenshots/kvP8mTn5NE.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1568ms", "action_id": "1SoNMOkoZI", "screenshot_filename": "1SoNMOkoZI.png", "report_screenshot": "1SoNMOkoZI.png", "resolved_screenshot": "screenshots/1SoNMOkoZI.png", "screenshot": "screenshots/1SoNMOkoZI.png"}, {"name": "Add Log: Done link is clicked (with screenshot)", "status": "passed", "duration": "14ms", "action_id": "57iXs1aFRN", "screenshot_filename": "57iXs1aFRN.png", "report_screenshot": "57iXs1aFRN.png", "resolved_screenshot": "screenshots/57iXs1aFRN.png", "screenshot": "screenshots/57iXs1aFRN.png"}, {"name": "Wait for 1 ms", "status": "passed", "duration": "1002ms", "action_id": "KzjZOcsLuC", "screenshot_filename": "KzjZOcsLuC.png", "report_screenshot": "KzjZOcsLuC.png", "resolved_screenshot": "screenshots/KzjZOcsLuC.png", "screenshot": "screenshots/EQVUlqoOIG.png", "clean_action_id": "EQVUlqoOIG", "prefixed_action_id": "al_EQVUlqoOIG", "action_id_screenshot": "screenshots/KzjZOcsLuC.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1035ms", "action_id": "OqYf9xF3oX", "screenshot_filename": "OqYf9xF3oX.png", "report_screenshot": "OqYf9xF3oX.png", "resolved_screenshot": "screenshots/OqYf9xF3oX.png", "screenshot": "screenshots/trcDZyioDN.png", "clean_action_id": "trcDZyioDN", "prefixed_action_id": "al_trcDZyioDN", "action_id_screenshot": "screenshots/OqYf9xF3oX.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "7ms", "action_id": "11SUjcr97E", "screenshot_filename": "11SUjcr97E.png", "report_screenshot": "11SUjcr97E.png", "resolved_screenshot": "screenshots/11SUjcr97E.png", "screenshot": "screenshots/11SUjcr97E.png"}], "retry_info": {"retry_count": 0, "max_retries": 0, "was_retried": false}, "display_name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"}, {"name": "apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "failed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1161ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "screenshot": "screenshots/NEbYAzdLiO.png", "clean_action_id": "NEbYAzdLiO", "prefixed_action_id": "al_NEbYAzdLiO", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "855ms", "action_id": "kSkOSpIBHU", "screenshot_filename": "kSkOSpIBHU.png", "report_screenshot": "kSkOSpIBHU.png", "resolved_screenshot": "screenshots/kSkOSpIBHU.png", "screenshot": "screenshots/kSkOSpIBHU.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1556ms", "action_id": "3y8aPHXaem", "screenshot_filename": "3y8aPHXaem.png", "report_screenshot": "3y8aPHXaem.png", "resolved_screenshot": "screenshots/3y8aPHXaem.png", "screenshot": "screenshots/3y8aPHXaem.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1027ms", "action_id": "E5An5BbVuK", "screenshot_filename": "E5An5BbVuK.png", "report_screenshot": "E5An5BbVuK.png", "resolved_screenshot": "screenshots/E5An5BbVuK.png", "screenshot": "screenshots/iQO5BQ7PkX.png", "clean_action_id": "iQO5BQ7PkX", "prefixed_action_id": "al_iQO5BQ7PkX", "action_id_screenshot": "screenshots/E5An5BbVuK.png"}, {"name": "Add Log: Closed App Successfully (with screenshot)", "status": "passed", "duration": "7ms", "action_id": "bCdxIm4e7G", "screenshot_filename": "bCdxIm4e7G.png", "report_screenshot": "bCdxIm4e7G.png", "resolved_screenshot": "screenshots/bCdxIm4e7G.png", "screenshot": "screenshots/bCdxIm4e7G.png"}, {"name": "Execute Test Case: health2 (9 steps)", "status": "failed", "duration": "0ms", "action_id": "KfOSdvcOkk", "screenshot_filename": "KfOSdvcOkk.png", "report_screenshot": "KfOSdvcOkk.png", "resolved_screenshot": "screenshots/KfOSdvcOkk.png", "screenshot": "screenshots/cPUkbCbRBO.png", "clean_action_id": "cPUkbCbRBO", "prefixed_action_id": "al_cPUkbCbRBO", "action_id_screenshot": "screenshots/KfOSdvcOkk.png"}, {"name": "Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)", "status": "passed", "duration": "0ms", "action_id": "dvrRFM51cl", "screenshot_filename": "dvrRFM51cl.png", "report_screenshot": "dvrRFM51cl.png", "resolved_screenshot": "screenshots/dvrRFM51cl.png", "screenshot": "screenshots/dvrRFM51cl.png"}], "retry_info": {"retry_count": 0, "max_retries": 0, "was_retried": false}, "display_name": "apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions"}, {"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "50ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "screenshot": "screenshots/cDGhB2VjpS.png", "clean_action_id": "cDGhB2VjpS", "prefixed_action_id": "al_cDGhB2VjpS", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "3422ms", "action_id": "VG4kA77aQd", "screenshot_filename": "VG4kA77aQd.png", "report_screenshot": "VG4kA77aQd.png", "resolved_screenshot": "screenshots/VG4kA77aQd.png", "screenshot": "screenshots/VG4kA77aQd.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1582ms", "action_id": "eqzBKUQiu2", "screenshot_filename": "eqzBKUQiu2.png", "report_screenshot": "eqzBKUQiu2.png", "resolved_screenshot": "screenshots/eqzBKUQiu2.png", "screenshot": "screenshots/eqzBKUQiu2.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1167ms", "action_id": "g867Blt4er", "screenshot_filename": "g867Blt4er.png", "report_screenshot": "g867Blt4er.png", "resolved_screenshot": "screenshots/g867Blt4er.png", "screenshot": "screenshots/g867Blt4er.png"}, {"name": "Add Log: Clicked on Edit link successfully (with screenshot)", "status": "passed", "duration": "7ms", "action_id": "YHlM8nSELJ", "screenshot_filename": "YHlM8nSELJ.png", "report_screenshot": "YHlM8nSELJ.png", "resolved_screenshot": "screenshots/YHlM8nSELJ.png", "screenshot": "screenshots/YHlM8nSELJ.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1562ms", "action_id": "2bYiq8gKck", "screenshot_filename": "2bYiq8gKck.png", "report_screenshot": "2bYiq8gKck.png", "resolved_screenshot": "screenshots/2bYiq8gKck.png", "screenshot": "screenshots/2bYiq8gKck.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1031ms", "action_id": "KzjZOcsLuC", "screenshot_filename": "KzjZOcsLuC.png", "report_screenshot": "KzjZOcsLuC.png", "resolved_screenshot": "screenshots/KzjZOcsLuC.png", "screenshot": "screenshots/ReIXfb6A0u.png", "clean_action_id": "ReIXfb6A0u", "prefixed_action_id": "al_ReIXfb6A0u", "action_id_screenshot": "screenshots/KzjZOcsLuC.png"}, {"name": "Execute Test Case: apple health (8 steps)", "status": "passed", "duration": "0ms", "action_id": "OqYf9xF3oX", "screenshot_filename": "OqYf9xF3oX.png", "report_screenshot": "OqYf9xF3oX.png", "resolved_screenshot": "screenshots/OqYf9xF3oX.png", "screenshot": "screenshots/R1htTBhTOs.png", "clean_action_id": "R1htTBhTOs", "prefixed_action_id": "al_R1htTBhTOs", "action_id_screenshot": "screenshots/OqYf9xF3oX.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "7ms", "action_id": "Uq44dINowL", "screenshot_filename": "Uq44dINowL.png", "report_screenshot": "Uq44dINowL.png", "resolved_screenshot": "screenshots/Uq44dINowL.png", "screenshot": "screenshots/Uq44dINowL.png"}], "retry_info": {"retry_count": 0, "max_retries": 0, "was_retried": false}, "display_name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"}], "passed": 2, "failed": 1, "skipped": 0, "status": "failed"}