<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/15/2025, 8:53:43 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">2</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 15/06/2025, 20:53:43
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions 
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1389ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="DgM3bVs1gq.png" data-action-id="DgM3bVs1gq" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Launched App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: DgM3bVs1gq">DgM3bVs1gq</span>
                            </div>
                            <span class="test-step-duration">6ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="xEkmNPZIcm.png" data-action-id="xEkmNPZIcm" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: xEkmNPZIcm">xEkmNPZIcm</span>
                            </div>
                            <span class="test-step-duration">1181ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="kvP8mTn5NE.png" data-action-id="kvP8mTn5NE" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Edit link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: kvP8mTn5NE">kvP8mTn5NE</span>
                            </div>
                            <span class="test-step-duration">7ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="1SoNMOkoZI.png" data-action-id="1SoNMOkoZI" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: 1SoNMOkoZI">1SoNMOkoZI</span>
                            </div>
                            <span class="test-step-duration">1568ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="57iXs1aFRN.png" data-action-id="57iXs1aFRN" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Done link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: 57iXs1aFRN">57iXs1aFRN</span>
                            </div>
                            <span class="test-step-duration">14ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="KzjZOcsLuC.png" data-action-id="KzjZOcsLuC" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 1 ms <span class="action-id-badge" title="Action ID: KzjZOcsLuC">KzjZOcsLuC</span>
                            </div>
                            <span class="test-step-duration">1002ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="OqYf9xF3oX.png" data-action-id="OqYf9xF3oX" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: OqYf9xF3oX">OqYf9xF3oX</span>
                            </div>
                            <span class="test-step-duration">1035ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="11SUjcr97E.png" data-action-id="11SUjcr97E" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: 11SUjcr97E">11SUjcr97E</span>
                            </div>
                            <span class="test-step-duration">7ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #2 apple health (Copy)
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            7 actions 
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1161ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="kSkOSpIBHU.png" data-action-id="kSkOSpIBHU" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: kSkOSpIBHU">kSkOSpIBHU</span>
                            </div>
                            <span class="test-step-duration">855ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="3y8aPHXaem.png" data-action-id="3y8aPHXaem" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: 3y8aPHXaem">3y8aPHXaem</span>
                            </div>
                            <span class="test-step-duration">1556ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="E5An5BbVuK.png" data-action-id="E5An5BbVuK" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: E5An5BbVuK">E5An5BbVuK</span>
                            </div>
                            <span class="test-step-duration">1027ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="bCdxIm4e7G.png" data-action-id="bCdxIm4e7G" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Closed App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: bCdxIm4e7G">bCdxIm4e7G</span>
                            </div>
                            <span class="test-step-duration">7ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="failed"
                            data-screenshot="KfOSdvcOkk.png" data-action-id="KfOSdvcOkk" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Execute Test Case: health2 (9 steps) <span class="action-id-badge" title="Action ID: KfOSdvcOkk">KfOSdvcOkk</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="dvrRFM51cl.png" data-action-id="dvrRFM51cl" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery) <span class="action-id-badge" title="Action ID: dvrRFM51cl">dvrRFM51cl</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #3 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions 
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-2-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">50ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="VG4kA77aQd.png" data-action-id="VG4kA77aQd" onclick="showStepDetails('step-2-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: VG4kA77aQd">VG4kA77aQd</span>
                            </div>
                            <span class="test-step-duration">3422ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="eqzBKUQiu2.png" data-action-id="eqzBKUQiu2" onclick="showStepDetails('step-2-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: eqzBKUQiu2">eqzBKUQiu2</span>
                            </div>
                            <span class="test-step-duration">1582ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="g867Blt4er.png" data-action-id="g867Blt4er" onclick="showStepDetails('step-2-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: g867Blt4er">g867Blt4er</span>
                            </div>
                            <span class="test-step-duration">1167ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="YHlM8nSELJ.png" data-action-id="YHlM8nSELJ" onclick="showStepDetails('step-2-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Clicked on Edit link successfully (with screenshot) <span class="action-id-badge" title="Action ID: YHlM8nSELJ">YHlM8nSELJ</span>
                            </div>
                            <span class="test-step-duration">7ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="2bYiq8gKck.png" data-action-id="2bYiq8gKck" onclick="showStepDetails('step-2-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: 2bYiq8gKck">2bYiq8gKck</span>
                            </div>
                            <span class="test-step-duration">1562ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="KzjZOcsLuC.png" data-action-id="KzjZOcsLuC" onclick="showStepDetails('step-2-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: KzjZOcsLuC">KzjZOcsLuC</span>
                            </div>
                            <span class="test-step-duration">1031ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="OqYf9xF3oX.png" data-action-id="OqYf9xF3oX" onclick="showStepDetails('step-2-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: apple health (8 steps) <span class="action-id-badge" title="Action ID: OqYf9xF3oX">OqYf9xF3oX</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="Uq44dINowL.png" data-action-id="Uq44dINowL" onclick="showStepDetails('step-2-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: Uq44dINowL">Uq44dINowL</span>
                            </div>
                            <span class="test-step-duration">7ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 15/06/2025, 20:53:43","testCases":[{"name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1389ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","screenshot":"screenshots/1ocjQweraX.png","clean_action_id":"1ocjQweraX","prefixed_action_id":"al_1ocjQweraX","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Add Log: Launched App Successfully (with screenshot)","status":"passed","duration":"6ms","action_id":"DgM3bVs1gq","screenshot_filename":"DgM3bVs1gq.png","report_screenshot":"DgM3bVs1gq.png","resolved_screenshot":"screenshots/DgM3bVs1gq.png","screenshot":"screenshots/DgM3bVs1gq.png","action_id_screenshot":"screenshots/DgM3bVs1gq.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1181ms","action_id":"xEkmNPZIcm","screenshot_filename":"xEkmNPZIcm.png","report_screenshot":"xEkmNPZIcm.png","resolved_screenshot":"screenshots/xEkmNPZIcm.png","screenshot":"screenshots/xEkmNPZIcm.png","action_id_screenshot":"screenshots/xEkmNPZIcm.png"},{"name":"Add Log: Edit link is clicked (with screenshot)","status":"passed","duration":"7ms","action_id":"kvP8mTn5NE","screenshot_filename":"kvP8mTn5NE.png","report_screenshot":"kvP8mTn5NE.png","resolved_screenshot":"screenshots/kvP8mTn5NE.png","screenshot":"screenshots/kvP8mTn5NE.png","action_id_screenshot":"screenshots/kvP8mTn5NE.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1568ms","action_id":"1SoNMOkoZI","screenshot_filename":"1SoNMOkoZI.png","report_screenshot":"1SoNMOkoZI.png","resolved_screenshot":"screenshots/1SoNMOkoZI.png","screenshot":"screenshots/1SoNMOkoZI.png","action_id_screenshot":"screenshots/1SoNMOkoZI.png"},{"name":"Add Log: Done link is clicked (with screenshot)","status":"passed","duration":"14ms","action_id":"57iXs1aFRN","screenshot_filename":"57iXs1aFRN.png","report_screenshot":"57iXs1aFRN.png","resolved_screenshot":"screenshots/57iXs1aFRN.png","screenshot":"screenshots/57iXs1aFRN.png","action_id_screenshot":"screenshots/57iXs1aFRN.png"},{"name":"Wait for 1 ms","status":"passed","duration":"1002ms","action_id":"KzjZOcsLuC","screenshot_filename":"KzjZOcsLuC.png","report_screenshot":"KzjZOcsLuC.png","resolved_screenshot":"screenshots/KzjZOcsLuC.png","screenshot":"screenshots/EQVUlqoOIG.png","clean_action_id":"EQVUlqoOIG","prefixed_action_id":"al_EQVUlqoOIG","action_id_screenshot":"screenshots/KzjZOcsLuC.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1035ms","action_id":"OqYf9xF3oX","screenshot_filename":"OqYf9xF3oX.png","report_screenshot":"OqYf9xF3oX.png","resolved_screenshot":"screenshots/OqYf9xF3oX.png","screenshot":"screenshots/trcDZyioDN.png","clean_action_id":"trcDZyioDN","prefixed_action_id":"al_trcDZyioDN","action_id_screenshot":"screenshots/OqYf9xF3oX.png"},{"name":"Add Log: App is closed (with screenshot)","status":"passed","duration":"7ms","action_id":"11SUjcr97E","screenshot_filename":"11SUjcr97E.png","report_screenshot":"11SUjcr97E.png","resolved_screenshot":"screenshots/11SUjcr97E.png","screenshot":"screenshots/11SUjcr97E.png","action_id_screenshot":"screenshots/11SUjcr97E.png"}],"retry_info":{"retry_count":0,"max_retries":0,"was_retried":false},"display_name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"},{"name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1161ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","screenshot":"screenshots/NEbYAzdLiO.png","clean_action_id":"NEbYAzdLiO","prefixed_action_id":"al_NEbYAzdLiO","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"855ms","action_id":"kSkOSpIBHU","screenshot_filename":"kSkOSpIBHU.png","report_screenshot":"kSkOSpIBHU.png","resolved_screenshot":"screenshots/kSkOSpIBHU.png","screenshot":"screenshots/kSkOSpIBHU.png","action_id_screenshot":"screenshots/kSkOSpIBHU.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1556ms","action_id":"3y8aPHXaem","screenshot_filename":"3y8aPHXaem.png","report_screenshot":"3y8aPHXaem.png","resolved_screenshot":"screenshots/3y8aPHXaem.png","screenshot":"screenshots/3y8aPHXaem.png","action_id_screenshot":"screenshots/3y8aPHXaem.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1027ms","action_id":"E5An5BbVuK","screenshot_filename":"E5An5BbVuK.png","report_screenshot":"E5An5BbVuK.png","resolved_screenshot":"screenshots/E5An5BbVuK.png","screenshot":"screenshots/iQO5BQ7PkX.png","clean_action_id":"iQO5BQ7PkX","prefixed_action_id":"al_iQO5BQ7PkX","action_id_screenshot":"screenshots/E5An5BbVuK.png"},{"name":"Add Log: Closed App Successfully (with screenshot)","status":"passed","duration":"7ms","action_id":"bCdxIm4e7G","screenshot_filename":"bCdxIm4e7G.png","report_screenshot":"bCdxIm4e7G.png","resolved_screenshot":"screenshots/bCdxIm4e7G.png","screenshot":"screenshots/bCdxIm4e7G.png","action_id_screenshot":"screenshots/bCdxIm4e7G.png"},{"name":"Execute Test Case: health2 (9 steps)","status":"failed","duration":"0ms","action_id":"KfOSdvcOkk","screenshot_filename":"KfOSdvcOkk.png","report_screenshot":"KfOSdvcOkk.png","resolved_screenshot":"screenshots/KfOSdvcOkk.png","screenshot":"screenshots/cPUkbCbRBO.png","clean_action_id":"cPUkbCbRBO","prefixed_action_id":"al_cPUkbCbRBO","action_id_screenshot":"screenshots/KfOSdvcOkk.png"},{"name":"Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)","status":"passed","duration":"0ms","action_id":"dvrRFM51cl","screenshot_filename":"dvrRFM51cl.png","report_screenshot":"dvrRFM51cl.png","resolved_screenshot":"screenshots/dvrRFM51cl.png","screenshot":"screenshots/dvrRFM51cl.png","action_id_screenshot":"screenshots/dvrRFM51cl.png"}],"retry_info":{"retry_count":0,"max_retries":0,"was_retried":false},"display_name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions"},{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"50ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","screenshot":"screenshots/cDGhB2VjpS.png","clean_action_id":"cDGhB2VjpS","prefixed_action_id":"al_cDGhB2VjpS","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"3422ms","action_id":"VG4kA77aQd","screenshot_filename":"VG4kA77aQd.png","report_screenshot":"VG4kA77aQd.png","resolved_screenshot":"screenshots/VG4kA77aQd.png","screenshot":"screenshots/VG4kA77aQd.png","action_id_screenshot":"screenshots/VG4kA77aQd.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1582ms","action_id":"eqzBKUQiu2","screenshot_filename":"eqzBKUQiu2.png","report_screenshot":"eqzBKUQiu2.png","resolved_screenshot":"screenshots/eqzBKUQiu2.png","screenshot":"screenshots/eqzBKUQiu2.png","action_id_screenshot":"screenshots/eqzBKUQiu2.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1167ms","action_id":"g867Blt4er","screenshot_filename":"g867Blt4er.png","report_screenshot":"g867Blt4er.png","resolved_screenshot":"screenshots/g867Blt4er.png","screenshot":"screenshots/g867Blt4er.png","action_id_screenshot":"screenshots/g867Blt4er.png"},{"name":"Add Log: Clicked on Edit link successfully (with screenshot)","status":"passed","duration":"7ms","action_id":"YHlM8nSELJ","screenshot_filename":"YHlM8nSELJ.png","report_screenshot":"YHlM8nSELJ.png","resolved_screenshot":"screenshots/YHlM8nSELJ.png","screenshot":"screenshots/YHlM8nSELJ.png","action_id_screenshot":"screenshots/YHlM8nSELJ.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1562ms","action_id":"2bYiq8gKck","screenshot_filename":"2bYiq8gKck.png","report_screenshot":"2bYiq8gKck.png","resolved_screenshot":"screenshots/2bYiq8gKck.png","screenshot":"screenshots/2bYiq8gKck.png","action_id_screenshot":"screenshots/2bYiq8gKck.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1031ms","action_id":"KzjZOcsLuC","screenshot_filename":"KzjZOcsLuC.png","report_screenshot":"KzjZOcsLuC.png","resolved_screenshot":"screenshots/KzjZOcsLuC.png","screenshot":"screenshots/ReIXfb6A0u.png","clean_action_id":"ReIXfb6A0u","prefixed_action_id":"al_ReIXfb6A0u","action_id_screenshot":"screenshots/KzjZOcsLuC.png"},{"name":"Execute Test Case: apple health (8 steps)","status":"passed","duration":"0ms","action_id":"OqYf9xF3oX","screenshot_filename":"OqYf9xF3oX.png","report_screenshot":"OqYf9xF3oX.png","resolved_screenshot":"screenshots/OqYf9xF3oX.png","screenshot":"screenshots/R1htTBhTOs.png","clean_action_id":"R1htTBhTOs","prefixed_action_id":"al_R1htTBhTOs","action_id_screenshot":"screenshots/OqYf9xF3oX.png"},{"name":"Add Log: App is closed (with screenshot)","status":"passed","duration":"7ms","action_id":"Uq44dINowL","screenshot_filename":"Uq44dINowL.png","report_screenshot":"Uq44dINowL.png","resolved_screenshot":"screenshots/Uq44dINowL.png","screenshot":"screenshots/Uq44dINowL.png","action_id_screenshot":"screenshots/Uq44dINowL.png"}],"retry_info":{"retry_count":0,"max_retries":0,"was_retried":false},"display_name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"}],"passed":2,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["4kBvNvFi5i.png","7MOUNxtPJz.png","AoLct5ZYWj.png","E5An5BbVuK.png","HphRLWPfSD.png","KfOSdvcOkk.png","KzjZOcsLuC.png","OqYf9xF3oX.png","SaJtvXOGlT.png","To6rgFtm9R.png","UppP3ZuqY6.png","ee5KkVz90e.png","f5C7GOVKXJ.png","jE4eZaRFK6.png","jF4jRny1iE.png","mOoxO3pBlm.png","mmT4QEfEZD.png","muJaxWLMoU.png","oIAtyQB5wY.png","rxDTLvtHmR.png","vjBGuN5y9x.png","wp1dY1wJ58.png","yvWe991wY2.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>