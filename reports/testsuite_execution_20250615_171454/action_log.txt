Action Log - 2025-06-15 18:18:11
================================================================================

[[18:18:11]] [INFO] Generating execution report...
[[18:18:11]] [SUCCESS] All tests passed successfully!
[[18:18:10]] [SUCCESS] Screenshot refreshed
[[18:18:10]] [INFO] Refreshing screenshot...
[[18:18:06]] [SUCCESS] Screenshot refreshed successfully
[[18:18:06]] [SUCCESS] Screenshot refreshed successfully
[[18:18:05]] [INFO] Executing action 512/512: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[18:18:05]] [SUCCESS] Screenshot refreshed
[[18:18:05]] [INFO] Refreshing screenshot...
[[18:18:00]] [SUCCESS] Screenshot refreshed successfully
[[18:18:00]] [SUCCESS] Screenshot refreshed successfully
[[18:18:00]] [INFO] Executing action 511/512: Tap on Text: "out"
[[18:18:00]] [SUCCESS] Screenshot refreshed
[[18:18:00]] [INFO] Refreshing screenshot...
[[18:17:56]] [SUCCESS] Screenshot refreshed successfully
[[18:17:56]] [SUCCESS] Screenshot refreshed successfully
[[18:17:56]] [INFO] Executing action 510/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:17:56]] [SUCCESS] Screenshot refreshed
[[18:17:56]] [INFO] Refreshing screenshot...
[[18:17:53]] [SUCCESS] Screenshot refreshed successfully
[[18:17:53]] [SUCCESS] Screenshot refreshed successfully
[[18:17:52]] [INFO] Executing action 509/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:17:52]] [SUCCESS] Screenshot refreshed
[[18:17:52]] [INFO] Refreshing screenshot...
[[18:17:49]] [SUCCESS] Screenshot refreshed successfully
[[18:17:49]] [SUCCESS] Screenshot refreshed successfully
[[18:17:48]] [INFO] Executing action 508/512: Swipe from (90%, 20%) to (30%, 20%)
[[18:17:48]] [SUCCESS] Screenshot refreshed
[[18:17:48]] [INFO] Refreshing screenshot...
[[18:17:45]] [SUCCESS] Screenshot refreshed successfully
[[18:17:45]] [SUCCESS] Screenshot refreshed successfully
[[18:17:44]] [INFO] Executing action 507/512: Swipe from (90%, 20%) to (30%, 20%)
[[18:17:44]] [SUCCESS] Screenshot refreshed
[[18:17:44]] [INFO] Refreshing screenshot...
[[18:17:40]] [SUCCESS] Screenshot refreshed successfully
[[18:17:40]] [SUCCESS] Screenshot refreshed successfully
[[18:17:40]] [INFO] Executing action 506/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:17:39]] [SUCCESS] Screenshot refreshed
[[18:17:39]] [INFO] Refreshing screenshot...
[[18:17:35]] [SUCCESS] Screenshot refreshed successfully
[[18:17:35]] [SUCCESS] Screenshot refreshed successfully
[[18:17:35]] [INFO] Executing action 505/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:17:34]] [SUCCESS] Screenshot refreshed
[[18:17:34]] [INFO] Refreshing screenshot...
[[18:17:30]] [SUCCESS] Screenshot refreshed successfully
[[18:17:30]] [SUCCESS] Screenshot refreshed successfully
[[18:17:30]] [INFO] Executing action 504/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:17:29]] [SUCCESS] Screenshot refreshed
[[18:17:29]] [INFO] Refreshing screenshot...
[[18:17:26]] [SUCCESS] Screenshot refreshed successfully
[[18:17:26]] [SUCCESS] Screenshot refreshed successfully
[[18:17:26]] [INFO] Executing action 503/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:17:25]] [SUCCESS] Screenshot refreshed
[[18:17:25]] [INFO] Refreshing screenshot...
[[18:17:22]] [SUCCESS] Screenshot refreshed successfully
[[18:17:22]] [SUCCESS] Screenshot refreshed successfully
[[18:17:22]] [INFO] Executing action 502/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:17:21]] [SUCCESS] Screenshot refreshed
[[18:17:21]] [INFO] Refreshing screenshot...
[[18:17:18]] [SUCCESS] Screenshot refreshed successfully
[[18:17:18]] [SUCCESS] Screenshot refreshed successfully
[[18:17:17]] [INFO] Executing action 501/512: iOS Function: text
[[18:17:17]] [SUCCESS] Screenshot refreshed
[[18:17:17]] [INFO] Refreshing screenshot...
[[18:17:12]] [SUCCESS] Screenshot refreshed successfully
[[18:17:12]] [SUCCESS] Screenshot refreshed successfully
[[18:17:11]] [INFO] Executing action 500/512: Tap on Text: "Find"
[[18:17:11]] [SUCCESS] Screenshot refreshed
[[18:17:11]] [INFO] Refreshing screenshot...
[[18:17:08]] [SUCCESS] Screenshot refreshed successfully
[[18:17:08]] [SUCCESS] Screenshot refreshed successfully
[[18:17:07]] [INFO] Executing action 499/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:17:07]] [SUCCESS] Screenshot refreshed
[[18:17:07]] [INFO] Refreshing screenshot...
[[18:17:02]] [SUCCESS] Screenshot refreshed successfully
[[18:17:02]] [SUCCESS] Screenshot refreshed successfully
[[18:17:02]] [INFO] Executing action 498/512: iOS Function: text
[[18:17:02]] [SUCCESS] Screenshot refreshed
[[18:17:02]] [INFO] Refreshing screenshot...
[[18:16:58]] [SUCCESS] Screenshot refreshed successfully
[[18:16:58]] [SUCCESS] Screenshot refreshed successfully
[[18:16:58]] [INFO] Executing action 497/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:16:57]] [SUCCESS] Screenshot refreshed
[[18:16:57]] [INFO] Refreshing screenshot...
[[18:16:53]] [SUCCESS] Screenshot refreshed successfully
[[18:16:53]] [SUCCESS] Screenshot refreshed successfully
[[18:16:53]] [INFO] Executing action 496/512: iOS Function: text
[[18:16:52]] [SUCCESS] Screenshot refreshed
[[18:16:52]] [INFO] Refreshing screenshot...
[[18:16:49]] [SUCCESS] Screenshot refreshed successfully
[[18:16:49]] [SUCCESS] Screenshot refreshed successfully
[[18:16:48]] [INFO] Executing action 495/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:16:48]] [SUCCESS] Screenshot refreshed
[[18:16:48]] [INFO] Refreshing screenshot...
[[18:16:46]] [SUCCESS] Screenshot refreshed successfully
[[18:16:46]] [SUCCESS] Screenshot refreshed successfully
[[18:16:45]] [INFO] Executing action 494/512: iOS Function: alert_accept
[[18:16:45]] [SUCCESS] Screenshot refreshed
[[18:16:45]] [INFO] Refreshing screenshot...
[[18:16:40]] [SUCCESS] Screenshot refreshed successfully
[[18:16:40]] [SUCCESS] Screenshot refreshed successfully
[[18:16:39]] [INFO] Executing action 493/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:16:39]] [SUCCESS] Screenshot refreshed
[[18:16:39]] [INFO] Refreshing screenshot...
[[18:16:35]] [SUCCESS] Screenshot refreshed successfully
[[18:16:35]] [SUCCESS] Screenshot refreshed successfully
[[18:16:35]] [INFO] Executing action 492/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:16:34]] [SUCCESS] Screenshot refreshed
[[18:16:34]] [INFO] Refreshing screenshot...
[[18:16:29]] [SUCCESS] Screenshot refreshed successfully
[[18:16:29]] [SUCCESS] Screenshot refreshed successfully
[[18:16:29]] [INFO] Executing action 491/512: Swipe from (5%, 50%) to (90%, 50%)
[[18:16:29]] [SUCCESS] Screenshot refreshed
[[18:16:29]] [INFO] Refreshing screenshot...
[[18:16:24]] [SUCCESS] Screenshot refreshed successfully
[[18:16:24]] [SUCCESS] Screenshot refreshed successfully
[[18:16:23]] [INFO] Executing action 490/512: Swipe from (5%, 50%) to (90%, 50%)
[[18:16:23]] [SUCCESS] Screenshot refreshed
[[18:16:23]] [INFO] Refreshing screenshot...
[[18:16:19]] [INFO] Executing action 489/512: Tap on Text: "Months"
[[18:16:19]] [SUCCESS] Screenshot refreshed successfully
[[18:16:19]] [SUCCESS] Screenshot refreshed successfully
[[18:16:18]] [SUCCESS] Screenshot refreshed
[[18:16:18]] [INFO] Refreshing screenshot...
[[18:16:14]] [SUCCESS] Screenshot refreshed successfully
[[18:16:14]] [SUCCESS] Screenshot refreshed successfully
[[18:16:14]] [INFO] Executing action 488/512: Tap on Text: "Age"
[[18:16:13]] [SUCCESS] Screenshot refreshed
[[18:16:13]] [INFO] Refreshing screenshot...
[[18:16:09]] [SUCCESS] Screenshot refreshed successfully
[[18:16:09]] [SUCCESS] Screenshot refreshed successfully
[[18:16:09]] [INFO] Executing action 487/512: Tap on Text: "Toys"
[[18:16:09]] [SUCCESS] Screenshot refreshed
[[18:16:09]] [INFO] Refreshing screenshot...
[[18:16:06]] [SUCCESS] Screenshot refreshed successfully
[[18:16:06]] [SUCCESS] Screenshot refreshed successfully
[[18:16:05]] [INFO] Executing action 486/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[18:16:05]] [SUCCESS] Screenshot refreshed
[[18:16:05]] [INFO] Refreshing screenshot...
[[18:16:01]] [SUCCESS] Screenshot refreshed successfully
[[18:16:01]] [SUCCESS] Screenshot refreshed successfully
[[18:15:59]] [INFO] Executing action 485/512: Restart app: env[appid]
[[18:15:59]] [SUCCESS] Screenshot refreshed
[[18:15:59]] [INFO] Refreshing screenshot...
[[18:15:59]] [SUCCESS] Screenshot refreshed
[[18:15:59]] [INFO] Refreshing screenshot...
[[18:15:53]] [SUCCESS] Screenshot refreshed successfully
[[18:15:53]] [SUCCESS] Screenshot refreshed successfully
[[18:15:53]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[18:15:52]] [SUCCESS] Screenshot refreshed
[[18:15:52]] [INFO] Refreshing screenshot...
[[18:14:11]] [SUCCESS] Screenshot refreshed successfully
[[18:14:11]] [SUCCESS] Screenshot refreshed successfully
[[18:14:11]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:14:10]] [SUCCESS] Screenshot refreshed
[[18:14:10]] [INFO] Refreshing screenshot...
[[18:14:05]] [SUCCESS] Screenshot refreshed successfully
[[18:14:05]] [SUCCESS] Screenshot refreshed successfully
[[18:14:04]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[18:14:04]] [SUCCESS] Screenshot refreshed
[[18:14:04]] [INFO] Refreshing screenshot...
[[18:12:24]] [SUCCESS] Screenshot refreshed successfully
[[18:12:24]] [SUCCESS] Screenshot refreshed successfully
[[18:12:23]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:12:23]] [SUCCESS] Screenshot refreshed
[[18:12:23]] [INFO] Refreshing screenshot...
[[18:12:17]] [SUCCESS] Screenshot refreshed successfully
[[18:12:17]] [SUCCESS] Screenshot refreshed successfully
[[18:12:17]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[18:12:16]] [SUCCESS] Screenshot refreshed
[[18:12:16]] [INFO] Refreshing screenshot...
[[18:10:35]] [SUCCESS] Screenshot refreshed successfully
[[18:10:35]] [SUCCESS] Screenshot refreshed successfully
[[18:10:34]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:10:33]] [SUCCESS] Screenshot refreshed
[[18:10:33]] [INFO] Refreshing screenshot...
[[18:10:28]] [SUCCESS] Screenshot refreshed successfully
[[18:10:28]] [SUCCESS] Screenshot refreshed successfully
[[18:10:27]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[18:10:27]] [SUCCESS] Screenshot refreshed
[[18:10:27]] [INFO] Refreshing screenshot...
[[18:08:44]] [SUCCESS] Screenshot refreshed successfully
[[18:08:44]] [SUCCESS] Screenshot refreshed successfully
[[18:08:44]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:08:43]] [SUCCESS] Screenshot refreshed
[[18:08:43]] [INFO] Refreshing screenshot...
[[18:08:38]] [SUCCESS] Screenshot refreshed successfully
[[18:08:38]] [SUCCESS] Screenshot refreshed successfully
[[18:08:37]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[18:08:37]] [SUCCESS] Screenshot refreshed
[[18:08:37]] [INFO] Refreshing screenshot...
[[18:06:51]] [SUCCESS] Screenshot refreshed successfully
[[18:06:51]] [SUCCESS] Screenshot refreshed successfully
[[18:06:51]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:06:51]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[18:06:50]] [INFO] Loading steps for Multi Step action: Click_Paginations
[[18:06:50]] [INFO] Executing action 484/512: Execute Test Case: Click_Paginations (10 steps)
[[18:06:50]] [SUCCESS] Screenshot refreshed
[[18:06:50]] [INFO] Refreshing screenshot...
[[18:06:46]] [SUCCESS] Screenshot refreshed successfully
[[18:06:46]] [SUCCESS] Screenshot refreshed successfully
[[18:06:46]] [INFO] Executing action 483/512: iOS Function: text
[[18:06:46]] [SUCCESS] Screenshot refreshed
[[18:06:46]] [INFO] Refreshing screenshot...
[[18:06:40]] [SUCCESS] Screenshot refreshed successfully
[[18:06:40]] [SUCCESS] Screenshot refreshed successfully
[[18:06:40]] [INFO] Executing action 482/512: Tap on Text: "Find"
[[18:06:39]] [SUCCESS] Screenshot refreshed
[[18:06:39]] [INFO] Refreshing screenshot...
[[18:06:36]] [SUCCESS] Screenshot refreshed successfully
[[18:06:36]] [SUCCESS] Screenshot refreshed successfully
[[18:06:35]] [INFO] Executing action 481/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:06:35]] [SUCCESS] Screenshot refreshed
[[18:06:35]] [INFO] Refreshing screenshot...
[[18:06:33]] [INFO] Executing action 480/512: Launch app: env[appid]
[[18:06:33]] [SUCCESS] Screenshot refreshed successfully
[[18:06:33]] [SUCCESS] Screenshot refreshed successfully
[[18:06:33]] [SUCCESS] Screenshot refreshed
[[18:06:33]] [INFO] Refreshing screenshot...
[[18:06:29]] [SUCCESS] Screenshot refreshed successfully
[[18:06:29]] [SUCCESS] Screenshot refreshed successfully
[[18:06:29]] [INFO] Executing action 479/512: Tap on Text: "+61"
[[18:06:28]] [SUCCESS] Screenshot refreshed
[[18:06:28]] [INFO] Refreshing screenshot...
[[18:06:24]] [SUCCESS] Screenshot refreshed successfully
[[18:06:24]] [SUCCESS] Screenshot refreshed successfully
[[18:06:24]] [INFO] Executing action 478/512: Tap on Text: "1800"
[[18:06:23]] [SUCCESS] Screenshot refreshed
[[18:06:23]] [INFO] Refreshing screenshot...
[[18:06:19]] [SUCCESS] Screenshot refreshed successfully
[[18:06:19]] [SUCCESS] Screenshot refreshed successfully
[[18:06:18]] [INFO] Executing action 477/512: Tap on Text: "click"
[[18:06:18]] [SUCCESS] Screenshot refreshed
[[18:06:18]] [INFO] Refreshing screenshot...
[[18:05:59]] [SUCCESS] Screenshot refreshed successfully
[[18:05:59]] [SUCCESS] Screenshot refreshed successfully
[[18:05:59]] [INFO] Executing action 476/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:05:59]] [SUCCESS] Screenshot refreshed
[[18:05:59]] [INFO] Refreshing screenshot...
[[18:05:55]] [SUCCESS] Screenshot refreshed successfully
[[18:05:55]] [SUCCESS] Screenshot refreshed successfully
[[18:05:55]] [INFO] Executing action 475/512: Tap on Text: "FAQ"
[[18:05:54]] [SUCCESS] Screenshot refreshed
[[18:05:54]] [INFO] Refreshing screenshot...
[[18:05:50]] [SUCCESS] Screenshot refreshed successfully
[[18:05:50]] [SUCCESS] Screenshot refreshed successfully
[[18:05:50]] [INFO] Executing action 474/512: Tap on Text: "Help"
[[18:05:49]] [SUCCESS] Screenshot refreshed
[[18:05:49]] [INFO] Refreshing screenshot...
[[18:05:46]] [SUCCESS] Screenshot refreshed successfully
[[18:05:46]] [SUCCESS] Screenshot refreshed successfully
[[18:05:46]] [INFO] Executing action 473/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:05:45]] [SUCCESS] Screenshot refreshed
[[18:05:45]] [INFO] Refreshing screenshot...
[[18:05:40]] [SUCCESS] Screenshot refreshed successfully
[[18:05:40]] [SUCCESS] Screenshot refreshed successfully
[[18:05:40]] [INFO] Executing action 472/512: Restart app: env[appid]
[[18:05:39]] [SUCCESS] Screenshot refreshed
[[18:05:39]] [INFO] Refreshing screenshot...
[[18:05:35]] [INFO] Executing action 471/512: Tap on Text: "Done"
[[18:05:35]] [SUCCESS] Screenshot refreshed successfully
[[18:05:35]] [SUCCESS] Screenshot refreshed successfully
[[18:05:34]] [SUCCESS] Screenshot refreshed
[[18:05:34]] [INFO] Refreshing screenshot...
[[18:05:30]] [SUCCESS] Screenshot refreshed successfully
[[18:05:30]] [SUCCESS] Screenshot refreshed successfully
[[18:05:30]] [INFO] Executing action 470/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"]
[[18:05:30]] [SUCCESS] Screenshot refreshed
[[18:05:30]] [INFO] Refreshing screenshot...
[[18:05:15]] [SUCCESS] Screenshot refreshed successfully
[[18:05:15]] [SUCCESS] Screenshot refreshed successfully
[[18:05:14]] [INFO] Executing action 469/512: Swipe from (50%, 80%) to (50%, 10%)
[[18:05:14]] [SUCCESS] Screenshot refreshed
[[18:05:14]] [INFO] Refreshing screenshot...
[[18:04:58]] [SUCCESS] Screenshot refreshed successfully
[[18:04:58]] [SUCCESS] Screenshot refreshed successfully
[[18:04:57]] [INFO] Executing action 468/512: Swipe from (50%, 70%) to (50%, 10%)
[[18:04:56]] [SUCCESS] Screenshot refreshed
[[18:04:56]] [INFO] Refreshing screenshot...
[[18:04:53]] [SUCCESS] Screenshot refreshed successfully
[[18:04:53]] [SUCCESS] Screenshot refreshed successfully
[[18:04:53]] [INFO] Executing action 467/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:04:53]] [SUCCESS] Screenshot refreshed
[[18:04:53]] [INFO] Refreshing screenshot...
[[18:04:49]] [SUCCESS] Screenshot refreshed successfully
[[18:04:49]] [SUCCESS] Screenshot refreshed successfully
[[18:04:49]] [INFO] Executing action 466/512: iOS Function: text
[[18:04:48]] [SUCCESS] Screenshot refreshed
[[18:04:48]] [INFO] Refreshing screenshot...
[[18:04:43]] [SUCCESS] Screenshot refreshed successfully
[[18:04:43]] [SUCCESS] Screenshot refreshed successfully
[[18:04:42]] [INFO] Executing action 465/512: Tap on Text: "Find"
[[18:04:42]] [SUCCESS] Screenshot refreshed
[[18:04:42]] [INFO] Refreshing screenshot...
[[18:04:27]] [SUCCESS] Screenshot refreshed successfully
[[18:04:27]] [SUCCESS] Screenshot refreshed successfully
[[18:04:27]] [INFO] Executing action 464/512: Restart app: env[appid]
[[18:04:26]] [SUCCESS] Screenshot refreshed
[[18:04:26]] [INFO] Refreshing screenshot...
[[18:04:23]] [SUCCESS] Screenshot refreshed successfully
[[18:04:23]] [SUCCESS] Screenshot refreshed successfully
[[18:04:22]] [INFO] Executing action 463/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:04:22]] [SUCCESS] Screenshot refreshed
[[18:04:22]] [INFO] Refreshing screenshot...
[[18:04:19]] [SUCCESS] Screenshot refreshed successfully
[[18:04:19]] [SUCCESS] Screenshot refreshed successfully
[[18:04:19]] [INFO] Executing action 462/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[18:04:18]] [SUCCESS] Screenshot refreshed
[[18:04:18]] [INFO] Refreshing screenshot...
[[18:04:13]] [SUCCESS] Screenshot refreshed successfully
[[18:04:13]] [SUCCESS] Screenshot refreshed successfully
[[18:04:13]] [INFO] Executing action 461/512: Restart app: env[appid]
[[18:04:12]] [SUCCESS] Screenshot refreshed
[[18:04:12]] [INFO] Refreshing screenshot...
[[18:04:06]] [SUCCESS] Screenshot refreshed successfully
[[18:04:06]] [SUCCESS] Screenshot refreshed successfully
[[18:04:05]] [INFO] Executing action 460/512: Tap on element with accessibility_id: Add to bag
[[18:04:05]] [SUCCESS] Screenshot refreshed
[[18:04:05]] [INFO] Refreshing screenshot...
[[18:04:02]] [SUCCESS] Screenshot refreshed successfully
[[18:04:02]] [SUCCESS] Screenshot refreshed successfully
[[18:04:01]] [INFO] Executing action 459/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[18:04:01]] [SUCCESS] Screenshot refreshed
[[18:04:01]] [INFO] Refreshing screenshot...
[[18:03:55]] [SUCCESS] Screenshot refreshed successfully
[[18:03:55]] [SUCCESS] Screenshot refreshed successfully
[[18:03:54]] [INFO] Executing action 458/512: swipeTillVisible action
[[18:03:54]] [SUCCESS] Screenshot refreshed
[[18:03:54]] [INFO] Refreshing screenshot...
[[18:03:52]] [SUCCESS] Screenshot refreshed successfully
[[18:03:52]] [SUCCESS] Screenshot refreshed successfully
[[18:03:50]] [INFO] Executing action 457/512: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[18:03:50]] [SUCCESS] Screenshot refreshed
[[18:03:50]] [INFO] Refreshing screenshot...
[[18:03:46]] [SUCCESS] Screenshot refreshed successfully
[[18:03:46]] [SUCCESS] Screenshot refreshed successfully
[[18:03:45]] [INFO] Executing action 456/512: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[18:03:44]] [SUCCESS] Screenshot refreshed
[[18:03:44]] [INFO] Refreshing screenshot...
[[18:03:41]] [SUCCESS] Screenshot refreshed successfully
[[18:03:41]] [SUCCESS] Screenshot refreshed successfully
[[18:03:40]] [INFO] Executing action 455/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[18:03:40]] [SUCCESS] Screenshot refreshed
[[18:03:40]] [INFO] Refreshing screenshot...
[[18:03:34]] [SUCCESS] Screenshot refreshed successfully
[[18:03:34]] [SUCCESS] Screenshot refreshed successfully
[[18:03:33]] [INFO] Executing action 454/512: swipeTillVisible action
[[18:03:33]] [SUCCESS] Screenshot refreshed
[[18:03:33]] [INFO] Refreshing screenshot...
[[18:03:29]] [SUCCESS] Screenshot refreshed successfully
[[18:03:29]] [SUCCESS] Screenshot refreshed successfully
[[18:03:29]] [INFO] Executing action 453/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[18:03:28]] [SUCCESS] Screenshot refreshed
[[18:03:28]] [INFO] Refreshing screenshot...
[[18:03:24]] [SUCCESS] Screenshot refreshed successfully
[[18:03:24]] [SUCCESS] Screenshot refreshed successfully
[[18:03:24]] [INFO] Executing action 452/512: Tap on image: banner-close-updated.png
[[18:03:23]] [SUCCESS] Screenshot refreshed
[[18:03:23]] [INFO] Refreshing screenshot...
[[18:03:20]] [SUCCESS] Screenshot refreshed successfully
[[18:03:20]] [SUCCESS] Screenshot refreshed successfully
[[18:03:20]] [INFO] Executing action 451/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:03:19]] [SUCCESS] Screenshot refreshed
[[18:03:19]] [INFO] Refreshing screenshot...
[[18:03:16]] [SUCCESS] Screenshot refreshed successfully
[[18:03:16]] [SUCCESS] Screenshot refreshed successfully
[[18:03:16]] [INFO] Executing action 450/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[18:03:15]] [SUCCESS] Screenshot refreshed
[[18:03:15]] [INFO] Refreshing screenshot...
[[18:03:12]] [INFO] Executing action 449/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[18:03:12]] [SUCCESS] Screenshot refreshed successfully
[[18:03:12]] [SUCCESS] Screenshot refreshed successfully
[[18:03:11]] [SUCCESS] Screenshot refreshed
[[18:03:11]] [INFO] Refreshing screenshot...
[[18:03:08]] [SUCCESS] Screenshot refreshed successfully
[[18:03:08]] [SUCCESS] Screenshot refreshed successfully
[[18:03:07]] [INFO] Executing action 448/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[18:03:07]] [SUCCESS] Screenshot refreshed
[[18:03:07]] [INFO] Refreshing screenshot...
[[18:03:01]] [SUCCESS] Screenshot refreshed successfully
[[18:03:01]] [SUCCESS] Screenshot refreshed successfully
[[18:03:01]] [INFO] Executing action 447/512: Tap on element with accessibility_id: Add to bag
[[18:03:01]] [SUCCESS] Screenshot refreshed
[[18:03:01]] [INFO] Refreshing screenshot...
[[18:02:57]] [SUCCESS] Screenshot refreshed successfully
[[18:02:57]] [SUCCESS] Screenshot refreshed successfully
[[18:02:57]] [INFO] Executing action 446/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[18:02:57]] [SUCCESS] Screenshot refreshed
[[18:02:57]] [INFO] Refreshing screenshot...
[[18:02:52]] [SUCCESS] Screenshot refreshed successfully
[[18:02:52]] [SUCCESS] Screenshot refreshed successfully
[[18:02:50]] [INFO] Executing action 445/512: Tap on Text: "List"
[[18:02:50]] [SUCCESS] Screenshot refreshed
[[18:02:50]] [INFO] Refreshing screenshot...
[[18:02:46]] [SUCCESS] Screenshot refreshed successfully
[[18:02:46]] [SUCCESS] Screenshot refreshed successfully
[[18:02:45]] [INFO] Executing action 444/512: Tap on image: env[catalogue-menu-img]
[[18:02:44]] [SUCCESS] Screenshot refreshed
[[18:02:44]] [INFO] Refreshing screenshot...
[[18:02:39]] [SUCCESS] Screenshot refreshed successfully
[[18:02:39]] [SUCCESS] Screenshot refreshed successfully
[[18:02:39]] [INFO] Executing action 443/512: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[18:02:38]] [SUCCESS] Screenshot refreshed
[[18:02:38]] [INFO] Refreshing screenshot...
[[18:02:33]] [SUCCESS] Screenshot refreshed successfully
[[18:02:33]] [SUCCESS] Screenshot refreshed successfully
[[18:02:33]] [INFO] Executing action 442/512: Tap on Text: "Catalogue"
[[18:02:33]] [SUCCESS] Screenshot refreshed
[[18:02:33]] [INFO] Refreshing screenshot...
[[18:02:29]] [SUCCESS] Screenshot refreshed successfully
[[18:02:29]] [SUCCESS] Screenshot refreshed successfully
[[18:02:29]] [INFO] Executing action 441/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[18:02:29]] [SUCCESS] Screenshot refreshed
[[18:02:29]] [INFO] Refreshing screenshot...
[[18:02:23]] [SUCCESS] Screenshot refreshed successfully
[[18:02:23]] [SUCCESS] Screenshot refreshed successfully
[[18:02:23]] [INFO] Executing action 440/512: Tap on element with accessibility_id: Add to bag
[[18:02:22]] [SUCCESS] Screenshot refreshed
[[18:02:22]] [INFO] Refreshing screenshot...
[[18:02:18]] [SUCCESS] Screenshot refreshed successfully
[[18:02:18]] [SUCCESS] Screenshot refreshed successfully
[[18:02:18]] [INFO] Executing action 439/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[18:02:17]] [SUCCESS] Screenshot refreshed
[[18:02:17]] [INFO] Refreshing screenshot...
[[18:02:12]] [SUCCESS] Screenshot refreshed successfully
[[18:02:12]] [SUCCESS] Screenshot refreshed successfully
[[18:02:11]] [INFO] Executing action 438/512: Tap on Text: "List"
[[18:02:10]] [SUCCESS] Screenshot refreshed
[[18:02:10]] [INFO] Refreshing screenshot...
[[18:02:06]] [SUCCESS] Screenshot refreshed successfully
[[18:02:06]] [SUCCESS] Screenshot refreshed successfully
[[18:02:05]] [INFO] Executing action 437/512: Tap on image: env[catalogue-menu-img]
[[18:02:05]] [SUCCESS] Screenshot refreshed
[[18:02:05]] [INFO] Refreshing screenshot...
[[18:02:00]] [SUCCESS] Screenshot refreshed successfully
[[18:02:00]] [SUCCESS] Screenshot refreshed successfully
[[18:01:59]] [INFO] Executing action 436/512: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[18:01:59]] [SUCCESS] Screenshot refreshed
[[18:01:59]] [INFO] Refreshing screenshot...
[[18:01:54]] [SUCCESS] Screenshot refreshed successfully
[[18:01:54]] [SUCCESS] Screenshot refreshed successfully
[[18:01:54]] [INFO] Executing action 435/512: Tap on Text: "Catalogue"
[[18:01:54]] [SUCCESS] Screenshot refreshed
[[18:01:54]] [INFO] Refreshing screenshot...
[[18:01:51]] [SUCCESS] Screenshot refreshed successfully
[[18:01:51]] [SUCCESS] Screenshot refreshed successfully
[[18:01:50]] [INFO] Executing action 434/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[18:01:50]] [SUCCESS] Screenshot refreshed
[[18:01:50]] [INFO] Refreshing screenshot...
[[18:01:47]] [SUCCESS] Screenshot refreshed successfully
[[18:01:47]] [SUCCESS] Screenshot refreshed successfully
[[18:01:46]] [INFO] Executing action 433/512: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[18:01:46]] [SUCCESS] Screenshot refreshed
[[18:01:46]] [INFO] Refreshing screenshot...
[[18:01:43]] [SUCCESS] Screenshot refreshed successfully
[[18:01:43]] [SUCCESS] Screenshot refreshed successfully
[[18:01:43]] [INFO] Executing action 432/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[18:01:42]] [SUCCESS] Screenshot refreshed
[[18:01:42]] [INFO] Refreshing screenshot...
[[18:01:38]] [SUCCESS] Screenshot refreshed successfully
[[18:01:38]] [SUCCESS] Screenshot refreshed successfully
[[18:01:37]] [INFO] Executing action 431/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:01:37]] [SUCCESS] Screenshot refreshed
[[18:01:37]] [INFO] Refreshing screenshot...
[[18:01:33]] [INFO] Executing action 430/512: iOS Function: text
[[18:01:33]] [SUCCESS] Screenshot refreshed successfully
[[18:01:33]] [SUCCESS] Screenshot refreshed successfully
[[18:01:33]] [SUCCESS] Screenshot refreshed
[[18:01:33]] [INFO] Refreshing screenshot...
[[18:01:30]] [SUCCESS] Screenshot refreshed successfully
[[18:01:30]] [SUCCESS] Screenshot refreshed successfully
[[18:01:30]] [INFO] Executing action 429/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[18:01:30]] [SUCCESS] Screenshot refreshed
[[18:01:30]] [INFO] Refreshing screenshot...
[[18:01:26]] [SUCCESS] Screenshot refreshed successfully
[[18:01:26]] [SUCCESS] Screenshot refreshed successfully
[[18:01:26]] [INFO] Executing action 428/512: Restart app: com.apple.mobilesafari
[[18:01:25]] [SUCCESS] Screenshot refreshed
[[18:01:25]] [INFO] Refreshing screenshot...
[[18:01:21]] [SUCCESS] Screenshot refreshed successfully
[[18:01:21]] [SUCCESS] Screenshot refreshed successfully
[[18:01:21]] [INFO] Executing action 427/512: Tap on Text: "out"
[[18:01:20]] [SUCCESS] Screenshot refreshed
[[18:01:20]] [INFO] Refreshing screenshot...
[[18:01:17]] [SUCCESS] Screenshot refreshed successfully
[[18:01:17]] [SUCCESS] Screenshot refreshed successfully
[[18:01:17]] [INFO] Executing action 426/512: Swipe from (50%, 70%) to (50%, 30%)
[[18:01:16]] [SUCCESS] Screenshot refreshed
[[18:01:16]] [INFO] Refreshing screenshot...
[[18:01:13]] [SUCCESS] Screenshot refreshed successfully
[[18:01:13]] [SUCCESS] Screenshot refreshed successfully
[[18:01:13]] [INFO] Executing action 425/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[18:01:12]] [SUCCESS] Screenshot refreshed
[[18:01:12]] [INFO] Refreshing screenshot...
[[18:01:07]] [INFO] Executing action 424/512: Restart app: env[appid]
[[18:01:07]] [SUCCESS] Screenshot refreshed successfully
[[18:01:07]] [SUCCESS] Screenshot refreshed successfully
[[18:01:06]] [SUCCESS] Screenshot refreshed
[[18:01:06]] [INFO] Refreshing screenshot...
[[18:01:00]] [INFO] Executing action 423/512: Wait for 5 ms
[[18:01:00]] [SUCCESS] Screenshot refreshed successfully
[[18:01:00]] [SUCCESS] Screenshot refreshed successfully
[[18:00:59]] [SUCCESS] Screenshot refreshed
[[18:00:59]] [INFO] Refreshing screenshot...
[[18:00:57]] [INFO] Executing action 422/512: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[18:00:57]] [SUCCESS] Screenshot refreshed successfully
[[18:00:57]] [SUCCESS] Screenshot refreshed successfully
[[18:00:57]] [SUCCESS] Screenshot refreshed
[[18:00:57]] [INFO] Refreshing screenshot...
[[18:00:55]] [SUCCESS] Screenshot refreshed successfully
[[18:00:55]] [SUCCESS] Screenshot refreshed successfully
[[18:00:55]] [INFO] Executing action 421/512: Launch app: com.apple.Preferences
[[18:00:55]] [SUCCESS] Screenshot refreshed
[[18:00:55]] [INFO] Refreshing screenshot...
[[18:00:53]] [SUCCESS] Screenshot refreshed successfully
[[18:00:53]] [SUCCESS] Screenshot refreshed successfully
[[18:00:53]] [INFO] Executing action 420/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:00:53]] [SUCCESS] Screenshot refreshed
[[18:00:53]] [INFO] Refreshing screenshot...
[[18:00:50]] [SUCCESS] Screenshot refreshed successfully
[[18:00:50]] [SUCCESS] Screenshot refreshed successfully
[[18:00:50]] [INFO] Executing action 419/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[18:00:50]] [SUCCESS] Screenshot refreshed
[[18:00:50]] [INFO] Refreshing screenshot...
[[18:00:48]] [SUCCESS] Screenshot refreshed successfully
[[18:00:48]] [SUCCESS] Screenshot refreshed successfully
[[18:00:48]] [INFO] Executing action 418/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:00:48]] [SUCCESS] Screenshot refreshed
[[18:00:48]] [INFO] Refreshing screenshot...
[[18:00:45]] [SUCCESS] Screenshot refreshed successfully
[[18:00:45]] [SUCCESS] Screenshot refreshed successfully
[[18:00:45]] [INFO] Executing action 417/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[18:00:45]] [SUCCESS] Screenshot refreshed
[[18:00:45]] [INFO] Refreshing screenshot...
[[18:00:43]] [SUCCESS] Screenshot refreshed successfully
[[18:00:43]] [SUCCESS] Screenshot refreshed successfully
[[18:00:43]] [INFO] Executing action 416/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:00:43]] [SUCCESS] Screenshot refreshed
[[18:00:43]] [INFO] Refreshing screenshot...
[[18:00:40]] [SUCCESS] Screenshot refreshed successfully
[[18:00:40]] [SUCCESS] Screenshot refreshed successfully
[[18:00:40]] [INFO] Executing action 415/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[18:00:40]] [SUCCESS] Screenshot refreshed
[[18:00:40]] [INFO] Refreshing screenshot...
[[18:00:38]] [SUCCESS] Screenshot refreshed successfully
[[18:00:38]] [SUCCESS] Screenshot refreshed successfully
[[18:00:38]] [INFO] Executing action 414/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:00:37]] [SUCCESS] Screenshot refreshed
[[18:00:37]] [INFO] Refreshing screenshot...
[[18:00:32]] [INFO] Executing action 413/512: Restart app: env[appid]
[[18:00:32]] [SUCCESS] Screenshot refreshed successfully
[[18:00:32]] [SUCCESS] Screenshot refreshed successfully
[[18:00:32]] [SUCCESS] Screenshot refreshed
[[18:00:32]] [INFO] Refreshing screenshot...
[[18:00:30]] [INFO] Executing action 412/512: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[18:00:29]] [SUCCESS] Screenshot refreshed successfully
[[18:00:29]] [SUCCESS] Screenshot refreshed successfully
[[18:00:29]] [SUCCESS] Screenshot refreshed
[[18:00:29]] [INFO] Refreshing screenshot...
[[18:00:25]] [INFO] Executing action 411/512: Tap on Text: "Wi-Fi"
[[18:00:25]] [SUCCESS] Screenshot refreshed successfully
[[18:00:25]] [SUCCESS] Screenshot refreshed successfully
[[18:00:24]] [SUCCESS] Screenshot refreshed
[[18:00:24]] [INFO] Refreshing screenshot...
[[18:00:22]] [SUCCESS] Screenshot refreshed successfully
[[18:00:22]] [SUCCESS] Screenshot refreshed successfully
[[18:00:21]] [INFO] Executing action 410/512: Launch app: com.apple.Preferences
[[18:00:21]] [SUCCESS] Screenshot refreshed
[[18:00:21]] [INFO] Refreshing screenshot...
[[18:00:20]] [SUCCESS] Screenshot refreshed successfully
[[18:00:20]] [SUCCESS] Screenshot refreshed successfully
[[18:00:19]] [INFO] Executing action 409/512: Terminate app: com.apple.Preferences
[[18:00:19]] [SUCCESS] Screenshot refreshed
[[18:00:19]] [INFO] Refreshing screenshot...
[[18:00:18]] [SUCCESS] Screenshot refreshed
[[18:00:18]] [INFO] Refreshing screenshot...
[[18:00:15]] [SUCCESS] Screenshot refreshed successfully
[[18:00:15]] [SUCCESS] Screenshot refreshed successfully
[[18:00:15]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:00:14]] [SUCCESS] Screenshot refreshed
[[18:00:14]] [INFO] Refreshing screenshot...
[[18:00:10]] [SUCCESS] Screenshot refreshed successfully
[[18:00:10]] [SUCCESS] Screenshot refreshed successfully
[[18:00:10]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[18:00:09]] [SUCCESS] Screenshot refreshed
[[18:00:09]] [INFO] Refreshing screenshot...
[[18:00:06]] [SUCCESS] Screenshot refreshed successfully
[[18:00:06]] [SUCCESS] Screenshot refreshed successfully
[[18:00:05]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:00:05]] [SUCCESS] Screenshot refreshed
[[18:00:05]] [INFO] Refreshing screenshot...
[[18:00:01]] [SUCCESS] Screenshot refreshed successfully
[[18:00:01]] [SUCCESS] Screenshot refreshed successfully
[[18:00:01]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[18:00:00]] [SUCCESS] Screenshot refreshed
[[18:00:00]] [INFO] Refreshing screenshot...
[[17:59:56]] [SUCCESS] Screenshot refreshed successfully
[[17:59:56]] [SUCCESS] Screenshot refreshed successfully
[[17:59:56]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:59:56]] [SUCCESS] Screenshot refreshed
[[17:59:56]] [INFO] Refreshing screenshot...
[[17:59:51]] [SUCCESS] Screenshot refreshed successfully
[[17:59:51]] [SUCCESS] Screenshot refreshed successfully
[[17:59:50]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:59:50]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[17:59:50]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[17:59:50]] [INFO] Executing action 408/512: Execute Test Case: Kmart-Signin (6 steps)
[[17:59:50]] [SUCCESS] Screenshot refreshed
[[17:59:50]] [INFO] Refreshing screenshot...
[[17:59:48]] [SUCCESS] Screenshot refreshed successfully
[[17:59:48]] [SUCCESS] Screenshot refreshed successfully
[[17:59:47]] [INFO] Executing action 407/512: iOS Function: alert_accept
[[17:59:47]] [SUCCESS] Screenshot refreshed
[[17:59:47]] [INFO] Refreshing screenshot...
[[17:59:43]] [SUCCESS] Screenshot refreshed successfully
[[17:59:43]] [SUCCESS] Screenshot refreshed successfully
[[17:59:43]] [INFO] Executing action 406/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[17:59:42]] [SUCCESS] Screenshot refreshed
[[17:59:42]] [INFO] Refreshing screenshot...
[[17:59:27]] [SUCCESS] Screenshot refreshed successfully
[[17:59:27]] [SUCCESS] Screenshot refreshed successfully
[[17:59:27]] [INFO] Executing action 405/512: Restart app: env[appid]
[[17:59:26]] [SUCCESS] Screenshot refreshed
[[17:59:26]] [INFO] Refreshing screenshot...
[[17:59:26]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[17:59:26]] [INFO] Executing action 404/512: Hook Action: tap on image: banner-close-updated.png (Recovery)
[[17:59:26]] [SUCCESS] Screenshot refreshed
[[17:59:26]] [INFO] Refreshing screenshot...
[[17:59:22]] [SUCCESS] Screenshot refreshed successfully
[[17:59:22]] [SUCCESS] Screenshot refreshed successfully
[[17:59:22]] [INFO] Executing action 403/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:59:21]] [SUCCESS] Screenshot refreshed
[[17:59:21]] [INFO] Refreshing screenshot...
[[17:59:16]] [SUCCESS] Screenshot refreshed successfully
[[17:59:16]] [SUCCESS] Screenshot refreshed successfully
[[17:59:16]] [INFO] Executing action 402/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:59:16]] [SUCCESS] Screenshot refreshed
[[17:59:16]] [INFO] Refreshing screenshot...
[[17:59:12]] [SUCCESS] Screenshot refreshed successfully
[[17:59:12]] [SUCCESS] Screenshot refreshed successfully
[[17:59:12]] [INFO] Executing action 401/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:59:11]] [SUCCESS] Screenshot refreshed
[[17:59:11]] [INFO] Refreshing screenshot...
[[17:59:10]] [SUCCESS] Screenshot refreshed successfully
[[17:59:10]] [SUCCESS] Screenshot refreshed successfully
[[17:59:10]] [INFO] Executing action 400/512: Add Log: Post code successfully changed from Bag (with screenshot)
[[17:59:09]] [SUCCESS] Screenshot refreshed
[[17:59:09]] [INFO] Refreshing screenshot...
[[17:59:05]] [SUCCESS] Screenshot refreshed successfully
[[17:59:05]] [SUCCESS] Screenshot refreshed successfully
[[17:59:05]] [INFO] Executing action 399/512: Check if image "deliverto3000-se.png" exists on screen
[[17:59:05]] [SUCCESS] Screenshot refreshed
[[17:59:05]] [INFO] Refreshing screenshot...
[[17:59:01]] [SUCCESS] Screenshot refreshed successfully
[[17:59:01]] [SUCCESS] Screenshot refreshed successfully
[[17:59:01]] [INFO] Executing action 398/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[17:59:01]] [SUCCESS] Screenshot refreshed
[[17:59:01]] [INFO] Refreshing screenshot...
[[17:58:58]] [SUCCESS] Screenshot refreshed successfully
[[17:58:58]] [SUCCESS] Screenshot refreshed successfully
[[17:58:58]] [INFO] Executing action 397/512: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[17:58:57]] [SUCCESS] Screenshot refreshed
[[17:58:57]] [INFO] Refreshing screenshot...
[[17:58:53]] [SUCCESS] Screenshot refreshed successfully
[[17:58:53]] [SUCCESS] Screenshot refreshed successfully
[[17:58:53]] [INFO] Executing action 396/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:58:53]] [SUCCESS] Screenshot refreshed
[[17:58:53]] [INFO] Refreshing screenshot...
[[17:58:37]] [SUCCESS] Screenshot refreshed successfully
[[17:58:37]] [SUCCESS] Screenshot refreshed successfully
[[17:58:37]] [INFO] Executing action 395/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:58:36]] [SUCCESS] Screenshot refreshed
[[17:58:36]] [INFO] Refreshing screenshot...
[[17:58:31]] [SUCCESS] Screenshot refreshed successfully
[[17:58:31]] [SUCCESS] Screenshot refreshed successfully
[[17:58:31]] [INFO] Executing action 394/512: Tap on element with accessibility_id: Done
[[17:58:30]] [SUCCESS] Screenshot refreshed
[[17:58:30]] [INFO] Refreshing screenshot...
[[17:58:25]] [SUCCESS] Screenshot refreshed successfully
[[17:58:25]] [SUCCESS] Screenshot refreshed successfully
[[17:58:25]] [INFO] Executing action 393/512: Tap on Text: "VIC"
[[17:58:25]] [SUCCESS] Screenshot refreshed
[[17:58:25]] [INFO] Refreshing screenshot...
[[17:58:18]] [SUCCESS] Screenshot refreshed successfully
[[17:58:18]] [SUCCESS] Screenshot refreshed successfully
[[17:58:18]] [INFO] Executing action 392/512: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[17:58:18]] [SUCCESS] Screenshot refreshed
[[17:58:18]] [INFO] Refreshing screenshot...
[[17:58:12]] [SUCCESS] Screenshot refreshed successfully
[[17:58:12]] [SUCCESS] Screenshot refreshed successfully
[[17:58:12]] [INFO] Executing action 391/512: Tap on element with accessibility_id: delete
[[17:58:12]] [SUCCESS] Screenshot refreshed
[[17:58:12]] [INFO] Refreshing screenshot...
[[17:58:07]] [SUCCESS] Screenshot refreshed successfully
[[17:58:07]] [SUCCESS] Screenshot refreshed successfully
[[17:58:07]] [INFO] Executing action 390/512: Tap on Text: "Nearby"
[[17:58:06]] [SUCCESS] Screenshot refreshed
[[17:58:06]] [INFO] Refreshing screenshot...
[[17:58:03]] [SUCCESS] Screenshot refreshed successfully
[[17:58:03]] [SUCCESS] Screenshot refreshed successfully
[[17:58:03]] [INFO] Executing action 389/512: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[17:58:02]] [SUCCESS] Screenshot refreshed
[[17:58:02]] [INFO] Refreshing screenshot...
[[17:57:59]] [SUCCESS] Screenshot refreshed successfully
[[17:57:59]] [SUCCESS] Screenshot refreshed successfully
[[17:57:59]] [INFO] Executing action 388/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[17:57:58]] [SUCCESS] Screenshot refreshed
[[17:57:58]] [INFO] Refreshing screenshot...
[[17:57:55]] [INFO] Executing action 387/512: Wait till xpath=//XCUIElementTypeOther[@name="Delivery options"]/XCUIElementTypeButton[3]
[[17:57:55]] [SUCCESS] Screenshot refreshed successfully
[[17:57:55]] [SUCCESS] Screenshot refreshed successfully
[[17:57:55]] [SUCCESS] Screenshot refreshed
[[17:57:55]] [INFO] Refreshing screenshot...
[[17:57:51]] [SUCCESS] Screenshot refreshed successfully
[[17:57:51]] [SUCCESS] Screenshot refreshed successfully
[[17:57:51]] [INFO] Executing action 386/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:57:50]] [SUCCESS] Screenshot refreshed
[[17:57:50]] [INFO] Refreshing screenshot...
[[17:57:47]] [SUCCESS] Screenshot refreshed successfully
[[17:57:47]] [SUCCESS] Screenshot refreshed successfully
[[17:57:47]] [INFO] Executing action 385/512: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[17:57:46]] [SUCCESS] Screenshot refreshed
[[17:57:46]] [INFO] Refreshing screenshot...
[[17:57:40]] [SUCCESS] Screenshot refreshed successfully
[[17:57:40]] [SUCCESS] Screenshot refreshed successfully
[[17:57:40]] [INFO] Executing action 384/512: Tap on element with accessibility_id: Add to bag
[[17:57:40]] [SUCCESS] Screenshot refreshed
[[17:57:40]] [INFO] Refreshing screenshot...
[[17:57:33]] [SUCCESS] Screenshot refreshed successfully
[[17:57:33]] [SUCCESS] Screenshot refreshed successfully
[[17:57:33]] [INFO] Executing action 383/512: swipeTillVisible action
[[17:57:32]] [SUCCESS] Screenshot refreshed
[[17:57:32]] [INFO] Refreshing screenshot...
[[17:57:31]] [SUCCESS] Screenshot refreshed successfully
[[17:57:31]] [SUCCESS] Screenshot refreshed successfully
[[17:57:30]] [INFO] Executing action 382/512: Add Log: Post code successfully changed in PDP (with screenshot)
[[17:57:30]] [SUCCESS] Screenshot refreshed
[[17:57:30]] [INFO] Refreshing screenshot...
[[17:57:26]] [SUCCESS] Screenshot refreshed successfully
[[17:57:26]] [SUCCESS] Screenshot refreshed successfully
[[17:57:26]] [INFO] Executing action 381/512: Check if image "shop-broadway-se.png" exists on screen
[[17:57:25]] [SUCCESS] Screenshot refreshed
[[17:57:25]] [INFO] Refreshing screenshot...
[[17:57:21]] [SUCCESS] Screenshot refreshed successfully
[[17:57:21]] [SUCCESS] Screenshot refreshed successfully
[[17:57:21]] [INFO] Executing action 380/512: Tap on Text: "Save"
[[17:57:20]] [SUCCESS] Screenshot refreshed
[[17:57:20]] [INFO] Refreshing screenshot...
[[17:57:16]] [SUCCESS] Screenshot refreshed successfully
[[17:57:16]] [SUCCESS] Screenshot refreshed successfully
[[17:57:16]] [INFO] Executing action 379/512: Wait till accessibility_id=btnSaveOrContinue
[[17:57:16]] [SUCCESS] Screenshot refreshed
[[17:57:16]] [INFO] Refreshing screenshot...
[[17:57:11]] [SUCCESS] Screenshot refreshed successfully
[[17:57:11]] [SUCCESS] Screenshot refreshed successfully
[[17:57:11]] [INFO] Executing action 378/512: Tap on Text: "2000"
[[17:57:10]] [SUCCESS] Screenshot refreshed
[[17:57:10]] [INFO] Refreshing screenshot...
[[17:57:06]] [SUCCESS] Screenshot refreshed successfully
[[17:57:06]] [SUCCESS] Screenshot refreshed successfully
[[17:57:06]] [INFO] Executing action 377/512: textClear action
[[17:57:06]] [SUCCESS] Screenshot refreshed
[[17:57:06]] [INFO] Refreshing screenshot...
[[17:57:01]] [SUCCESS] Screenshot refreshed successfully
[[17:57:01]] [SUCCESS] Screenshot refreshed successfully
[[17:57:01]] [INFO] Executing action 376/512: Tap on element with accessibility_id: Search suburb or postcode
[[17:57:00]] [SUCCESS] Screenshot refreshed
[[17:57:00]] [INFO] Refreshing screenshot...
[[17:56:55]] [SUCCESS] Screenshot refreshed successfully
[[17:56:55]] [SUCCESS] Screenshot refreshed successfully
[[17:56:55]] [INFO] Executing action 375/512: Tap on Text: "Edit"
[[17:56:54]] [SUCCESS] Screenshot refreshed
[[17:56:54]] [INFO] Refreshing screenshot...
[[17:56:51]] [SUCCESS] Screenshot refreshed successfully
[[17:56:51]] [SUCCESS] Screenshot refreshed successfully
[[17:56:51]] [INFO] Executing action 374/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[17:56:50]] [SUCCESS] Screenshot refreshed
[[17:56:50]] [INFO] Refreshing screenshot...
[[17:56:46]] [SUCCESS] Screenshot refreshed successfully
[[17:56:46]] [SUCCESS] Screenshot refreshed successfully
[[17:56:46]] [INFO] Executing action 373/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:56:45]] [SUCCESS] Screenshot refreshed
[[17:56:45]] [INFO] Refreshing screenshot...
[[17:56:44]] [SUCCESS] Screenshot refreshed successfully
[[17:56:44]] [SUCCESS] Screenshot refreshed successfully
[[17:56:43]] [INFO] Executing action 372/512: Add Log: Post code successfully changed in PLP (with screenshot)
[[17:56:43]] [SUCCESS] Screenshot refreshed
[[17:56:43]] [INFO] Refreshing screenshot...
[[17:56:39]] [SUCCESS] Screenshot refreshed successfully
[[17:56:39]] [SUCCESS] Screenshot refreshed successfully
[[17:56:39]] [INFO] Executing action 371/512: Check if image "SANCTURYLAKE-SE.png" exists on screen
[[17:56:38]] [SUCCESS] Screenshot refreshed
[[17:56:38]] [INFO] Refreshing screenshot...
[[17:56:34]] [SUCCESS] Screenshot refreshed successfully
[[17:56:34]] [SUCCESS] Screenshot refreshed successfully
[[17:56:33]] [INFO] Executing action 370/512: Tap on Text: "Save"
[[17:56:33]] [SUCCESS] Screenshot refreshed
[[17:56:33]] [INFO] Refreshing screenshot...
[[17:56:29]] [SUCCESS] Screenshot refreshed successfully
[[17:56:29]] [SUCCESS] Screenshot refreshed successfully
[[17:56:29]] [INFO] Executing action 369/512: Wait till accessibility_id=btnSaveOrContinue
[[17:56:28]] [SUCCESS] Screenshot refreshed
[[17:56:28]] [INFO] Refreshing screenshot...
[[17:56:24]] [SUCCESS] Screenshot refreshed successfully
[[17:56:24]] [SUCCESS] Screenshot refreshed successfully
[[17:56:24]] [INFO] Executing action 368/512: Tap on Text: "current"
[[17:56:23]] [SUCCESS] Screenshot refreshed
[[17:56:23]] [INFO] Refreshing screenshot...
[[17:56:19]] [SUCCESS] Screenshot refreshed successfully
[[17:56:19]] [SUCCESS] Screenshot refreshed successfully
[[17:56:19]] [INFO] Executing action 367/512: Wait till accessibility_id=btnCurrentLocationButton
[[17:56:19]] [SUCCESS] Screenshot refreshed
[[17:56:19]] [INFO] Refreshing screenshot...
[[17:56:14]] [SUCCESS] Screenshot refreshed successfully
[[17:56:14]] [SUCCESS] Screenshot refreshed successfully
[[17:56:13]] [INFO] Executing action 366/512: Tap on Text: "Edit"
[[17:56:13]] [SUCCESS] Screenshot refreshed
[[17:56:13]] [INFO] Refreshing screenshot...
[[17:56:10]] [SUCCESS] Screenshot refreshed successfully
[[17:56:10]] [SUCCESS] Screenshot refreshed successfully
[[17:56:09]] [INFO] Executing action 365/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:56:09]] [SUCCESS] Screenshot refreshed
[[17:56:09]] [INFO] Refreshing screenshot...
[[17:56:05]] [SUCCESS] Screenshot refreshed successfully
[[17:56:05]] [SUCCESS] Screenshot refreshed successfully
[[17:56:05]] [INFO] Executing action 364/512: iOS Function: text
[[17:56:05]] [SUCCESS] Screenshot refreshed
[[17:56:05]] [INFO] Refreshing screenshot...
[[17:56:00]] [SUCCESS] Screenshot refreshed successfully
[[17:56:00]] [SUCCESS] Screenshot refreshed successfully
[[17:55:59]] [INFO] Executing action 363/512: Tap on Text: "Find"
[[17:55:59]] [SUCCESS] Screenshot refreshed
[[17:55:59]] [INFO] Refreshing screenshot...
[[17:55:57]] [SUCCESS] Screenshot refreshed successfully
[[17:55:57]] [SUCCESS] Screenshot refreshed successfully
[[17:55:56]] [INFO] Executing action 362/512: Add Log: Post code successfully changed to broadway from home page (with screenshot)
[[17:55:56]] [SUCCESS] Screenshot refreshed
[[17:55:56]] [INFO] Refreshing screenshot...
[[17:55:51]] [SUCCESS] Screenshot refreshed successfully
[[17:55:51]] [SUCCESS] Screenshot refreshed successfully
[[17:55:50]] [INFO] Executing action 361/512: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[17:55:50]] [SUCCESS] Screenshot refreshed
[[17:55:50]] [INFO] Refreshing screenshot...
[[17:55:45]] [SUCCESS] Screenshot refreshed successfully
[[17:55:45]] [SUCCESS] Screenshot refreshed successfully
[[17:55:45]] [INFO] Executing action 360/512: Tap on Text: "Save"
[[17:55:45]] [SUCCESS] Screenshot refreshed
[[17:55:45]] [INFO] Refreshing screenshot...
[[17:55:41]] [SUCCESS] Screenshot refreshed successfully
[[17:55:41]] [SUCCESS] Screenshot refreshed successfully
[[17:55:40]] [INFO] Executing action 359/512: Wait till accessibility_id=btnSaveOrContinue
[[17:55:40]] [SUCCESS] Screenshot refreshed
[[17:55:40]] [INFO] Refreshing screenshot...
[[17:55:35]] [SUCCESS] Screenshot refreshed successfully
[[17:55:35]] [SUCCESS] Screenshot refreshed successfully
[[17:55:35]] [INFO] Executing action 358/512: Tap on Text: "2000"
[[17:55:35]] [SUCCESS] Screenshot refreshed
[[17:55:35]] [INFO] Refreshing screenshot...
[[17:55:30]] [SUCCESS] Screenshot refreshed successfully
[[17:55:30]] [SUCCESS] Screenshot refreshed successfully
[[17:55:30]] [INFO] Executing action 357/512: textClear action
[[17:55:30]] [SUCCESS] Screenshot refreshed
[[17:55:30]] [INFO] Refreshing screenshot...
[[17:55:25]] [SUCCESS] Screenshot refreshed successfully
[[17:55:25]] [SUCCESS] Screenshot refreshed successfully
[[17:55:25]] [INFO] Executing action 356/512: Tap on element with accessibility_id: Search suburb or postcode
[[17:55:25]] [SUCCESS] Screenshot refreshed
[[17:55:25]] [INFO] Refreshing screenshot...
[[17:55:22]] [SUCCESS] Screenshot refreshed successfully
[[17:55:22]] [SUCCESS] Screenshot refreshed successfully
[[17:55:21]] [INFO] Executing action 355/512: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[17:55:21]] [SUCCESS] Screenshot refreshed
[[17:55:21]] [INFO] Refreshing screenshot...
[[17:55:19]] [SUCCESS] Screenshot refreshed successfully
[[17:55:19]] [SUCCESS] Screenshot refreshed successfully
[[17:55:17]] [INFO] Executing action 354/512: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[17:55:17]] [SUCCESS] Screenshot refreshed
[[17:55:17]] [INFO] Refreshing screenshot...
[[17:55:16]] [SUCCESS] Screenshot refreshed
[[17:55:16]] [INFO] Refreshing screenshot...
[[17:55:14]] [SUCCESS] Screenshot refreshed successfully
[[17:55:14]] [SUCCESS] Screenshot refreshed successfully
[[17:55:13]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[17:55:13]] [SUCCESS] Screenshot refreshed
[[17:55:13]] [INFO] Refreshing screenshot...
[[17:55:08]] [SUCCESS] Screenshot refreshed successfully
[[17:55:08]] [SUCCESS] Screenshot refreshed successfully
[[17:55:08]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[17:55:08]] [SUCCESS] Screenshot refreshed
[[17:55:08]] [INFO] Refreshing screenshot...
[[17:55:04]] [SUCCESS] Screenshot refreshed successfully
[[17:55:04]] [SUCCESS] Screenshot refreshed successfully
[[17:55:04]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:55:03]] [SUCCESS] Screenshot refreshed
[[17:55:03]] [INFO] Refreshing screenshot...
[[17:54:59]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[17:54:59]] [SUCCESS] Screenshot refreshed successfully
[[17:54:59]] [SUCCESS] Screenshot refreshed successfully
[[17:54:58]] [SUCCESS] Screenshot refreshed
[[17:54:58]] [INFO] Refreshing screenshot...
[[17:54:55]] [SUCCESS] Screenshot refreshed successfully
[[17:54:55]] [SUCCESS] Screenshot refreshed successfully
[[17:54:55]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:54:54]] [SUCCESS] Screenshot refreshed
[[17:54:54]] [INFO] Refreshing screenshot...
[[17:54:49]] [SUCCESS] Screenshot refreshed successfully
[[17:54:49]] [SUCCESS] Screenshot refreshed successfully
[[17:54:49]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:54:49]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[17:54:49]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[17:54:49]] [INFO] Executing action 353/512: Execute Test Case: Kmart-Signin (6 steps)
[[17:54:48]] [SUCCESS] Screenshot refreshed
[[17:54:48]] [INFO] Refreshing screenshot...
[[17:54:45]] [SUCCESS] Screenshot refreshed successfully
[[17:54:45]] [SUCCESS] Screenshot refreshed successfully
[[17:54:45]] [INFO] Executing action 352/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:54:44]] [SUCCESS] Screenshot refreshed
[[17:54:44]] [INFO] Refreshing screenshot...
[[17:54:42]] [SUCCESS] Screenshot refreshed successfully
[[17:54:42]] [SUCCESS] Screenshot refreshed successfully
[[17:54:41]] [INFO] Executing action 351/512: iOS Function: alert_accept
[[17:54:41]] [SUCCESS] Screenshot refreshed
[[17:54:41]] [INFO] Refreshing screenshot...
[[17:54:35]] [SUCCESS] Screenshot refreshed successfully
[[17:54:35]] [SUCCESS] Screenshot refreshed successfully
[[17:54:34]] [INFO] Executing action 350/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:54:34]] [SUCCESS] Screenshot refreshed
[[17:54:34]] [INFO] Refreshing screenshot...
[[17:54:20]] [SUCCESS] Screenshot refreshed successfully
[[17:54:20]] [SUCCESS] Screenshot refreshed successfully
[[17:54:20]] [INFO] Executing action 349/512: Restart app: env[appid]
[[17:54:19]] [SUCCESS] Screenshot refreshed
[[17:54:19]] [INFO] Refreshing screenshot...
[[17:54:16]] [SUCCESS] Screenshot refreshed successfully
[[17:54:16]] [SUCCESS] Screenshot refreshed successfully
[[17:54:16]] [INFO] Executing action 348/512: Terminate app: env[appid]
[[17:54:15]] [SUCCESS] Screenshot refreshed
[[17:54:15]] [INFO] Refreshing screenshot...
[[17:54:12]] [SUCCESS] Screenshot refreshed successfully
[[17:54:12]] [SUCCESS] Screenshot refreshed successfully
[[17:54:12]] [INFO] Executing action 347/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:54:11]] [SUCCESS] Screenshot refreshed
[[17:54:11]] [INFO] Refreshing screenshot...
[[17:54:08]] [SUCCESS] Screenshot refreshed successfully
[[17:54:08]] [SUCCESS] Screenshot refreshed successfully
[[17:54:08]] [INFO] Executing action 346/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:54:07]] [SUCCESS] Screenshot refreshed
[[17:54:07]] [INFO] Refreshing screenshot...
[[17:54:05]] [SUCCESS] Screenshot refreshed successfully
[[17:54:05]] [SUCCESS] Screenshot refreshed successfully
[[17:54:03]] [INFO] Executing action 345/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:54:03]] [SUCCESS] Screenshot refreshed
[[17:54:03]] [INFO] Refreshing screenshot...
[[17:54:03]] [SUCCESS] Screenshot refreshed
[[17:54:03]] [INFO] Refreshing screenshot...
[[17:53:59]] [SUCCESS] Screenshot refreshed successfully
[[17:53:59]] [SUCCESS] Screenshot refreshed successfully
[[17:53:59]] [INFO] Executing Multi Step action step 41/41: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[17:53:59]] [SUCCESS] Screenshot refreshed
[[17:53:59]] [INFO] Refreshing screenshot...
[[17:53:55]] [SUCCESS] Screenshot refreshed successfully
[[17:53:55]] [SUCCESS] Screenshot refreshed successfully
[[17:53:55]] [INFO] Executing Multi Step action step 40/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:53:54]] [SUCCESS] Screenshot refreshed
[[17:53:54]] [INFO] Refreshing screenshot...
[[17:53:49]] [SUCCESS] Screenshot refreshed successfully
[[17:53:49]] [SUCCESS] Screenshot refreshed successfully
[[17:53:48]] [INFO] Executing Multi Step action step 39/41: swipeTillVisible action
[[17:53:48]] [SUCCESS] Screenshot refreshed
[[17:53:48]] [INFO] Refreshing screenshot...
[[17:53:45]] [INFO] Executing Multi Step action step 38/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[17:53:45]] [SUCCESS] Screenshot refreshed successfully
[[17:53:45]] [SUCCESS] Screenshot refreshed successfully
[[17:53:44]] [SUCCESS] Screenshot refreshed
[[17:53:44]] [INFO] Refreshing screenshot...
[[17:53:41]] [SUCCESS] Screenshot refreshed successfully
[[17:53:41]] [SUCCESS] Screenshot refreshed successfully
[[17:53:41]] [INFO] Executing Multi Step action step 37/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:53:40]] [SUCCESS] Screenshot refreshed
[[17:53:40]] [INFO] Refreshing screenshot...
[[17:53:36]] [SUCCESS] Screenshot refreshed successfully
[[17:53:36]] [SUCCESS] Screenshot refreshed successfully
[[17:53:36]] [INFO] Executing Multi Step action step 36/41: Tap on image: banner-close-updated.png
[[17:53:35]] [SUCCESS] Screenshot refreshed
[[17:53:35]] [INFO] Refreshing screenshot...
[[17:53:33]] [SUCCESS] Screenshot refreshed successfully
[[17:53:33]] [SUCCESS] Screenshot refreshed successfully
[[17:53:33]] [INFO] Executing Multi Step action step 35/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[17:53:32]] [SUCCESS] Screenshot refreshed
[[17:53:32]] [INFO] Refreshing screenshot...
[[17:53:29]] [SUCCESS] Screenshot refreshed successfully
[[17:53:29]] [SUCCESS] Screenshot refreshed successfully
[[17:53:28]] [INFO] Executing Multi Step action step 34/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[17:53:28]] [SUCCESS] Screenshot refreshed
[[17:53:28]] [INFO] Refreshing screenshot...
[[17:53:24]] [SUCCESS] Screenshot refreshed successfully
[[17:53:24]] [SUCCESS] Screenshot refreshed successfully
[[17:53:24]] [INFO] Executing Multi Step action step 33/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[17:53:24]] [SUCCESS] Screenshot refreshed
[[17:53:24]] [INFO] Refreshing screenshot...
[[17:53:20]] [SUCCESS] Screenshot refreshed successfully
[[17:53:20]] [SUCCESS] Screenshot refreshed successfully
[[17:53:20]] [INFO] Executing Multi Step action step 32/41: Tap on image: banner-close-updated.png
[[17:53:19]] [SUCCESS] Screenshot refreshed
[[17:53:19]] [INFO] Refreshing screenshot...
[[17:53:14]] [INFO] Executing Multi Step action step 31/41: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[17:53:14]] [SUCCESS] Screenshot refreshed successfully
[[17:53:14]] [SUCCESS] Screenshot refreshed successfully
[[17:53:14]] [SUCCESS] Screenshot refreshed
[[17:53:14]] [INFO] Refreshing screenshot...
[[17:53:10]] [SUCCESS] Screenshot refreshed successfully
[[17:53:10]] [SUCCESS] Screenshot refreshed successfully
[[17:53:10]] [INFO] Executing Multi Step action step 30/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[17:53:10]] [SUCCESS] Screenshot refreshed
[[17:53:10]] [INFO] Refreshing screenshot...
[[17:53:06]] [SUCCESS] Screenshot refreshed successfully
[[17:53:06]] [SUCCESS] Screenshot refreshed successfully
[[17:53:06]] [INFO] Executing Multi Step action step 29/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[17:53:05]] [SUCCESS] Screenshot refreshed
[[17:53:05]] [INFO] Refreshing screenshot...
[[17:53:02]] [SUCCESS] Screenshot refreshed successfully
[[17:53:02]] [SUCCESS] Screenshot refreshed successfully
[[17:53:02]] [INFO] Executing Multi Step action step 28/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[17:53:01]] [SUCCESS] Screenshot refreshed
[[17:53:01]] [INFO] Refreshing screenshot...
[[17:52:58]] [SUCCESS] Screenshot refreshed successfully
[[17:52:58]] [SUCCESS] Screenshot refreshed successfully
[[17:52:58]] [INFO] Executing Multi Step action step 27/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[17:52:58]] [SUCCESS] Screenshot refreshed
[[17:52:58]] [INFO] Refreshing screenshot...
[[17:52:54]] [SUCCESS] Screenshot refreshed successfully
[[17:52:54]] [SUCCESS] Screenshot refreshed successfully
[[17:52:54]] [INFO] Executing Multi Step action step 26/41: Tap on element with xpath: //XCUIElementTypeOther[@name="PayPal, main"]/XCUIElementTypeOther
[[17:52:53]] [SUCCESS] Screenshot refreshed
[[17:52:53]] [INFO] Refreshing screenshot...
[[17:52:50]] [SUCCESS] Screenshot refreshed successfully
[[17:52:50]] [SUCCESS] Screenshot refreshed successfully
[[17:52:50]] [INFO] Executing Multi Step action step 25/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[17:52:49]] [SUCCESS] Screenshot refreshed
[[17:52:49]] [INFO] Refreshing screenshot...
[[17:52:46]] [SUCCESS] Screenshot refreshed successfully
[[17:52:46]] [SUCCESS] Screenshot refreshed successfully
[[17:52:45]] [INFO] Executing Multi Step action step 24/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[17:52:45]] [SUCCESS] Screenshot refreshed
[[17:52:45]] [INFO] Refreshing screenshot...
[[17:52:33]] [SUCCESS] Screenshot refreshed successfully
[[17:52:33]] [SUCCESS] Screenshot refreshed successfully
[[17:52:33]] [INFO] Executing Multi Step action step 23/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay with PayPal"]" exists
[[17:52:32]] [SUCCESS] Screenshot refreshed
[[17:52:32]] [INFO] Refreshing screenshot...
[[17:52:29]] [SUCCESS] Screenshot refreshed successfully
[[17:52:29]] [SUCCESS] Screenshot refreshed successfully
[[17:52:28]] [INFO] Executing Multi Step action step 22/41: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[17:52:28]] [SUCCESS] Screenshot refreshed
[[17:52:28]] [INFO] Refreshing screenshot...
[[17:52:21]] [SUCCESS] Screenshot refreshed successfully
[[17:52:21]] [SUCCESS] Screenshot refreshed successfully
[[17:52:21]] [INFO] Executing Multi Step action step 21/41: swipeTillVisible action
[[17:52:20]] [SUCCESS] Screenshot refreshed
[[17:52:20]] [INFO] Refreshing screenshot...
[[17:52:17]] [SUCCESS] Screenshot refreshed successfully
[[17:52:17]] [SUCCESS] Screenshot refreshed successfully
[[17:52:16]] [INFO] Executing Multi Step action step 20/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[17:52:16]] [SUCCESS] Screenshot refreshed
[[17:52:16]] [INFO] Refreshing screenshot...
[[17:52:12]] [SUCCESS] Screenshot refreshed successfully
[[17:52:12]] [SUCCESS] Screenshot refreshed successfully
[[17:52:12]] [INFO] Executing Multi Step action step 19/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[17:52:12]] [SUCCESS] Screenshot refreshed
[[17:52:12]] [INFO] Refreshing screenshot...
[[17:52:04]] [SUCCESS] Screenshot refreshed successfully
[[17:52:04]] [SUCCESS] Screenshot refreshed successfully
[[17:52:04]] [INFO] Executing Multi Step action step 18/41: swipeTillVisible action
[[17:52:04]] [SUCCESS] Screenshot refreshed
[[17:52:04]] [INFO] Refreshing screenshot...
[[17:52:00]] [SUCCESS] Screenshot refreshed successfully
[[17:52:00]] [SUCCESS] Screenshot refreshed successfully
[[17:52:00]] [INFO] Executing Multi Step action step 17/41: Tap on image: env[delivery-address-img]
[[17:51:59]] [SUCCESS] Screenshot refreshed
[[17:51:59]] [INFO] Refreshing screenshot...
[[17:51:56]] [SUCCESS] Screenshot refreshed successfully
[[17:51:56]] [SUCCESS] Screenshot refreshed successfully
[[17:51:56]] [INFO] Executing Multi Step action step 16/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[17:51:55]] [SUCCESS] Screenshot refreshed
[[17:51:55]] [INFO] Refreshing screenshot...
[[17:51:48]] [SUCCESS] Screenshot refreshed successfully
[[17:51:48]] [SUCCESS] Screenshot refreshed successfully
[[17:51:48]] [INFO] Executing Multi Step action step 15/41: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "env[deliver-address]"
[[17:51:47]] [SUCCESS] Screenshot refreshed
[[17:51:47]] [INFO] Refreshing screenshot...
[[17:51:42]] [SUCCESS] Screenshot refreshed successfully
[[17:51:42]] [SUCCESS] Screenshot refreshed successfully
[[17:51:42]] [INFO] Executing Multi Step action step 14/41: Tap on Text: "address"
[[17:51:42]] [SUCCESS] Screenshot refreshed
[[17:51:42]] [INFO] Refreshing screenshot...
[[17:51:38]] [SUCCESS] Screenshot refreshed successfully
[[17:51:38]] [SUCCESS] Screenshot refreshed successfully
[[17:51:38]] [INFO] Executing Multi Step action step 13/41: iOS Function: text
[[17:51:37]] [SUCCESS] Screenshot refreshed
[[17:51:37]] [INFO] Refreshing screenshot...
[[17:51:31]] [SUCCESS] Screenshot refreshed successfully
[[17:51:31]] [SUCCESS] Screenshot refreshed successfully
[[17:51:31]] [INFO] Executing Multi Step action step 12/41: textClear action
[[17:51:31]] [SUCCESS] Screenshot refreshed
[[17:51:31]] [INFO] Refreshing screenshot...
[[17:51:27]] [SUCCESS] Screenshot refreshed successfully
[[17:51:27]] [SUCCESS] Screenshot refreshed successfully
[[17:51:27]] [INFO] Executing Multi Step action step 11/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[17:51:26]] [SUCCESS] Screenshot refreshed
[[17:51:26]] [INFO] Refreshing screenshot...
[[17:51:20]] [SUCCESS] Screenshot refreshed successfully
[[17:51:20]] [SUCCESS] Screenshot refreshed successfully
[[17:51:20]] [INFO] Executing Multi Step action step 10/41: textClear action
[[17:51:19]] [SUCCESS] Screenshot refreshed
[[17:51:19]] [INFO] Refreshing screenshot...
[[17:51:16]] [SUCCESS] Screenshot refreshed successfully
[[17:51:16]] [SUCCESS] Screenshot refreshed successfully
[[17:51:16]] [INFO] Executing Multi Step action step 9/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:51:15]] [SUCCESS] Screenshot refreshed
[[17:51:15]] [INFO] Refreshing screenshot...
[[17:51:09]] [SUCCESS] Screenshot refreshed successfully
[[17:51:09]] [SUCCESS] Screenshot refreshed successfully
[[17:51:09]] [INFO] Executing Multi Step action step 8/41: textClear action
[[17:51:09]] [SUCCESS] Screenshot refreshed
[[17:51:09]] [INFO] Refreshing screenshot...
[[17:51:05]] [SUCCESS] Screenshot refreshed successfully
[[17:51:05]] [SUCCESS] Screenshot refreshed successfully
[[17:51:05]] [INFO] Executing Multi Step action step 7/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[17:51:04]] [SUCCESS] Screenshot refreshed
[[17:51:04]] [INFO] Refreshing screenshot...
[[17:50:58]] [SUCCESS] Screenshot refreshed successfully
[[17:50:58]] [SUCCESS] Screenshot refreshed successfully
[[17:50:58]] [INFO] Executing Multi Step action step 6/41: textClear action
[[17:50:58]] [SUCCESS] Screenshot refreshed
[[17:50:58]] [INFO] Refreshing screenshot...
[[17:50:54]] [SUCCESS] Screenshot refreshed successfully
[[17:50:54]] [SUCCESS] Screenshot refreshed successfully
[[17:50:54]] [INFO] Executing Multi Step action step 5/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[17:50:54]] [SUCCESS] Screenshot refreshed
[[17:50:54]] [INFO] Refreshing screenshot...
[[17:50:50]] [SUCCESS] Screenshot refreshed successfully
[[17:50:50]] [SUCCESS] Screenshot refreshed successfully
[[17:50:50]] [INFO] Executing Multi Step action step 4/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[17:50:49]] [SUCCESS] Screenshot refreshed
[[17:50:49]] [INFO] Refreshing screenshot...
[[17:50:31]] [SUCCESS] Screenshot refreshed successfully
[[17:50:31]] [SUCCESS] Screenshot refreshed successfully
[[17:50:31]] [INFO] Executing Multi Step action step 3/41: swipeTillVisible action
[[17:50:30]] [SUCCESS] Screenshot refreshed
[[17:50:30]] [INFO] Refreshing screenshot...
[[17:50:27]] [SUCCESS] Screenshot refreshed successfully
[[17:50:27]] [SUCCESS] Screenshot refreshed successfully
[[17:50:27]] [INFO] Executing Multi Step action step 2/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[17:50:26]] [SUCCESS] Screenshot refreshed
[[17:50:26]] [INFO] Refreshing screenshot...
[[17:50:21]] [INFO] Executing Multi Step action step 1/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[17:50:21]] [INFO] Loaded 41 steps from test case: Delivery Buy Steps
[[17:50:21]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps
[[17:50:21]] [INFO] Executing action 344/512: Execute Test Case: Delivery Buy Steps (41 steps)
[[17:50:21]] [SUCCESS] Screenshot refreshed successfully
[[17:50:21]] [SUCCESS] Screenshot refreshed successfully
[[17:50:20]] [SUCCESS] Screenshot refreshed
[[17:50:20]] [INFO] Refreshing screenshot...
[[17:50:18]] [SUCCESS] Screenshot refreshed successfully
[[17:50:18]] [SUCCESS] Screenshot refreshed successfully
[[17:50:17]] [INFO] Executing action 343/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:50:17]] [SUCCESS] Screenshot refreshed
[[17:50:17]] [INFO] Refreshing screenshot...
[[17:50:12]] [SUCCESS] Screenshot refreshed successfully
[[17:50:12]] [SUCCESS] Screenshot refreshed successfully
[[17:50:11]] [INFO] Executing action 342/512: Restart app: env[appid]
[[17:50:11]] [SUCCESS] Screenshot refreshed
[[17:50:11]] [INFO] Refreshing screenshot...
[[17:49:59]] [SUCCESS] Screenshot refreshed successfully
[[17:49:59]] [SUCCESS] Screenshot refreshed successfully
[[17:49:58]] [INFO] Executing action 341/512: If exists: xpath="//XCUIElementTypeButton[@name="Save my location"]" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Save my location"]
[[17:49:58]] [SUCCESS] Screenshot refreshed
[[17:49:58]] [INFO] Refreshing screenshot...
[[17:49:54]] [SUCCESS] Screenshot refreshed successfully
[[17:49:54]] [SUCCESS] Screenshot refreshed successfully
[[17:49:54]] [INFO] Executing action 340/512: Tap on image: env[atg-pdp]
[[17:49:53]] [SUCCESS] Screenshot refreshed
[[17:49:53]] [INFO] Refreshing screenshot...
[[17:49:50]] [SUCCESS] Screenshot refreshed successfully
[[17:49:50]] [SUCCESS] Screenshot refreshed successfully
[[17:49:50]] [INFO] Executing action 339/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:49:49]] [SUCCESS] Screenshot refreshed
[[17:49:49]] [INFO] Refreshing screenshot...
[[17:49:46]] [SUCCESS] Screenshot refreshed successfully
[[17:49:46]] [SUCCESS] Screenshot refreshed successfully
[[17:49:46]] [INFO] Executing action 338/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:49:46]] [SUCCESS] Screenshot refreshed
[[17:49:46]] [INFO] Refreshing screenshot...
[[17:49:42]] [SUCCESS] Screenshot refreshed successfully
[[17:49:42]] [SUCCESS] Screenshot refreshed successfully
[[17:49:42]] [INFO] Executing action 337/512: iOS Function: text
[[17:49:41]] [SUCCESS] Screenshot refreshed
[[17:49:41]] [INFO] Refreshing screenshot...
[[17:49:36]] [SUCCESS] Screenshot refreshed successfully
[[17:49:36]] [SUCCESS] Screenshot refreshed successfully
[[17:49:36]] [INFO] Executing action 336/512: Tap on Text: "Find"
[[17:49:35]] [SUCCESS] Screenshot refreshed
[[17:49:35]] [INFO] Refreshing screenshot...
[[17:49:31]] [SUCCESS] Screenshot refreshed successfully
[[17:49:31]] [SUCCESS] Screenshot refreshed successfully
[[17:49:31]] [INFO] Executing action 335/512: Tap on image: env[device-back-img]
[[17:49:31]] [SUCCESS] Screenshot refreshed
[[17:49:31]] [INFO] Refreshing screenshot...
[[17:49:08]] [SUCCESS] Screenshot refreshed successfully
[[17:49:08]] [SUCCESS] Screenshot refreshed successfully
[[17:49:08]] [INFO] Executing action 334/512: If exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="btnUpdate"]
[[17:49:08]] [SUCCESS] Screenshot refreshed
[[17:49:07]] [INFO] Refreshing screenshot...
[[17:49:03]] [SUCCESS] Screenshot refreshed successfully
[[17:49:03]] [SUCCESS] Screenshot refreshed successfully
[[17:49:03]] [INFO] Executing action 333/512: Tap on Text: "current"
[[17:49:02]] [SUCCESS] Screenshot refreshed
[[17:49:02]] [INFO] Refreshing screenshot...
[[17:48:58]] [SUCCESS] Screenshot refreshed successfully
[[17:48:58]] [SUCCESS] Screenshot refreshed successfully
[[17:48:57]] [INFO] Executing action 332/512: Tap on Text: "Edit"
[[17:48:57]] [SUCCESS] Screenshot refreshed
[[17:48:57]] [INFO] Refreshing screenshot...
[[17:48:52]] [SUCCESS] Screenshot refreshed successfully
[[17:48:52]] [SUCCESS] Screenshot refreshed successfully
[[17:48:52]] [INFO] Executing action 331/512: Restart app: env[appid]
[[17:48:51]] [SUCCESS] Screenshot refreshed
[[17:48:51]] [INFO] Refreshing screenshot...
[[17:48:47]] [SUCCESS] Screenshot refreshed successfully
[[17:48:47]] [SUCCESS] Screenshot refreshed successfully
[[17:48:47]] [INFO] Executing action 330/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[17:48:46]] [SUCCESS] Screenshot refreshed
[[17:48:46]] [INFO] Refreshing screenshot...
[[17:48:43]] [SUCCESS] Screenshot refreshed successfully
[[17:48:43]] [SUCCESS] Screenshot refreshed successfully
[[17:48:43]] [INFO] Executing action 329/512: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[17:48:42]] [SUCCESS] Screenshot refreshed
[[17:48:42]] [INFO] Refreshing screenshot...
[[17:48:39]] [SUCCESS] Screenshot refreshed successfully
[[17:48:39]] [SUCCESS] Screenshot refreshed successfully
[[17:48:39]] [INFO] Executing action 328/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[17:48:38]] [SUCCESS] Screenshot refreshed
[[17:48:38]] [INFO] Refreshing screenshot...
[[17:48:34]] [SUCCESS] Screenshot refreshed successfully
[[17:48:34]] [SUCCESS] Screenshot refreshed successfully
[[17:48:34]] [INFO] Executing action 327/512: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[17:48:33]] [SUCCESS] Screenshot refreshed
[[17:48:33]] [INFO] Refreshing screenshot...
[[17:48:29]] [SUCCESS] Screenshot refreshed successfully
[[17:48:29]] [SUCCESS] Screenshot refreshed successfully
[[17:48:29]] [INFO] Executing action 326/512: Tap on Text: "Store"
[[17:48:28]] [SUCCESS] Screenshot refreshed
[[17:48:28]] [INFO] Refreshing screenshot...
[[17:48:24]] [SUCCESS] Screenshot refreshed successfully
[[17:48:24]] [SUCCESS] Screenshot refreshed successfully
[[17:48:24]] [INFO] Executing action 325/512: Tap on Text: "receipts"
[[17:48:24]] [SUCCESS] Screenshot refreshed
[[17:48:24]] [INFO] Refreshing screenshot...
[[17:48:20]] [SUCCESS] Screenshot refreshed successfully
[[17:48:20]] [SUCCESS] Screenshot refreshed successfully
[[17:48:20]] [INFO] Executing action 324/512: Tap on image: env[device-back-img]
[[17:48:19]] [SUCCESS] Screenshot refreshed
[[17:48:19]] [INFO] Refreshing screenshot...
[[17:48:17]] [SUCCESS] Screenshot refreshed successfully
[[17:48:17]] [SUCCESS] Screenshot refreshed successfully
[[17:48:17]] [INFO] Executing action 323/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[17:48:16]] [SUCCESS] Screenshot refreshed
[[17:48:16]] [INFO] Refreshing screenshot...
[[17:48:12]] [SUCCESS] Screenshot refreshed successfully
[[17:48:12]] [SUCCESS] Screenshot refreshed successfully
[[17:48:12]] [INFO] Executing action 322/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[17:48:12]] [SUCCESS] Screenshot refreshed
[[17:48:12]] [INFO] Refreshing screenshot...
[[17:48:08]] [SUCCESS] Screenshot refreshed successfully
[[17:48:08]] [SUCCESS] Screenshot refreshed successfully
[[17:48:08]] [INFO] Executing action 321/512: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[17:48:08]] [SUCCESS] Screenshot refreshed
[[17:48:08]] [INFO] Refreshing screenshot...
[[17:48:03]] [SUCCESS] Screenshot refreshed successfully
[[17:48:03]] [SUCCESS] Screenshot refreshed successfully
[[17:48:03]] [INFO] Executing action 320/512: iOS Function: text
[[17:48:03]] [SUCCESS] Screenshot refreshed
[[17:48:03]] [INFO] Refreshing screenshot...
[[17:47:59]] [SUCCESS] Screenshot refreshed successfully
[[17:47:59]] [SUCCESS] Screenshot refreshed successfully
[[17:47:59]] [INFO] Executing action 319/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:47:59]] [SUCCESS] Screenshot refreshed
[[17:47:59]] [INFO] Refreshing screenshot...
[[17:47:54]] [SUCCESS] Screenshot refreshed successfully
[[17:47:54]] [SUCCESS] Screenshot refreshed successfully
[[17:47:54]] [INFO] Executing action 318/512: iOS Function: text
[[17:47:54]] [SUCCESS] Screenshot refreshed
[[17:47:54]] [INFO] Refreshing screenshot...
[[17:47:50]] [SUCCESS] Screenshot refreshed successfully
[[17:47:50]] [SUCCESS] Screenshot refreshed successfully
[[17:47:50]] [INFO] Executing action 317/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:47:49]] [SUCCESS] Screenshot refreshed
[[17:47:49]] [INFO] Refreshing screenshot...
[[17:47:47]] [SUCCESS] Screenshot refreshed successfully
[[17:47:47]] [SUCCESS] Screenshot refreshed successfully
[[17:47:47]] [INFO] Executing action 316/512: iOS Function: alert_accept
[[17:47:46]] [SUCCESS] Screenshot refreshed
[[17:47:46]] [INFO] Refreshing screenshot...
[[17:47:43]] [SUCCESS] Screenshot refreshed successfully
[[17:47:43]] [SUCCESS] Screenshot refreshed successfully
[[17:47:43]] [INFO] Executing action 315/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[17:47:42]] [SUCCESS] Screenshot refreshed
[[17:47:42]] [INFO] Refreshing screenshot...
[[17:47:38]] [SUCCESS] Screenshot refreshed successfully
[[17:47:38]] [SUCCESS] Screenshot refreshed successfully
[[17:47:38]] [INFO] Executing action 314/512: Tap on image: env[device-back-img]
[[17:47:38]] [SUCCESS] Screenshot refreshed
[[17:47:38]] [INFO] Refreshing screenshot...
[[17:47:35]] [SUCCESS] Screenshot refreshed successfully
[[17:47:35]] [SUCCESS] Screenshot refreshed successfully
[[17:47:35]] [INFO] Executing action 313/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[17:47:35]] [SUCCESS] Screenshot refreshed
[[17:47:35]] [INFO] Refreshing screenshot...
[[17:47:31]] [SUCCESS] Screenshot refreshed successfully
[[17:47:31]] [SUCCESS] Screenshot refreshed successfully
[[17:47:31]] [INFO] Executing action 312/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[17:47:31]] [SUCCESS] Screenshot refreshed
[[17:47:31]] [INFO] Refreshing screenshot...
[[17:47:28]] [SUCCESS] Screenshot refreshed successfully
[[17:47:28]] [SUCCESS] Screenshot refreshed successfully
[[17:47:28]] [INFO] Executing action 311/512: Input text: "env[uname-op]"
[[17:47:27]] [SUCCESS] Screenshot refreshed
[[17:47:27]] [INFO] Refreshing screenshot...
[[17:47:24]] [SUCCESS] Screenshot refreshed successfully
[[17:47:24]] [SUCCESS] Screenshot refreshed successfully
[[17:47:23]] [INFO] Executing action 310/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[17:47:23]] [SUCCESS] Screenshot refreshed
[[17:47:23]] [INFO] Refreshing screenshot...
[[17:47:20]] [SUCCESS] Screenshot refreshed successfully
[[17:47:20]] [SUCCESS] Screenshot refreshed successfully
[[17:47:20]] [INFO] Executing action 309/512: Input text: "env[searchorder]"
[[17:47:20]] [SUCCESS] Screenshot refreshed
[[17:47:20]] [INFO] Refreshing screenshot...
[[17:47:16]] [INFO] Executing action 308/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[17:47:16]] [SUCCESS] Screenshot refreshed successfully
[[17:47:16]] [SUCCESS] Screenshot refreshed successfully
[[17:47:16]] [SUCCESS] Screenshot refreshed
[[17:47:16]] [INFO] Refreshing screenshot...
[[17:47:12]] [SUCCESS] Screenshot refreshed successfully
[[17:47:12]] [SUCCESS] Screenshot refreshed successfully
[[17:47:12]] [INFO] Executing action 307/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[17:47:12]] [SUCCESS] Screenshot refreshed
[[17:47:12]] [INFO] Refreshing screenshot...
[[17:47:09]] [SUCCESS] Screenshot refreshed successfully
[[17:47:09]] [SUCCESS] Screenshot refreshed successfully
[[17:47:08]] [INFO] Executing action 306/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:47:08]] [SUCCESS] Screenshot refreshed
[[17:47:08]] [INFO] Refreshing screenshot...
[[17:47:04]] [SUCCESS] Screenshot refreshed successfully
[[17:47:04]] [SUCCESS] Screenshot refreshed successfully
[[17:47:03]] [INFO] Executing action 305/512: Tap on image: env[device-back-img]
[[17:47:02]] [SUCCESS] Screenshot refreshed
[[17:47:02]] [INFO] Refreshing screenshot...
[[17:47:00]] [SUCCESS] Screenshot refreshed successfully
[[17:47:00]] [SUCCESS] Screenshot refreshed successfully
[[17:46:58]] [INFO] Executing action 304/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[17:46:58]] [SUCCESS] Screenshot refreshed
[[17:46:58]] [INFO] Refreshing screenshot...
[[17:46:55]] [SUCCESS] Screenshot refreshed successfully
[[17:46:55]] [SUCCESS] Screenshot refreshed successfully
[[17:46:54]] [INFO] Executing action 303/512: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[17:46:53]] [SUCCESS] Screenshot refreshed
[[17:46:53]] [INFO] Refreshing screenshot...
[[17:46:51]] [SUCCESS] Screenshot refreshed successfully
[[17:46:51]] [SUCCESS] Screenshot refreshed successfully
[[17:46:49]] [INFO] Executing action 302/512: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[17:46:49]] [SUCCESS] Screenshot refreshed
[[17:46:49]] [INFO] Refreshing screenshot...
[[17:46:47]] [SUCCESS] Screenshot refreshed successfully
[[17:46:47]] [SUCCESS] Screenshot refreshed successfully
[[17:46:45]] [INFO] Executing action 301/512: iOS Function: alert_accept
[[17:46:45]] [SUCCESS] Screenshot refreshed
[[17:46:45]] [INFO] Refreshing screenshot...
[[17:46:40]] [SUCCESS] Screenshot refreshed successfully
[[17:46:40]] [SUCCESS] Screenshot refreshed successfully
[[17:46:40]] [INFO] Executing action 300/512: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[17:46:39]] [SUCCESS] Screenshot refreshed
[[17:46:39]] [INFO] Refreshing screenshot...
[[17:46:25]] [SUCCESS] Screenshot refreshed successfully
[[17:46:25]] [SUCCESS] Screenshot refreshed successfully
[[17:46:24]] [INFO] Executing action 299/512: Restart app: env[appid]
[[17:46:24]] [SUCCESS] Screenshot refreshed
[[17:46:24]] [INFO] Refreshing screenshot...
[[17:46:19]] [SUCCESS] Screenshot refreshed successfully
[[17:46:19]] [SUCCESS] Screenshot refreshed successfully
[[17:46:19]] [INFO] Executing action 298/512: Tap on Text: "out"
[[17:46:18]] [SUCCESS] Screenshot refreshed
[[17:46:18]] [INFO] Refreshing screenshot...
[[17:46:13]] [SUCCESS] Screenshot refreshed successfully
[[17:46:13]] [SUCCESS] Screenshot refreshed successfully
[[17:46:13]] [INFO] Executing action 297/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:46:13]] [SUCCESS] Screenshot refreshed
[[17:46:13]] [INFO] Refreshing screenshot...
[[17:46:09]] [SUCCESS] Screenshot refreshed successfully
[[17:46:09]] [SUCCESS] Screenshot refreshed successfully
[[17:46:09]] [INFO] Executing action 296/512: Tap on image: env[device-back-img]
[[17:46:09]] [SUCCESS] Screenshot refreshed
[[17:46:09]] [INFO] Refreshing screenshot...
[[17:46:04]] [SUCCESS] Screenshot refreshed successfully
[[17:46:04]] [SUCCESS] Screenshot refreshed successfully
[[17:46:04]] [INFO] Executing action 295/512: Tap on Text: "Customer"
[[17:46:04]] [SUCCESS] Screenshot refreshed
[[17:46:04]] [INFO] Refreshing screenshot...
[[17:46:00]] [SUCCESS] Screenshot refreshed successfully
[[17:46:00]] [SUCCESS] Screenshot refreshed successfully
[[17:45:59]] [INFO] Executing action 294/512: Tap on image: banner-close-updated.png
[[17:45:59]] [SUCCESS] Screenshot refreshed
[[17:45:59]] [INFO] Refreshing screenshot...
[[17:45:56]] [SUCCESS] Screenshot refreshed successfully
[[17:45:56]] [SUCCESS] Screenshot refreshed successfully
[[17:45:56]] [INFO] Executing action 293/512: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[17:45:55]] [SUCCESS] Screenshot refreshed
[[17:45:55]] [INFO] Refreshing screenshot...
[[17:45:50]] [SUCCESS] Screenshot refreshed successfully
[[17:45:50]] [SUCCESS] Screenshot refreshed successfully
[[17:45:50]] [INFO] Executing action 292/512: Tap on Text: "Invite"
[[17:45:50]] [SUCCESS] Screenshot refreshed
[[17:45:50]] [INFO] Refreshing screenshot...
[[17:45:46]] [SUCCESS] Screenshot refreshed successfully
[[17:45:46]] [SUCCESS] Screenshot refreshed successfully
[[17:45:46]] [INFO] Executing action 291/512: Tap on image: env[device-back-img]
[[17:45:45]] [SUCCESS] Screenshot refreshed
[[17:45:45]] [INFO] Refreshing screenshot...
[[17:45:43]] [SUCCESS] Screenshot refreshed successfully
[[17:45:43]] [SUCCESS] Screenshot refreshed successfully
[[17:45:43]] [INFO] Executing action 290/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[17:45:42]] [SUCCESS] Screenshot refreshed
[[17:45:42]] [INFO] Refreshing screenshot...
[[17:45:37]] [SUCCESS] Screenshot refreshed successfully
[[17:45:37]] [SUCCESS] Screenshot refreshed successfully
[[17:45:37]] [INFO] Executing action 289/512: Tap on Text: "VIC"
[[17:45:37]] [SUCCESS] Screenshot refreshed
[[17:45:37]] [INFO] Refreshing screenshot...
[[17:45:30]] [SUCCESS] Screenshot refreshed successfully
[[17:45:30]] [SUCCESS] Screenshot refreshed successfully
[[17:45:30]] [INFO] Executing action 288/512: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[17:45:29]] [SUCCESS] Screenshot refreshed
[[17:45:29]] [INFO] Refreshing screenshot...
[[17:45:25]] [SUCCESS] Screenshot refreshed successfully
[[17:45:25]] [SUCCESS] Screenshot refreshed successfully
[[17:45:25]] [INFO] Executing action 287/512: Tap on Text: "Nearby"
[[17:45:24]] [SUCCESS] Screenshot refreshed
[[17:45:24]] [INFO] Refreshing screenshot...
[[17:45:01]] [SUCCESS] Screenshot refreshed successfully
[[17:45:01]] [SUCCESS] Screenshot refreshed successfully
[[17:45:01]] [INFO] Executing action 286/512: Tap on Text: "locator"
[[17:45:00]] [SUCCESS] Screenshot refreshed
[[17:45:00]] [INFO] Refreshing screenshot...
[[17:44:55]] [SUCCESS] Screenshot refreshed successfully
[[17:44:55]] [SUCCESS] Screenshot refreshed successfully
[[17:44:55]] [INFO] Executing action 285/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:44:54]] [SUCCESS] Screenshot refreshed
[[17:44:54]] [INFO] Refreshing screenshot...
[[17:44:42]] [SUCCESS] Screenshot refreshed successfully
[[17:44:42]] [SUCCESS] Screenshot refreshed successfully
[[17:44:42]] [INFO] Executing action 284/512: Check if element with accessibility_id="txtMy Flybuys card" exists
[[17:44:41]] [SUCCESS] Screenshot refreshed
[[17:44:41]] [INFO] Refreshing screenshot...
[[17:44:38]] [SUCCESS] Screenshot refreshed successfully
[[17:44:38]] [SUCCESS] Screenshot refreshed successfully
[[17:44:37]] [INFO] Executing action 283/512: Tap on image: env[device-back-img]
[[17:44:37]] [SUCCESS] Screenshot refreshed
[[17:44:37]] [INFO] Refreshing screenshot...
[[17:44:22]] [SUCCESS] Screenshot refreshed successfully
[[17:44:22]] [SUCCESS] Screenshot refreshed successfully
[[17:44:21]] [INFO] Executing action 282/512: Tap on element with accessibility_id: btnSaveFlybuysCard
[[17:44:21]] [SUCCESS] Screenshot refreshed
[[17:44:21]] [INFO] Refreshing screenshot...
[[17:44:16]] [SUCCESS] Screenshot refreshed successfully
[[17:44:16]] [SUCCESS] Screenshot refreshed successfully
[[17:44:16]] [INFO] Executing action 281/512: Tap on element with accessibility_id: Done
[[17:44:16]] [SUCCESS] Screenshot refreshed
[[17:44:16]] [INFO] Refreshing screenshot...
[[17:44:13]] [SUCCESS] Screenshot refreshed successfully
[[17:44:13]] [SUCCESS] Screenshot refreshed successfully
[[17:44:13]] [INFO] Executing action 280/512: Input text: "FlyBuy Number"
[[17:44:12]] [SUCCESS] Screenshot refreshed
[[17:44:12]] [INFO] Refreshing screenshot...
[[17:44:07]] [SUCCESS] Screenshot refreshed successfully
[[17:44:07]] [SUCCESS] Screenshot refreshed successfully
[[17:44:07]] [INFO] Executing action 279/512: Tap on element with accessibility_id: Flybuys barcode number
[[17:44:07]] [SUCCESS] Screenshot refreshed
[[17:44:07]] [INFO] Refreshing screenshot...
[[17:44:02]] [SUCCESS] Screenshot refreshed successfully
[[17:44:02]] [SUCCESS] Screenshot refreshed successfully
[[17:44:02]] [INFO] Executing action 278/512: Tap on element with accessibility_id: btnLinkFlyBuys
[[17:44:02]] [SUCCESS] Screenshot refreshed
[[17:44:02]] [INFO] Refreshing screenshot...
[[17:43:57]] [SUCCESS] Screenshot refreshed successfully
[[17:43:57]] [SUCCESS] Screenshot refreshed successfully
[[17:43:57]] [INFO] Executing action 277/512: Tap on Text: "Flybuys"
[[17:43:56]] [SUCCESS] Screenshot refreshed
[[17:43:56]] [INFO] Refreshing screenshot...
[[17:43:52]] [SUCCESS] Screenshot refreshed successfully
[[17:43:52]] [SUCCESS] Screenshot refreshed successfully
[[17:43:52]] [INFO] Executing action 276/512: Tap on element with accessibility_id: btnRemove
[[17:43:51]] [SUCCESS] Screenshot refreshed
[[17:43:51]] [INFO] Refreshing screenshot...
[[17:43:46]] [SUCCESS] Screenshot refreshed successfully
[[17:43:46]] [SUCCESS] Screenshot refreshed successfully
[[17:43:46]] [INFO] Executing action 275/512: Tap on element with accessibility_id: Remove card
[[17:43:46]] [SUCCESS] Screenshot refreshed
[[17:43:46]] [INFO] Refreshing screenshot...
[[17:43:41]] [SUCCESS] Screenshot refreshed successfully
[[17:43:41]] [SUCCESS] Screenshot refreshed successfully
[[17:43:41]] [INFO] Executing action 274/512: Tap on element with accessibility_id: btneditFlybuysCard
[[17:43:40]] [SUCCESS] Screenshot refreshed
[[17:43:40]] [INFO] Refreshing screenshot...
[[17:43:36]] [SUCCESS] Screenshot refreshed successfully
[[17:43:36]] [SUCCESS] Screenshot refreshed successfully
[[17:43:36]] [INFO] Executing action 273/512: Wait till accessibility_id=btneditFlybuysCard
[[17:43:36]] [SUCCESS] Screenshot refreshed
[[17:43:36]] [INFO] Refreshing screenshot...
[[17:43:31]] [SUCCESS] Screenshot refreshed successfully
[[17:43:31]] [SUCCESS] Screenshot refreshed successfully
[[17:43:31]] [INFO] Executing action 272/512: Tap on Text: "Flybuys"
[[17:43:31]] [SUCCESS] Screenshot refreshed
[[17:43:31]] [INFO] Refreshing screenshot...
[[17:43:27]] [SUCCESS] Screenshot refreshed successfully
[[17:43:27]] [SUCCESS] Screenshot refreshed successfully
[[17:43:27]] [INFO] Executing action 271/512: Tap on image: env[device-back-img]
[[17:43:27]] [SUCCESS] Screenshot refreshed
[[17:43:27]] [INFO] Refreshing screenshot...
[[17:43:23]] [SUCCESS] Screenshot refreshed successfully
[[17:43:23]] [SUCCESS] Screenshot refreshed successfully
[[17:43:23]] [INFO] Executing action 270/512: Tap on image: env[device-back-img]
[[17:43:22]] [SUCCESS] Screenshot refreshed
[[17:43:22]] [INFO] Refreshing screenshot...
[[17:43:18]] [SUCCESS] Screenshot refreshed successfully
[[17:43:18]] [SUCCESS] Screenshot refreshed successfully
[[17:43:18]] [INFO] Executing action 269/512: Tap on Text: "payment"
[[17:43:18]] [SUCCESS] Screenshot refreshed
[[17:43:18]] [INFO] Refreshing screenshot...
[[17:43:14]] [SUCCESS] Screenshot refreshed successfully
[[17:43:14]] [SUCCESS] Screenshot refreshed successfully
[[17:43:14]] [INFO] Executing action 268/512: Tap on image: env[device-back-img]
[[17:43:13]] [SUCCESS] Screenshot refreshed
[[17:43:13]] [INFO] Refreshing screenshot...
[[17:43:09]] [SUCCESS] Screenshot refreshed successfully
[[17:43:09]] [SUCCESS] Screenshot refreshed successfully
[[17:43:09]] [INFO] Executing action 267/512: Tap on Text: "address"
[[17:43:09]] [SUCCESS] Screenshot refreshed
[[17:43:09]] [INFO] Refreshing screenshot...
[[17:43:05]] [SUCCESS] Screenshot refreshed successfully
[[17:43:05]] [SUCCESS] Screenshot refreshed successfully
[[17:43:05]] [INFO] Executing action 266/512: Tap on image: env[device-back-img]
[[17:43:04]] [SUCCESS] Screenshot refreshed
[[17:43:04]] [INFO] Refreshing screenshot...
[[17:43:00]] [SUCCESS] Screenshot refreshed successfully
[[17:43:00]] [SUCCESS] Screenshot refreshed successfully
[[17:43:00]] [INFO] Executing action 265/512: Tap on Text: "details"
[[17:43:00]] [SUCCESS] Screenshot refreshed
[[17:43:00]] [INFO] Refreshing screenshot...
[[17:42:56]] [SUCCESS] Screenshot refreshed successfully
[[17:42:56]] [SUCCESS] Screenshot refreshed successfully
[[17:42:56]] [INFO] Executing action 264/512: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[17:42:56]] [SUCCESS] Screenshot refreshed
[[17:42:56]] [INFO] Refreshing screenshot...
[[17:42:53]] [SUCCESS] Screenshot refreshed successfully
[[17:42:53]] [SUCCESS] Screenshot refreshed successfully
[[17:42:52]] [INFO] Executing action 263/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:42:52]] [SUCCESS] Screenshot refreshed
[[17:42:52]] [INFO] Refreshing screenshot...
[[17:42:48]] [SUCCESS] Screenshot refreshed successfully
[[17:42:48]] [SUCCESS] Screenshot refreshed successfully
[[17:42:47]] [INFO] Executing action 262/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[17:42:47]] [SUCCESS] Screenshot refreshed
[[17:42:47]] [INFO] Refreshing screenshot...
[[17:42:42]] [SUCCESS] Screenshot refreshed successfully
[[17:42:42]] [SUCCESS] Screenshot refreshed successfully
[[17:42:42]] [INFO] Executing action 261/512: Tap on Text: "Return"
[[17:42:42]] [SUCCESS] Screenshot refreshed
[[17:42:42]] [INFO] Refreshing screenshot...
[[17:42:35]] [SUCCESS] Screenshot refreshed successfully
[[17:42:35]] [SUCCESS] Screenshot refreshed successfully
[[17:42:35]] [INFO] Executing action 260/512: Wait for 5 ms
[[17:42:35]] [SUCCESS] Screenshot refreshed
[[17:42:35]] [INFO] Refreshing screenshot...
[[17:42:13]] [SUCCESS] Screenshot refreshed successfully
[[17:42:13]] [SUCCESS] Screenshot refreshed successfully
[[17:42:13]] [INFO] Executing action 259/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:42:12]] [SUCCESS] Screenshot refreshed
[[17:42:12]] [INFO] Refreshing screenshot...
[[17:42:09]] [SUCCESS] Screenshot refreshed successfully
[[17:42:09]] [SUCCESS] Screenshot refreshed successfully
[[17:42:08]] [INFO] Executing action 258/512: Tap on image: env[device-back-img]
[[17:42:08]] [SUCCESS] Screenshot refreshed
[[17:42:08]] [INFO] Refreshing screenshot...
[[17:42:05]] [INFO] Executing action 257/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[17:42:05]] [SUCCESS] Screenshot refreshed successfully
[[17:42:05]] [SUCCESS] Screenshot refreshed successfully
[[17:42:05]] [SUCCESS] Screenshot refreshed
[[17:42:05]] [INFO] Refreshing screenshot...
[[17:42:01]] [SUCCESS] Screenshot refreshed successfully
[[17:42:01]] [SUCCESS] Screenshot refreshed successfully
[[17:42:01]] [INFO] Executing action 256/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[17:42:01]] [SUCCESS] Screenshot refreshed
[[17:42:01]] [INFO] Refreshing screenshot...
[[17:41:58]] [SUCCESS] Screenshot refreshed successfully
[[17:41:58]] [SUCCESS] Screenshot refreshed successfully
[[17:41:58]] [INFO] Executing action 255/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[17:41:57]] [SUCCESS] Screenshot refreshed
[[17:41:57]] [INFO] Refreshing screenshot...
[[17:41:54]] [SUCCESS] Screenshot refreshed successfully
[[17:41:54]] [SUCCESS] Screenshot refreshed successfully
[[17:41:54]] [INFO] Executing action 254/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[17:41:53]] [SUCCESS] Screenshot refreshed
[[17:41:53]] [INFO] Refreshing screenshot...
[[17:41:47]] [SUCCESS] Screenshot refreshed successfully
[[17:41:47]] [SUCCESS] Screenshot refreshed successfully
[[17:41:47]] [INFO] Executing action 253/512: Wait for 5 ms
[[17:41:46]] [SUCCESS] Screenshot refreshed
[[17:41:46]] [INFO] Refreshing screenshot...
[[17:41:42]] [SUCCESS] Screenshot refreshed successfully
[[17:41:42]] [SUCCESS] Screenshot refreshed successfully
[[17:41:42]] [INFO] Executing action 252/512: Tap on image: env[device-back-img]
[[17:41:42]] [SUCCESS] Screenshot refreshed
[[17:41:42]] [INFO] Refreshing screenshot...
[[17:41:38]] [INFO] Executing action 251/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[17:41:38]] [SUCCESS] Screenshot refreshed successfully
[[17:41:38]] [SUCCESS] Screenshot refreshed successfully
[[17:41:37]] [SUCCESS] Screenshot refreshed
[[17:41:37]] [INFO] Refreshing screenshot...
[[17:41:33]] [SUCCESS] Screenshot refreshed successfully
[[17:41:33]] [SUCCESS] Screenshot refreshed successfully
[[17:41:32]] [INFO] Executing action 250/512: Tap on element with accessibility_id: Print order details
[[17:41:32]] [SUCCESS] Screenshot refreshed
[[17:41:32]] [INFO] Refreshing screenshot...
[[17:41:28]] [SUCCESS] Screenshot refreshed successfully
[[17:41:28]] [SUCCESS] Screenshot refreshed successfully
[[17:41:28]] [INFO] Executing action 249/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[17:41:28]] [SUCCESS] Screenshot refreshed
[[17:41:28]] [INFO] Refreshing screenshot...
[[17:41:14]] [INFO] Executing action 248/512: swipeTillVisible action
[[17:41:14]] [SUCCESS] Screenshot refreshed successfully
[[17:41:14]] [SUCCESS] Screenshot refreshed successfully
[[17:41:13]] [SUCCESS] Screenshot refreshed
[[17:41:13]] [INFO] Refreshing screenshot...
[[17:41:10]] [SUCCESS] Screenshot refreshed successfully
[[17:41:10]] [SUCCESS] Screenshot refreshed successfully
[[17:41:09]] [INFO] Executing action 247/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[17:41:09]] [SUCCESS] Screenshot refreshed
[[17:41:09]] [INFO] Refreshing screenshot...
[[17:41:02]] [SUCCESS] Screenshot refreshed successfully
[[17:41:02]] [SUCCESS] Screenshot refreshed successfully
[[17:41:02]] [INFO] Executing action 246/512: Wait for 5 ms
[[17:41:02]] [SUCCESS] Screenshot refreshed
[[17:41:02]] [INFO] Refreshing screenshot...
[[17:40:57]] [SUCCESS] Screenshot refreshed successfully
[[17:40:57]] [SUCCESS] Screenshot refreshed successfully
[[17:40:57]] [INFO] Executing action 245/512: Tap on Text: "receipts"
[[17:40:57]] [SUCCESS] Screenshot refreshed
[[17:40:57]] [INFO] Refreshing screenshot...
[[17:40:54]] [SUCCESS] Screenshot refreshed successfully
[[17:40:54]] [SUCCESS] Screenshot refreshed successfully
[[17:40:54]] [INFO] Executing action 244/512: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[17:40:53]] [SUCCESS] Screenshot refreshed
[[17:40:53]] [INFO] Refreshing screenshot...
[[17:40:52]] [SUCCESS] Screenshot refreshed successfully
[[17:40:52]] [SUCCESS] Screenshot refreshed successfully
[[17:40:50]] [INFO] Executing action 243/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:40:50]] [SUCCESS] Screenshot refreshed
[[17:40:50]] [INFO] Refreshing screenshot...
[[17:40:49]] [SUCCESS] Screenshot refreshed
[[17:40:49]] [INFO] Refreshing screenshot...
[[17:40:44]] [SUCCESS] Screenshot refreshed successfully
[[17:40:44]] [SUCCESS] Screenshot refreshed successfully
[[17:40:44]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[17:40:44]] [SUCCESS] Screenshot refreshed
[[17:40:44]] [INFO] Refreshing screenshot...
[[17:40:40]] [SUCCESS] Screenshot refreshed successfully
[[17:40:40]] [SUCCESS] Screenshot refreshed successfully
[[17:40:40]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[17:40:39]] [SUCCESS] Screenshot refreshed
[[17:40:39]] [INFO] Refreshing screenshot...
[[17:40:35]] [SUCCESS] Screenshot refreshed successfully
[[17:40:35]] [SUCCESS] Screenshot refreshed successfully
[[17:40:35]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:40:35]] [SUCCESS] Screenshot refreshed
[[17:40:35]] [INFO] Refreshing screenshot...
[[17:40:30]] [SUCCESS] Screenshot refreshed successfully
[[17:40:30]] [SUCCESS] Screenshot refreshed successfully
[[17:40:30]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[17:40:30]] [SUCCESS] Screenshot refreshed
[[17:40:30]] [INFO] Refreshing screenshot...
[[17:40:26]] [SUCCESS] Screenshot refreshed successfully
[[17:40:26]] [SUCCESS] Screenshot refreshed successfully
[[17:40:26]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:40:26]] [SUCCESS] Screenshot refreshed
[[17:40:26]] [INFO] Refreshing screenshot...
[[17:40:20]] [SUCCESS] Screenshot refreshed successfully
[[17:40:20]] [SUCCESS] Screenshot refreshed successfully
[[17:40:20]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:40:20]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[17:40:20]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[17:40:20]] [INFO] Executing action 242/512: Execute Test Case: Kmart-Signin (8 steps)
[[17:40:20]] [SUCCESS] Screenshot refreshed
[[17:40:20]] [INFO] Refreshing screenshot...
[[17:40:17]] [SUCCESS] Screenshot refreshed successfully
[[17:40:17]] [SUCCESS] Screenshot refreshed successfully
[[17:40:17]] [INFO] Executing action 241/512: iOS Function: alert_accept
[[17:40:16]] [SUCCESS] Screenshot refreshed
[[17:40:16]] [INFO] Refreshing screenshot...
[[17:40:10]] [SUCCESS] Screenshot refreshed successfully
[[17:40:10]] [SUCCESS] Screenshot refreshed successfully
[[17:40:10]] [INFO] Executing action 240/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:40:09]] [SUCCESS] Screenshot refreshed
[[17:40:09]] [INFO] Refreshing screenshot...
[[17:40:03]] [SUCCESS] Screenshot refreshed successfully
[[17:40:03]] [SUCCESS] Screenshot refreshed successfully
[[17:40:02]] [INFO] Executing action 239/512: Wait for 5 ms
[[17:40:02]] [SUCCESS] Screenshot refreshed
[[17:40:02]] [INFO] Refreshing screenshot...
[[17:39:48]] [SUCCESS] Screenshot refreshed successfully
[[17:39:48]] [SUCCESS] Screenshot refreshed successfully
[[17:39:48]] [INFO] Executing action 238/512: Restart app: env[appid]
[[17:39:47]] [SUCCESS] Screenshot refreshed
[[17:39:47]] [INFO] Refreshing screenshot...
[[17:39:47]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[17:39:47]] [INFO] Executing action 237/512: Hook Action: tap on image: banner-close-updated.png (Recovery)
[[17:39:46]] [SUCCESS] Screenshot refreshed
[[17:39:46]] [INFO] Refreshing screenshot...
[[17:39:44]] [SUCCESS] Screenshot refreshed successfully
[[17:39:44]] [SUCCESS] Screenshot refreshed successfully
[[17:39:43]] [INFO] Executing action 236/512: Terminate app: env[appid]
[[17:39:43]] [SUCCESS] Screenshot refreshed
[[17:39:43]] [INFO] Refreshing screenshot...
[[17:39:39]] [INFO] Executing action 235/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:39:39]] [SUCCESS] Screenshot refreshed successfully
[[17:39:39]] [SUCCESS] Screenshot refreshed successfully
[[17:39:38]] [SUCCESS] Screenshot refreshed
[[17:39:38]] [INFO] Refreshing screenshot...
[[17:39:33]] [SUCCESS] Screenshot refreshed successfully
[[17:39:33]] [SUCCESS] Screenshot refreshed successfully
[[17:39:33]] [INFO] Executing action 234/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:39:33]] [SUCCESS] Screenshot refreshed
[[17:39:33]] [INFO] Refreshing screenshot...
[[17:39:30]] [SUCCESS] Screenshot refreshed successfully
[[17:39:30]] [SUCCESS] Screenshot refreshed successfully
[[17:39:29]] [INFO] Executing action 233/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:39:29]] [SUCCESS] Screenshot refreshed
[[17:39:29]] [INFO] Refreshing screenshot...
[[17:39:24]] [INFO] Executing action 232/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[17:39:24]] [SUCCESS] Screenshot refreshed successfully
[[17:39:24]] [SUCCESS] Screenshot refreshed successfully
[[17:39:24]] [SUCCESS] Screenshot refreshed
[[17:39:24]] [INFO] Refreshing screenshot...
[[17:39:20]] [SUCCESS] Screenshot refreshed successfully
[[17:39:20]] [SUCCESS] Screenshot refreshed successfully
[[17:39:20]] [INFO] Executing action 231/512: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[17:39:19]] [SUCCESS] Screenshot refreshed
[[17:39:19]] [INFO] Refreshing screenshot...
[[17:39:15]] [SUCCESS] Screenshot refreshed successfully
[[17:39:15]] [SUCCESS] Screenshot refreshed successfully
[[17:39:15]] [INFO] Executing action 230/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[17:39:15]] [SUCCESS] Screenshot refreshed
[[17:39:15]] [INFO] Refreshing screenshot...
[[17:39:09]] [SUCCESS] Screenshot refreshed successfully
[[17:39:09]] [SUCCESS] Screenshot refreshed successfully
[[17:39:09]] [INFO] Executing action 229/512: swipeTillVisible action
[[17:39:08]] [SUCCESS] Screenshot refreshed
[[17:39:08]] [INFO] Refreshing screenshot...
[[17:39:05]] [SUCCESS] Screenshot refreshed successfully
[[17:39:05]] [SUCCESS] Screenshot refreshed successfully
[[17:39:05]] [INFO] Executing action 228/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:39:04]] [SUCCESS] Screenshot refreshed
[[17:39:04]] [INFO] Refreshing screenshot...
[[17:39:02]] [SUCCESS] Screenshot refreshed successfully
[[17:39:02]] [SUCCESS] Screenshot refreshed successfully
[[17:39:01]] [INFO] Executing action 227/512: iOS Function: alert_accept
[[17:39:01]] [SUCCESS] Screenshot refreshed
[[17:39:01]] [INFO] Refreshing screenshot...
[[17:38:56]] [SUCCESS] Screenshot refreshed successfully
[[17:38:56]] [SUCCESS] Screenshot refreshed successfully
[[17:38:55]] [INFO] Executing action 226/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:38:55]] [SUCCESS] Screenshot refreshed
[[17:38:55]] [INFO] Refreshing screenshot...
[[17:38:52]] [SUCCESS] Screenshot refreshed successfully
[[17:38:52]] [SUCCESS] Screenshot refreshed successfully
[[17:38:51]] [INFO] Executing action 225/512: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[17:38:51]] [SUCCESS] Screenshot refreshed
[[17:38:51]] [INFO] Refreshing screenshot...
[[17:38:47]] [SUCCESS] Screenshot refreshed successfully
[[17:38:47]] [SUCCESS] Screenshot refreshed successfully
[[17:38:47]] [INFO] Executing action 224/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:38:46]] [SUCCESS] Screenshot refreshed
[[17:38:46]] [INFO] Refreshing screenshot...
[[17:38:41]] [SUCCESS] Screenshot refreshed successfully
[[17:38:41]] [SUCCESS] Screenshot refreshed successfully
[[17:38:41]] [INFO] Executing action 223/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:38:41]] [SUCCESS] Screenshot refreshed
[[17:38:41]] [INFO] Refreshing screenshot...
[[17:38:38]] [SUCCESS] Screenshot refreshed successfully
[[17:38:38]] [SUCCESS] Screenshot refreshed successfully
[[17:38:37]] [INFO] Executing action 222/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:38:37]] [SUCCESS] Screenshot refreshed
[[17:38:37]] [INFO] Refreshing screenshot...
[[17:38:29]] [INFO] Executing action 221/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[17:38:29]] [SUCCESS] Screenshot refreshed successfully
[[17:38:29]] [SUCCESS] Screenshot refreshed successfully
[[17:38:29]] [SUCCESS] Screenshot refreshed
[[17:38:29]] [INFO] Refreshing screenshot...
[[17:38:26]] [INFO] Executing action 220/512: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[17:38:26]] [SUCCESS] Screenshot refreshed successfully
[[17:38:26]] [SUCCESS] Screenshot refreshed successfully
[[17:38:25]] [SUCCESS] Screenshot refreshed
[[17:38:25]] [INFO] Refreshing screenshot...
[[17:38:22]] [INFO] Executing action 219/512: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[17:38:22]] [SUCCESS] Screenshot refreshed successfully
[[17:38:22]] [SUCCESS] Screenshot refreshed successfully
[[17:38:22]] [SUCCESS] Screenshot refreshed
[[17:38:22]] [INFO] Refreshing screenshot...
[[17:38:19]] [INFO] Executing action 218/512: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[17:38:19]] [SUCCESS] Screenshot refreshed successfully
[[17:38:19]] [SUCCESS] Screenshot refreshed successfully
[[17:38:18]] [SUCCESS] Screenshot refreshed
[[17:38:18]] [INFO] Refreshing screenshot...
[[17:38:15]] [INFO] Executing action 217/512: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[17:38:15]] [SUCCESS] Screenshot refreshed successfully
[[17:38:15]] [SUCCESS] Screenshot refreshed successfully
[[17:38:15]] [SUCCESS] Screenshot refreshed
[[17:38:15]] [INFO] Refreshing screenshot...
[[17:38:12]] [INFO] Executing action 216/512: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[17:38:12]] [SUCCESS] Screenshot refreshed successfully
[[17:38:12]] [SUCCESS] Screenshot refreshed successfully
[[17:38:11]] [SUCCESS] Screenshot refreshed
[[17:38:11]] [INFO] Refreshing screenshot...
[[17:38:08]] [INFO] Executing action 215/512: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[17:38:08]] [SUCCESS] Screenshot refreshed successfully
[[17:38:08]] [SUCCESS] Screenshot refreshed successfully
[[17:38:08]] [SUCCESS] Screenshot refreshed
[[17:38:08]] [INFO] Refreshing screenshot...
[[17:38:05]] [INFO] Executing action 214/512: Tap on Text: "Passcode"
[[17:38:05]] [SUCCESS] Screenshot refreshed successfully
[[17:38:05]] [SUCCESS] Screenshot refreshed successfully
[[17:38:04]] [SUCCESS] Screenshot refreshed
[[17:38:04]] [INFO] Refreshing screenshot...
[[17:37:53]] [SUCCESS] Screenshot refreshed successfully
[[17:37:53]] [SUCCESS] Screenshot refreshed successfully
[[17:37:53]] [INFO] Executing action 213/512: Wait for 10 ms
[[17:37:52]] [SUCCESS] Screenshot refreshed
[[17:37:52]] [INFO] Refreshing screenshot...
[[17:37:47]] [SUCCESS] Screenshot refreshed successfully
[[17:37:47]] [SUCCESS] Screenshot refreshed successfully
[[17:37:47]] [INFO] Executing action 212/512: Tap on Text: "Apple"
[[17:37:47]] [SUCCESS] Screenshot refreshed
[[17:37:47]] [INFO] Refreshing screenshot...
[[17:37:43]] [SUCCESS] Screenshot refreshed successfully
[[17:37:43]] [SUCCESS] Screenshot refreshed successfully
[[17:37:43]] [INFO] Executing action 211/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:37:43]] [SUCCESS] Screenshot refreshed
[[17:37:43]] [INFO] Refreshing screenshot...
[[17:37:39]] [SUCCESS] Screenshot refreshed successfully
[[17:37:39]] [SUCCESS] Screenshot refreshed successfully
[[17:37:39]] [INFO] Executing action 210/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:37:39]] [SUCCESS] Screenshot refreshed
[[17:37:39]] [INFO] Refreshing screenshot...
[[17:37:36]] [SUCCESS] Screenshot refreshed successfully
[[17:37:36]] [SUCCESS] Screenshot refreshed successfully
[[17:37:36]] [INFO] Executing action 209/512: iOS Function: alert_accept
[[17:37:35]] [SUCCESS] Screenshot refreshed
[[17:37:35]] [INFO] Refreshing screenshot...
[[17:37:31]] [SUCCESS] Screenshot refreshed successfully
[[17:37:31]] [SUCCESS] Screenshot refreshed successfully
[[17:37:30]] [INFO] Executing action 208/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:37:30]] [SUCCESS] Screenshot refreshed
[[17:37:30]] [INFO] Refreshing screenshot...
[[17:37:26]] [SUCCESS] Screenshot refreshed successfully
[[17:37:26]] [SUCCESS] Screenshot refreshed successfully
[[17:37:26]] [INFO] Executing action 207/512: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[17:37:25]] [SUCCESS] Screenshot refreshed
[[17:37:25]] [INFO] Refreshing screenshot...
[[17:37:21]] [SUCCESS] Screenshot refreshed successfully
[[17:37:21]] [SUCCESS] Screenshot refreshed successfully
[[17:37:21]] [INFO] Executing action 206/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:37:21]] [SUCCESS] Screenshot refreshed
[[17:37:21]] [INFO] Refreshing screenshot...
[[17:37:16]] [SUCCESS] Screenshot refreshed successfully
[[17:37:16]] [SUCCESS] Screenshot refreshed successfully
[[17:37:16]] [INFO] Executing action 205/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:37:15]] [SUCCESS] Screenshot refreshed
[[17:37:15]] [INFO] Refreshing screenshot...
[[17:37:12]] [SUCCESS] Screenshot refreshed successfully
[[17:37:12]] [SUCCESS] Screenshot refreshed successfully
[[17:37:12]] [INFO] Executing action 204/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:37:11]] [SUCCESS] Screenshot refreshed
[[17:37:11]] [INFO] Refreshing screenshot...
[[17:37:08]] [SUCCESS] Screenshot refreshed successfully
[[17:37:08]] [SUCCESS] Screenshot refreshed successfully
[[17:37:08]] [INFO] Executing action 203/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[17:37:08]] [SUCCESS] Screenshot refreshed
[[17:37:08]] [INFO] Refreshing screenshot...
[[17:37:03]] [SUCCESS] Screenshot refreshed successfully
[[17:37:03]] [SUCCESS] Screenshot refreshed successfully
[[17:37:03]] [INFO] Executing action 202/512: iOS Function: text
[[17:37:02]] [SUCCESS] Screenshot refreshed
[[17:37:02]] [INFO] Refreshing screenshot...
[[17:36:58]] [SUCCESS] Screenshot refreshed successfully
[[17:36:58]] [SUCCESS] Screenshot refreshed successfully
[[17:36:58]] [INFO] Executing action 201/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[17:36:58]] [SUCCESS] Screenshot refreshed
[[17:36:58]] [INFO] Refreshing screenshot...
[[17:36:53]] [SUCCESS] Screenshot refreshed successfully
[[17:36:53]] [SUCCESS] Screenshot refreshed successfully
[[17:36:53]] [INFO] Executing action 200/512: iOS Function: text
[[17:36:53]] [SUCCESS] Screenshot refreshed
[[17:36:53]] [INFO] Refreshing screenshot...
[[17:36:49]] [SUCCESS] Screenshot refreshed successfully
[[17:36:49]] [SUCCESS] Screenshot refreshed successfully
[[17:36:49]] [INFO] Executing action 199/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[17:36:48]] [SUCCESS] Screenshot refreshed
[[17:36:48]] [INFO] Refreshing screenshot...
[[17:36:44]] [SUCCESS] Screenshot refreshed successfully
[[17:36:44]] [SUCCESS] Screenshot refreshed successfully
[[17:36:44]] [INFO] Executing action 198/512: Tap on Text: "OnePass"
[[17:36:43]] [SUCCESS] Screenshot refreshed
[[17:36:43]] [INFO] Refreshing screenshot...
[[17:36:40]] [SUCCESS] Screenshot refreshed successfully
[[17:36:40]] [SUCCESS] Screenshot refreshed successfully
[[17:36:39]] [INFO] Executing action 197/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:36:39]] [SUCCESS] Screenshot refreshed
[[17:36:39]] [INFO] Refreshing screenshot...
[[17:36:36]] [SUCCESS] Screenshot refreshed successfully
[[17:36:36]] [SUCCESS] Screenshot refreshed successfully
[[17:36:36]] [INFO] Executing action 196/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:36:35]] [SUCCESS] Screenshot refreshed
[[17:36:35]] [INFO] Refreshing screenshot...
[[17:36:33]] [SUCCESS] Screenshot refreshed successfully
[[17:36:33]] [SUCCESS] Screenshot refreshed successfully
[[17:36:32]] [INFO] Executing action 195/512: iOS Function: alert_accept
[[17:36:32]] [SUCCESS] Screenshot refreshed
[[17:36:32]] [INFO] Refreshing screenshot...
[[17:36:27]] [SUCCESS] Screenshot refreshed successfully
[[17:36:27]] [SUCCESS] Screenshot refreshed successfully
[[17:36:26]] [INFO] Executing action 194/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:36:26]] [SUCCESS] Screenshot refreshed
[[17:36:26]] [INFO] Refreshing screenshot...
[[17:36:23]] [SUCCESS] Screenshot refreshed successfully
[[17:36:23]] [SUCCESS] Screenshot refreshed successfully
[[17:36:22]] [INFO] Executing action 193/512: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[17:36:22]] [SUCCESS] Screenshot refreshed
[[17:36:22]] [INFO] Refreshing screenshot...
[[17:36:18]] [SUCCESS] Screenshot refreshed successfully
[[17:36:18]] [SUCCESS] Screenshot refreshed successfully
[[17:36:18]] [INFO] Executing action 192/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:36:17]] [SUCCESS] Screenshot refreshed
[[17:36:17]] [INFO] Refreshing screenshot...
[[17:36:12]] [SUCCESS] Screenshot refreshed successfully
[[17:36:12]] [SUCCESS] Screenshot refreshed successfully
[[17:36:12]] [INFO] Executing action 191/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:36:11]] [SUCCESS] Screenshot refreshed
[[17:36:11]] [INFO] Refreshing screenshot...
[[17:36:08]] [SUCCESS] Screenshot refreshed successfully
[[17:36:08]] [SUCCESS] Screenshot refreshed successfully
[[17:36:08]] [INFO] Executing action 190/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:36:07]] [SUCCESS] Screenshot refreshed
[[17:36:07]] [INFO] Refreshing screenshot...
[[17:36:05]] [SUCCESS] Screenshot refreshed successfully
[[17:36:05]] [SUCCESS] Screenshot refreshed successfully
[[17:36:04]] [INFO] Executing action 189/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[17:36:04]] [SUCCESS] Screenshot refreshed
[[17:36:04]] [INFO] Refreshing screenshot...
[[17:35:59]] [SUCCESS] Screenshot refreshed successfully
[[17:35:59]] [SUCCESS] Screenshot refreshed successfully
[[17:35:59]] [INFO] Executing action 188/512: iOS Function: text
[[17:35:59]] [SUCCESS] Screenshot refreshed
[[17:35:59]] [INFO] Refreshing screenshot...
[[17:35:55]] [SUCCESS] Screenshot refreshed successfully
[[17:35:55]] [SUCCESS] Screenshot refreshed successfully
[[17:35:55]] [INFO] Executing action 187/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:35:54]] [SUCCESS] Screenshot refreshed
[[17:35:54]] [INFO] Refreshing screenshot...
[[17:35:50]] [SUCCESS] Screenshot refreshed successfully
[[17:35:50]] [SUCCESS] Screenshot refreshed successfully
[[17:35:50]] [INFO] Executing action 186/512: iOS Function: text
[[17:35:49]] [SUCCESS] Screenshot refreshed
[[17:35:49]] [INFO] Refreshing screenshot...
[[17:35:46]] [SUCCESS] Screenshot refreshed successfully
[[17:35:46]] [SUCCESS] Screenshot refreshed successfully
[[17:35:46]] [INFO] Executing action 185/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:35:45]] [SUCCESS] Screenshot refreshed
[[17:35:45]] [INFO] Refreshing screenshot...
[[17:35:42]] [SUCCESS] Screenshot refreshed successfully
[[17:35:42]] [SUCCESS] Screenshot refreshed successfully
[[17:35:42]] [INFO] Executing action 184/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:35:41]] [SUCCESS] Screenshot refreshed
[[17:35:41]] [INFO] Refreshing screenshot...
[[17:35:39]] [SUCCESS] Screenshot refreshed successfully
[[17:35:39]] [SUCCESS] Screenshot refreshed successfully
[[17:35:38]] [INFO] Executing action 183/512: iOS Function: alert_accept
[[17:35:38]] [SUCCESS] Screenshot refreshed
[[17:35:38]] [INFO] Refreshing screenshot...
[[17:35:32]] [SUCCESS] Screenshot refreshed successfully
[[17:35:32]] [SUCCESS] Screenshot refreshed successfully
[[17:35:32]] [INFO] Executing action 182/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:35:31]] [SUCCESS] Screenshot refreshed
[[17:35:31]] [INFO] Refreshing screenshot...
[[17:35:16]] [SUCCESS] Screenshot refreshed successfully
[[17:35:16]] [SUCCESS] Screenshot refreshed successfully
[[17:35:16]] [INFO] Executing action 181/512: Restart app: env[appid]
[[17:35:15]] [SUCCESS] Screenshot refreshed
[[17:35:15]] [INFO] Refreshing screenshot...
[[17:35:11]] [SUCCESS] Screenshot refreshed successfully
[[17:35:11]] [SUCCESS] Screenshot refreshed successfully
[[17:35:11]] [INFO] Executing action 180/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:35:11]] [SUCCESS] Screenshot refreshed
[[17:35:11]] [INFO] Refreshing screenshot...
[[17:35:06]] [SUCCESS] Screenshot refreshed successfully
[[17:35:06]] [SUCCESS] Screenshot refreshed successfully
[[17:35:06]] [INFO] Executing action 179/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:35:05]] [SUCCESS] Screenshot refreshed
[[17:35:05]] [INFO] Refreshing screenshot...
[[17:35:02]] [SUCCESS] Screenshot refreshed successfully
[[17:35:02]] [SUCCESS] Screenshot refreshed successfully
[[17:35:02]] [INFO] Executing action 178/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:35:01]] [SUCCESS] Screenshot refreshed
[[17:35:01]] [INFO] Refreshing screenshot...
[[17:34:57]] [SUCCESS] Screenshot refreshed successfully
[[17:34:57]] [SUCCESS] Screenshot refreshed successfully
[[17:34:57]] [INFO] Executing action 177/512: Tap on Text: "Remove"
[[17:34:57]] [SUCCESS] Screenshot refreshed
[[17:34:57]] [INFO] Refreshing screenshot...
[[17:34:52]] [SUCCESS] Screenshot refreshed successfully
[[17:34:52]] [SUCCESS] Screenshot refreshed successfully
[[17:34:52]] [INFO] Executing action 176/512: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[17:34:52]] [SUCCESS] Screenshot refreshed
[[17:34:52]] [INFO] Refreshing screenshot...
[[17:34:48]] [SUCCESS] Screenshot refreshed successfully
[[17:34:48]] [SUCCESS] Screenshot refreshed successfully
[[17:34:48]] [INFO] Executing action 175/512: Tap on Text: "Remove"
[[17:34:47]] [SUCCESS] Screenshot refreshed
[[17:34:47]] [INFO] Refreshing screenshot...
[[17:34:43]] [SUCCESS] Screenshot refreshed successfully
[[17:34:43]] [SUCCESS] Screenshot refreshed successfully
[[17:34:43]] [INFO] Executing action 174/512: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[17:34:42]] [SUCCESS] Screenshot refreshed
[[17:34:42]] [INFO] Refreshing screenshot...
[[17:34:39]] [SUCCESS] Screenshot refreshed successfully
[[17:34:39]] [SUCCESS] Screenshot refreshed successfully
[[17:34:38]] [INFO] Executing action 173/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[17:34:38]] [SUCCESS] Screenshot refreshed
[[17:34:38]] [INFO] Refreshing screenshot...
[[17:34:34]] [SUCCESS] Screenshot refreshed successfully
[[17:34:34]] [SUCCESS] Screenshot refreshed successfully
[[17:34:34]] [INFO] Executing action 172/512: Tap on image: banner-close-updated.png
[[17:34:33]] [SUCCESS] Screenshot refreshed
[[17:34:33]] [INFO] Refreshing screenshot...
[[17:34:28]] [SUCCESS] Screenshot refreshed successfully
[[17:34:28]] [SUCCESS] Screenshot refreshed successfully
[[17:34:28]] [INFO] Executing action 171/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[17:34:27]] [SUCCESS] Screenshot refreshed
[[17:34:27]] [INFO] Refreshing screenshot...
[[17:34:24]] [SUCCESS] Screenshot refreshed successfully
[[17:34:24]] [SUCCESS] Screenshot refreshed successfully
[[17:34:24]] [INFO] Executing action 170/512: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[17:34:24]] [SUCCESS] Screenshot refreshed
[[17:34:24]] [INFO] Refreshing screenshot...
[[17:34:18]] [SUCCESS] Screenshot refreshed successfully
[[17:34:18]] [SUCCESS] Screenshot refreshed successfully
[[17:34:18]] [INFO] Executing action 169/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:34:18]] [SUCCESS] Screenshot refreshed
[[17:34:18]] [INFO] Refreshing screenshot...
[[17:34:14]] [SUCCESS] Screenshot refreshed successfully
[[17:34:14]] [SUCCESS] Screenshot refreshed successfully
[[17:34:14]] [INFO] Executing action 168/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:34:13]] [SUCCESS] Screenshot refreshed
[[17:34:13]] [INFO] Refreshing screenshot...
[[17:34:10]] [SUCCESS] Screenshot refreshed successfully
[[17:34:10]] [SUCCESS] Screenshot refreshed successfully
[[17:34:09]] [INFO] Executing action 167/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[17:34:09]] [SUCCESS] Screenshot refreshed
[[17:34:09]] [INFO] Refreshing screenshot...
[[17:34:05]] [SUCCESS] Screenshot refreshed successfully
[[17:34:05]] [SUCCESS] Screenshot refreshed successfully
[[17:34:05]] [INFO] Executing action 166/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[17:34:04]] [SUCCESS] Screenshot refreshed
[[17:34:04]] [INFO] Refreshing screenshot...
[[17:34:00]] [SUCCESS] Screenshot refreshed successfully
[[17:34:00]] [SUCCESS] Screenshot refreshed successfully
[[17:34:00]] [INFO] Executing action 165/512: Tap on Text: "Remove"
[[17:33:59]] [SUCCESS] Screenshot refreshed
[[17:33:59]] [INFO] Refreshing screenshot...
[[17:33:56]] [SUCCESS] Screenshot refreshed successfully
[[17:33:56]] [SUCCESS] Screenshot refreshed successfully
[[17:33:56]] [INFO] Executing action 164/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[17:33:56]] [SUCCESS] Screenshot refreshed
[[17:33:56]] [INFO] Refreshing screenshot...
[[17:33:51]] [SUCCESS] Screenshot refreshed successfully
[[17:33:51]] [SUCCESS] Screenshot refreshed successfully
[[17:33:51]] [INFO] Executing action 163/512: Tap on Text: "Move"
[[17:33:51]] [SUCCESS] Screenshot refreshed
[[17:33:51]] [INFO] Refreshing screenshot...
[[17:33:47]] [SUCCESS] Screenshot refreshed successfully
[[17:33:47]] [SUCCESS] Screenshot refreshed successfully
[[17:33:47]] [INFO] Executing action 162/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[17:33:47]] [SUCCESS] Screenshot refreshed
[[17:33:47]] [INFO] Refreshing screenshot...
[[17:33:44]] [SUCCESS] Screenshot refreshed successfully
[[17:33:44]] [SUCCESS] Screenshot refreshed successfully
[[17:33:44]] [INFO] Executing action 161/512: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[17:33:43]] [SUCCESS] Screenshot refreshed
[[17:33:43]] [INFO] Refreshing screenshot...
[[17:33:39]] [SUCCESS] Screenshot refreshed successfully
[[17:33:39]] [SUCCESS] Screenshot refreshed successfully
[[17:33:39]] [INFO] Executing action 160/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[17:33:39]] [SUCCESS] Screenshot refreshed
[[17:33:39]] [INFO] Refreshing screenshot...
[[17:33:35]] [SUCCESS] Screenshot refreshed successfully
[[17:33:35]] [SUCCESS] Screenshot refreshed successfully
[[17:33:35]] [INFO] Executing action 159/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[17:33:34]] [SUCCESS] Screenshot refreshed
[[17:33:34]] [INFO] Refreshing screenshot...
[[17:33:28]] [SUCCESS] Screenshot refreshed successfully
[[17:33:28]] [SUCCESS] Screenshot refreshed successfully
[[17:33:27]] [INFO] Executing action 158/512: swipeTillVisible action
[[17:33:27]] [SUCCESS] Screenshot refreshed
[[17:33:27]] [INFO] Refreshing screenshot...
[[17:33:23]] [INFO] Executing action 157/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[17:33:23]] [SUCCESS] Screenshot refreshed successfully
[[17:33:23]] [SUCCESS] Screenshot refreshed successfully
[[17:33:23]] [SUCCESS] Screenshot refreshed
[[17:33:23]] [INFO] Refreshing screenshot...
[[17:33:19]] [SUCCESS] Screenshot refreshed successfully
[[17:33:19]] [SUCCESS] Screenshot refreshed successfully
[[17:33:19]] [INFO] Executing action 156/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:33:19]] [SUCCESS] Screenshot refreshed
[[17:33:19]] [INFO] Refreshing screenshot...
[[17:33:15]] [SUCCESS] Screenshot refreshed successfully
[[17:33:15]] [SUCCESS] Screenshot refreshed successfully
[[17:33:15]] [INFO] Executing action 155/512: iOS Function: text
[[17:33:14]] [SUCCESS] Screenshot refreshed
[[17:33:14]] [INFO] Refreshing screenshot...
[[17:33:09]] [SUCCESS] Screenshot refreshed successfully
[[17:33:09]] [SUCCESS] Screenshot refreshed successfully
[[17:33:09]] [INFO] Executing action 154/512: Tap on Text: "Find"
[[17:33:08]] [SUCCESS] Screenshot refreshed
[[17:33:08]] [INFO] Refreshing screenshot...
[[17:33:03]] [SUCCESS] Screenshot refreshed successfully
[[17:33:03]] [SUCCESS] Screenshot refreshed successfully
[[17:33:03]] [INFO] Executing action 153/512: Restart app: env[appid]
[[17:33:03]] [SUCCESS] Screenshot refreshed
[[17:33:03]] [INFO] Refreshing screenshot...
[[17:32:59]] [SUCCESS] Screenshot refreshed successfully
[[17:32:59]] [SUCCESS] Screenshot refreshed successfully
[[17:32:59]] [INFO] Executing action 152/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[17:32:58]] [SUCCESS] Screenshot refreshed
[[17:32:58]] [INFO] Refreshing screenshot...
[[17:32:52]] [SUCCESS] Screenshot refreshed successfully
[[17:32:52]] [SUCCESS] Screenshot refreshed successfully
[[17:32:51]] [INFO] Executing action 151/512: swipeTillVisible action
[[17:32:51]] [SUCCESS] Screenshot refreshed
[[17:32:51]] [INFO] Refreshing screenshot...
[[17:32:48]] [SUCCESS] Screenshot refreshed successfully
[[17:32:48]] [SUCCESS] Screenshot refreshed successfully
[[17:32:47]] [INFO] Executing action 150/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[17:32:47]] [SUCCESS] Screenshot refreshed
[[17:32:47]] [INFO] Refreshing screenshot...
[[17:32:31]] [SUCCESS] Screenshot refreshed successfully
[[17:32:31]] [SUCCESS] Screenshot refreshed successfully
[[17:32:31]] [INFO] Executing action 149/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[17:32:31]] [SUCCESS] Screenshot refreshed
[[17:32:31]] [INFO] Refreshing screenshot...
[[17:32:27]] [SUCCESS] Screenshot refreshed successfully
[[17:32:27]] [SUCCESS] Screenshot refreshed successfully
[[17:32:26]] [INFO] Executing action 148/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[17:32:26]] [SUCCESS] Screenshot refreshed
[[17:32:26]] [INFO] Refreshing screenshot...
[[17:32:20]] [SUCCESS] Screenshot refreshed successfully
[[17:32:20]] [SUCCESS] Screenshot refreshed successfully
[[17:32:19]] [INFO] Executing action 147/512: swipeTillVisible action
[[17:32:19]] [SUCCESS] Screenshot refreshed
[[17:32:19]] [INFO] Refreshing screenshot...
[[17:32:15]] [SUCCESS] Screenshot refreshed successfully
[[17:32:15]] [SUCCESS] Screenshot refreshed successfully
[[17:32:15]] [INFO] Executing action 146/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[17:32:14]] [SUCCESS] Screenshot refreshed
[[17:32:14]] [INFO] Refreshing screenshot...
[[17:32:11]] [SUCCESS] Screenshot refreshed successfully
[[17:32:11]] [SUCCESS] Screenshot refreshed successfully
[[17:32:11]] [INFO] Executing action 145/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:32:10]] [SUCCESS] Screenshot refreshed
[[17:32:10]] [INFO] Refreshing screenshot...
[[17:32:07]] [SUCCESS] Screenshot refreshed successfully
[[17:32:07]] [SUCCESS] Screenshot refreshed successfully
[[17:32:06]] [INFO] Executing action 144/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:32:06]] [SUCCESS] Screenshot refreshed
[[17:32:06]] [INFO] Refreshing screenshot...
[[17:32:02]] [SUCCESS] Screenshot refreshed successfully
[[17:32:02]] [SUCCESS] Screenshot refreshed successfully
[[17:32:02]] [INFO] Executing action 143/512: iOS Function: text
[[17:32:02]] [SUCCESS] Screenshot refreshed
[[17:32:02]] [INFO] Refreshing screenshot...
[[17:31:57]] [SUCCESS] Screenshot refreshed successfully
[[17:31:57]] [SUCCESS] Screenshot refreshed successfully
[[17:31:56]] [INFO] Executing action 142/512: Tap on Text: "Find"
[[17:31:56]] [SUCCESS] Screenshot refreshed
[[17:31:56]] [INFO] Refreshing screenshot...
[[17:31:44]] [SUCCESS] Screenshot refreshed successfully
[[17:31:44]] [SUCCESS] Screenshot refreshed successfully
[[17:31:44]] [INFO] Executing action 141/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[17:31:43]] [SUCCESS] Screenshot refreshed
[[17:31:43]] [INFO] Refreshing screenshot...
[[17:31:39]] [SUCCESS] Screenshot refreshed successfully
[[17:31:39]] [SUCCESS] Screenshot refreshed successfully
[[17:31:39]] [INFO] Executing action 140/512: iOS Function: text
[[17:31:38]] [SUCCESS] Screenshot refreshed
[[17:31:38]] [INFO] Refreshing screenshot...
[[17:31:34]] [SUCCESS] Screenshot refreshed successfully
[[17:31:34]] [SUCCESS] Screenshot refreshed successfully
[[17:31:34]] [INFO] Executing action 139/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:31:34]] [SUCCESS] Screenshot refreshed
[[17:31:34]] [INFO] Refreshing screenshot...
[[17:31:29]] [INFO] Executing action 138/512: iOS Function: text
[[17:31:29]] [SUCCESS] Screenshot refreshed successfully
[[17:31:29]] [SUCCESS] Screenshot refreshed successfully
[[17:31:29]] [SUCCESS] Screenshot refreshed
[[17:31:29]] [INFO] Refreshing screenshot...
[[17:31:25]] [SUCCESS] Screenshot refreshed successfully
[[17:31:25]] [SUCCESS] Screenshot refreshed successfully
[[17:31:25]] [INFO] Executing action 137/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:31:25]] [SUCCESS] Screenshot refreshed
[[17:31:25]] [INFO] Refreshing screenshot...
[[17:31:21]] [SUCCESS] Screenshot refreshed successfully
[[17:31:21]] [SUCCESS] Screenshot refreshed successfully
[[17:31:21]] [INFO] Executing action 136/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:31:21]] [SUCCESS] Screenshot refreshed
[[17:31:21]] [INFO] Refreshing screenshot...
[[17:31:18]] [SUCCESS] Screenshot refreshed successfully
[[17:31:18]] [SUCCESS] Screenshot refreshed successfully
[[17:31:18]] [INFO] Executing action 135/512: iOS Function: alert_accept
[[17:31:17]] [SUCCESS] Screenshot refreshed
[[17:31:17]] [INFO] Refreshing screenshot...
[[17:31:11]] [SUCCESS] Screenshot refreshed successfully
[[17:31:11]] [SUCCESS] Screenshot refreshed successfully
[[17:31:11]] [INFO] Executing action 134/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:31:10]] [SUCCESS] Screenshot refreshed
[[17:31:10]] [INFO] Refreshing screenshot...
[[17:30:56]] [SUCCESS] Screenshot refreshed successfully
[[17:30:56]] [SUCCESS] Screenshot refreshed successfully
[[17:30:55]] [INFO] Executing action 133/512: Restart app: env[appid]
[[17:30:55]] [SUCCESS] Screenshot refreshed
[[17:30:55]] [INFO] Refreshing screenshot...
[[17:30:55]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[17:30:55]] [INFO] Executing action 132/512: Hook Action: tap on image: banner-close-updated.png (Recovery)
[[17:30:54]] [SUCCESS] Screenshot refreshed
[[17:30:54]] [INFO] Refreshing screenshot...
[[17:30:50]] [SUCCESS] Screenshot refreshed successfully
[[17:30:50]] [SUCCESS] Screenshot refreshed successfully
[[17:30:50]] [INFO] Executing action 131/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:30:50]] [SUCCESS] Screenshot refreshed
[[17:30:50]] [INFO] Refreshing screenshot...
[[17:30:45]] [SUCCESS] Screenshot refreshed successfully
[[17:30:45]] [SUCCESS] Screenshot refreshed successfully
[[17:30:45]] [INFO] Executing action 130/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:30:44]] [SUCCESS] Screenshot refreshed
[[17:30:44]] [INFO] Refreshing screenshot...
[[17:30:41]] [SUCCESS] Screenshot refreshed successfully
[[17:30:41]] [SUCCESS] Screenshot refreshed successfully
[[17:30:41]] [INFO] Executing action 129/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:30:40]] [SUCCESS] Screenshot refreshed
[[17:30:40]] [INFO] Refreshing screenshot...
[[17:30:39]] [SUCCESS] Screenshot refreshed successfully
[[17:30:39]] [SUCCESS] Screenshot refreshed successfully
[[17:30:38]] [INFO] Executing action 128/512: Add Log: Sign in from universal login page successful (with screenshot)
[[17:30:38]] [SUCCESS] Screenshot refreshed
[[17:30:38]] [INFO] Refreshing screenshot...
[[17:30:38]] [SUCCESS] Screenshot refreshed
[[17:30:38]] [INFO] Refreshing screenshot...
[[17:30:35]] [SUCCESS] Screenshot refreshed successfully
[[17:30:35]] [SUCCESS] Screenshot refreshed successfully
[[17:30:34]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[17:30:34]] [SUCCESS] Screenshot refreshed
[[17:30:34]] [INFO] Refreshing screenshot...
[[17:30:29]] [SUCCESS] Screenshot refreshed successfully
[[17:30:29]] [SUCCESS] Screenshot refreshed successfully
[[17:30:29]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[17:30:29]] [SUCCESS] Screenshot refreshed
[[17:30:29]] [INFO] Refreshing screenshot...
[[17:30:25]] [SUCCESS] Screenshot refreshed successfully
[[17:30:25]] [SUCCESS] Screenshot refreshed successfully
[[17:30:25]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:30:24]] [SUCCESS] Screenshot refreshed
[[17:30:24]] [INFO] Refreshing screenshot...
[[17:30:20]] [SUCCESS] Screenshot refreshed successfully
[[17:30:20]] [SUCCESS] Screenshot refreshed successfully
[[17:30:20]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[17:30:19]] [SUCCESS] Screenshot refreshed
[[17:30:19]] [INFO] Refreshing screenshot...
[[17:30:16]] [SUCCESS] Screenshot refreshed successfully
[[17:30:16]] [SUCCESS] Screenshot refreshed successfully
[[17:30:16]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:30:15]] [SUCCESS] Screenshot refreshed
[[17:30:15]] [INFO] Refreshing screenshot...
[[17:30:10]] [SUCCESS] Screenshot refreshed successfully
[[17:30:10]] [SUCCESS] Screenshot refreshed successfully
[[17:30:10]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:30:10]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[17:30:10]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[17:30:10]] [INFO] Executing action 127/512: Execute Test Case: Kmart-Signin (6 steps)
[[17:30:09]] [SUCCESS] Screenshot refreshed
[[17:30:09]] [INFO] Refreshing screenshot...
[[17:30:07]] [SUCCESS] Screenshot refreshed successfully
[[17:30:07]] [SUCCESS] Screenshot refreshed successfully
[[17:30:07]] [INFO] Executing action 126/512: iOS Function: alert_accept
[[17:30:06]] [SUCCESS] Screenshot refreshed
[[17:30:06]] [INFO] Refreshing screenshot...
[[17:30:01]] [SUCCESS] Screenshot refreshed successfully
[[17:30:01]] [SUCCESS] Screenshot refreshed successfully
[[17:30:01]] [INFO] Executing action 125/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:30:00]] [SUCCESS] Screenshot refreshed
[[17:30:00]] [INFO] Refreshing screenshot...
[[17:29:57]] [SUCCESS] Screenshot refreshed successfully
[[17:29:57]] [SUCCESS] Screenshot refreshed successfully
[[17:29:56]] [INFO] Executing action 124/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[17:29:56]] [SUCCESS] Screenshot refreshed
[[17:29:56]] [INFO] Refreshing screenshot...
[[17:29:52]] [SUCCESS] Screenshot refreshed successfully
[[17:29:52]] [SUCCESS] Screenshot refreshed successfully
[[17:29:52]] [INFO] Executing action 123/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:29:51]] [SUCCESS] Screenshot refreshed
[[17:29:51]] [INFO] Refreshing screenshot...
[[17:29:46]] [SUCCESS] Screenshot refreshed successfully
[[17:29:46]] [SUCCESS] Screenshot refreshed successfully
[[17:29:46]] [INFO] Executing action 122/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:29:46]] [SUCCESS] Screenshot refreshed
[[17:29:46]] [INFO] Refreshing screenshot...
[[17:29:41]] [SUCCESS] Screenshot refreshed successfully
[[17:29:41]] [SUCCESS] Screenshot refreshed successfully
[[17:29:41]] [INFO] Executing action 121/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:29:41]] [SUCCESS] Screenshot refreshed
[[17:29:41]] [INFO] Refreshing screenshot...
[[17:29:40]] [SUCCESS] Screenshot refreshed successfully
[[17:29:40]] [SUCCESS] Screenshot refreshed successfully
[[17:29:39]] [INFO] Executing action 120/512: Add Log: Sign in from bag page is successfully (with screenshot)
[[17:29:39]] [SUCCESS] Screenshot refreshed
[[17:29:39]] [INFO] Refreshing screenshot...
[[17:29:35]] [SUCCESS] Screenshot refreshed successfully
[[17:29:35]] [SUCCESS] Screenshot refreshed successfully
[[17:29:35]] [INFO] Executing action 119/512: iOS Function: text
[[17:29:34]] [SUCCESS] Screenshot refreshed
[[17:29:34]] [INFO] Refreshing screenshot...
[[17:29:30]] [SUCCESS] Screenshot refreshed successfully
[[17:29:30]] [SUCCESS] Screenshot refreshed successfully
[[17:29:30]] [INFO] Executing action 118/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[17:29:30]] [SUCCESS] Screenshot refreshed
[[17:29:30]] [INFO] Refreshing screenshot...
[[17:29:25]] [SUCCESS] Screenshot refreshed successfully
[[17:29:25]] [SUCCESS] Screenshot refreshed successfully
[[17:29:25]] [INFO] Executing action 117/512: iOS Function: text
[[17:29:25]] [SUCCESS] Screenshot refreshed
[[17:29:25]] [INFO] Refreshing screenshot...
[[17:29:21]] [SUCCESS] Screenshot refreshed successfully
[[17:29:21]] [SUCCESS] Screenshot refreshed successfully
[[17:29:21]] [INFO] Executing action 116/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[17:29:20]] [SUCCESS] Screenshot refreshed
[[17:29:20]] [INFO] Refreshing screenshot...
[[17:29:18]] [SUCCESS] Screenshot refreshed successfully
[[17:29:18]] [SUCCESS] Screenshot refreshed successfully
[[17:29:18]] [INFO] Executing action 115/512: iOS Function: alert_accept
[[17:29:17]] [SUCCESS] Screenshot refreshed
[[17:29:17]] [INFO] Refreshing screenshot...
[[17:29:13]] [SUCCESS] Screenshot refreshed successfully
[[17:29:13]] [SUCCESS] Screenshot refreshed successfully
[[17:29:13]] [INFO] Executing action 114/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign In"]
[[17:29:12]] [SUCCESS] Screenshot refreshed
[[17:29:12]] [INFO] Refreshing screenshot...
[[17:29:02]] [SUCCESS] Screenshot refreshed successfully
[[17:29:02]] [SUCCESS] Screenshot refreshed successfully
[[17:29:02]] [INFO] Executing action 113/512: swipeTillVisible action
[[17:29:01]] [SUCCESS] Screenshot refreshed
[[17:29:01]] [INFO] Refreshing screenshot...
[[17:28:44]] [SUCCESS] Screenshot refreshed successfully
[[17:28:44]] [SUCCESS] Screenshot refreshed successfully
[[17:28:43]] [INFO] Executing action 112/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[17:28:43]] [SUCCESS] Screenshot refreshed
[[17:28:43]] [INFO] Refreshing screenshot...
[[17:28:39]] [SUCCESS] Screenshot refreshed successfully
[[17:28:39]] [SUCCESS] Screenshot refreshed successfully
[[17:28:38]] [INFO] Executing action 111/512: Tap on image: Unocard-product-se.png
[[17:28:38]] [SUCCESS] Screenshot refreshed
[[17:28:38]] [INFO] Refreshing screenshot...
[[17:28:34]] [SUCCESS] Screenshot refreshed successfully
[[17:28:34]] [SUCCESS] Screenshot refreshed successfully
[[17:28:34]] [INFO] Executing action 110/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:28:34]] [SUCCESS] Screenshot refreshed
[[17:28:34]] [INFO] Refreshing screenshot...
[[17:28:30]] [SUCCESS] Screenshot refreshed successfully
[[17:28:30]] [SUCCESS] Screenshot refreshed successfully
[[17:28:30]] [INFO] Executing action 109/512: iOS Function: text
[[17:28:29]] [SUCCESS] Screenshot refreshed
[[17:28:29]] [INFO] Refreshing screenshot...
[[17:28:24]] [SUCCESS] Screenshot refreshed successfully
[[17:28:24]] [SUCCESS] Screenshot refreshed successfully
[[17:28:24]] [INFO] Executing action 108/512: Tap on Text: "Find"
[[17:28:23]] [SUCCESS] Screenshot refreshed
[[17:28:23]] [INFO] Refreshing screenshot...
[[17:28:18]] [SUCCESS] Screenshot refreshed successfully
[[17:28:18]] [SUCCESS] Screenshot refreshed successfully
[[17:28:18]] [INFO] Executing action 107/512: Restart app: env[appid]
[[17:28:17]] [SUCCESS] Screenshot refreshed
[[17:28:17]] [INFO] Refreshing screenshot...
[[17:28:13]] [SUCCESS] Screenshot refreshed successfully
[[17:28:13]] [SUCCESS] Screenshot refreshed successfully
[[17:28:13]] [INFO] Executing action 106/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:28:13]] [SUCCESS] Screenshot refreshed
[[17:28:13]] [INFO] Refreshing screenshot...
[[17:28:08]] [SUCCESS] Screenshot refreshed successfully
[[17:28:08]] [SUCCESS] Screenshot refreshed successfully
[[17:28:08]] [INFO] Executing action 105/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:28:07]] [SUCCESS] Screenshot refreshed
[[17:28:07]] [INFO] Refreshing screenshot...
[[17:28:04]] [SUCCESS] Screenshot refreshed successfully
[[17:28:04]] [SUCCESS] Screenshot refreshed successfully
[[17:28:04]] [INFO] Executing action 104/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:28:03]] [SUCCESS] Screenshot refreshed
[[17:28:03]] [INFO] Refreshing screenshot...
[[17:28:02]] [SUCCESS] Screenshot refreshed successfully
[[17:28:02]] [SUCCESS] Screenshot refreshed successfully
[[17:28:01]] [INFO] Executing action 103/512: Add Log: Sign in from wishlist page successfully (with screenshot)
[[17:28:01]] [SUCCESS] Screenshot refreshed
[[17:28:01]] [INFO] Refreshing screenshot...
[[17:27:58]] [SUCCESS] Screenshot refreshed successfully
[[17:27:58]] [SUCCESS] Screenshot refreshed successfully
[[17:27:57]] [INFO] Executing action 102/512: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[17:27:57]] [SUCCESS] Screenshot refreshed
[[17:27:57]] [INFO] Refreshing screenshot...
[[17:27:53]] [SUCCESS] Screenshot refreshed successfully
[[17:27:53]] [SUCCESS] Screenshot refreshed successfully
[[17:27:53]] [INFO] Executing action 101/512: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[17:27:52]] [SUCCESS] Screenshot refreshed
[[17:27:52]] [INFO] Refreshing screenshot...
[[17:27:48]] [SUCCESS] Screenshot refreshed successfully
[[17:27:48]] [SUCCESS] Screenshot refreshed successfully
[[17:27:48]] [INFO] Executing action 100/512: iOS Function: text
[[17:27:48]] [SUCCESS] Screenshot refreshed
[[17:27:48]] [INFO] Refreshing screenshot...
[[17:27:44]] [SUCCESS] Screenshot refreshed successfully
[[17:27:44]] [SUCCESS] Screenshot refreshed successfully
[[17:27:44]] [INFO] Executing action 99/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:27:43]] [SUCCESS] Screenshot refreshed
[[17:27:43]] [INFO] Refreshing screenshot...
[[17:27:39]] [SUCCESS] Screenshot refreshed successfully
[[17:27:39]] [SUCCESS] Screenshot refreshed successfully
[[17:27:39]] [INFO] Executing action 98/512: iOS Function: text
[[17:27:38]] [SUCCESS] Screenshot refreshed
[[17:27:38]] [INFO] Refreshing screenshot...
[[17:27:35]] [SUCCESS] Screenshot refreshed successfully
[[17:27:35]] [SUCCESS] Screenshot refreshed successfully
[[17:27:35]] [INFO] Executing action 97/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:27:34]] [SUCCESS] Screenshot refreshed
[[17:27:34]] [INFO] Refreshing screenshot...
[[17:27:31]] [SUCCESS] Screenshot refreshed successfully
[[17:27:31]] [SUCCESS] Screenshot refreshed successfully
[[17:27:31]] [INFO] Executing action 96/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:27:30]] [SUCCESS] Screenshot refreshed
[[17:27:30]] [INFO] Refreshing screenshot...
[[17:27:28]] [SUCCESS] Screenshot refreshed successfully
[[17:27:28]] [SUCCESS] Screenshot refreshed successfully
[[17:27:27]] [INFO] Executing action 95/512: iOS Function: alert_accept
[[17:27:27]] [SUCCESS] Screenshot refreshed
[[17:27:27]] [INFO] Refreshing screenshot...
[[17:27:24]] [SUCCESS] Screenshot refreshed successfully
[[17:27:24]] [SUCCESS] Screenshot refreshed successfully
[[17:27:24]] [INFO] Executing action 94/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[17:27:23]] [SUCCESS] Screenshot refreshed
[[17:27:23]] [INFO] Refreshing screenshot...
[[17:27:20]] [SUCCESS] Screenshot refreshed successfully
[[17:27:20]] [SUCCESS] Screenshot refreshed successfully
[[17:27:20]] [INFO] Executing action 93/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[17:27:19]] [SUCCESS] Screenshot refreshed
[[17:27:19]] [INFO] Refreshing screenshot...
[[17:27:15]] [SUCCESS] Screenshot refreshed successfully
[[17:27:15]] [SUCCESS] Screenshot refreshed successfully
[[17:27:15]] [INFO] Executing action 92/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:27:15]] [SUCCESS] Screenshot refreshed
[[17:27:15]] [INFO] Refreshing screenshot...
[[17:27:10]] [SUCCESS] Screenshot refreshed successfully
[[17:27:10]] [SUCCESS] Screenshot refreshed successfully
[[17:27:10]] [INFO] Executing action 91/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:27:09]] [SUCCESS] Screenshot refreshed
[[17:27:09]] [INFO] Refreshing screenshot...
[[17:27:06]] [SUCCESS] Screenshot refreshed successfully
[[17:27:06]] [SUCCESS] Screenshot refreshed successfully
[[17:27:06]] [INFO] Executing action 90/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:27:05]] [SUCCESS] Screenshot refreshed
[[17:27:05]] [INFO] Refreshing screenshot...
[[17:27:04]] [SUCCESS] Screenshot refreshed successfully
[[17:27:04]] [SUCCESS] Screenshot refreshed successfully
[[17:27:03]] [INFO] Executing action 89/512: Add Log: Sign in from home page sign in button successfully (with screenshot)
[[17:27:02]] [SUCCESS] Screenshot refreshed
[[17:27:02]] [INFO] Refreshing screenshot...
[[17:27:00]] [SUCCESS] Screenshot refreshed successfully
[[17:27:00]] [SUCCESS] Screenshot refreshed successfully
[[17:26:59]] [INFO] Executing action 88/512: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[17:26:59]] [SUCCESS] Screenshot refreshed
[[17:26:59]] [INFO] Refreshing screenshot...
[[17:26:54]] [SUCCESS] Screenshot refreshed successfully
[[17:26:54]] [SUCCESS] Screenshot refreshed successfully
[[17:26:54]] [INFO] Executing action 87/512: iOS Function: text
[[17:26:53]] [SUCCESS] Screenshot refreshed
[[17:26:53]] [INFO] Refreshing screenshot...
[[17:26:50]] [SUCCESS] Screenshot refreshed successfully
[[17:26:50]] [SUCCESS] Screenshot refreshed successfully
[[17:26:50]] [INFO] Executing action 86/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:26:49]] [SUCCESS] Screenshot refreshed
[[17:26:49]] [INFO] Refreshing screenshot...
[[17:26:45]] [INFO] Executing action 85/512: iOS Function: text
[[17:26:45]] [SUCCESS] Screenshot refreshed successfully
[[17:26:45]] [SUCCESS] Screenshot refreshed successfully
[[17:26:44]] [SUCCESS] Screenshot refreshed
[[17:26:44]] [INFO] Refreshing screenshot...
[[17:26:40]] [SUCCESS] Screenshot refreshed successfully
[[17:26:40]] [SUCCESS] Screenshot refreshed successfully
[[17:26:40]] [INFO] Executing action 84/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:26:40]] [SUCCESS] Screenshot refreshed
[[17:26:40]] [INFO] Refreshing screenshot...
[[17:26:37]] [SUCCESS] Screenshot refreshed successfully
[[17:26:37]] [SUCCESS] Screenshot refreshed successfully
[[17:26:37]] [INFO] Executing action 83/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:26:36]] [SUCCESS] Screenshot refreshed
[[17:26:36]] [INFO] Refreshing screenshot...
[[17:26:34]] [SUCCESS] Screenshot refreshed successfully
[[17:26:34]] [SUCCESS] Screenshot refreshed successfully
[[17:26:33]] [INFO] Executing action 82/512: iOS Function: alert_accept
[[17:26:33]] [SUCCESS] Screenshot refreshed
[[17:26:33]] [INFO] Refreshing screenshot...
[[17:26:27]] [SUCCESS] Screenshot refreshed successfully
[[17:26:27]] [SUCCESS] Screenshot refreshed successfully
[[17:26:26]] [INFO] Executing action 81/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:26:26]] [SUCCESS] Screenshot refreshed
[[17:26:26]] [INFO] Refreshing screenshot...
[[17:26:12]] [SUCCESS] Screenshot refreshed successfully
[[17:26:12]] [SUCCESS] Screenshot refreshed successfully
[[17:26:11]] [INFO] Executing action 80/512: Restart app: env[appid]
[[17:26:11]] [SUCCESS] Screenshot refreshed
[[17:26:11]] [INFO] Refreshing screenshot...
[[17:26:10]] [SUCCESS] Screenshot refreshed
[[17:26:10]] [INFO] Refreshing screenshot...
[[17:26:06]] [SUCCESS] Screenshot refreshed successfully
[[17:26:06]] [SUCCESS] Screenshot refreshed successfully
[[17:26:06]] [INFO] Executing Multi Step action step 34/34: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[17:26:06]] [SUCCESS] Screenshot refreshed
[[17:26:06]] [INFO] Refreshing screenshot...
[[17:26:02]] [INFO] Executing Multi Step action step 33/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:26:02]] [SUCCESS] Screenshot refreshed successfully
[[17:26:02]] [SUCCESS] Screenshot refreshed successfully
[[17:26:02]] [SUCCESS] Screenshot refreshed
[[17:26:02]] [INFO] Refreshing screenshot...
[[17:25:59]] [SUCCESS] Screenshot refreshed successfully
[[17:25:59]] [SUCCESS] Screenshot refreshed successfully
[[17:25:58]] [INFO] Executing Multi Step action step 32/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:25:57]] [SUCCESS] Screenshot refreshed
[[17:25:57]] [INFO] Refreshing screenshot...
[[17:25:53]] [SUCCESS] Screenshot refreshed successfully
[[17:25:53]] [SUCCESS] Screenshot refreshed successfully
[[17:25:53]] [INFO] Executing Multi Step action step 31/34: Tap on image: banner-close-updated.png
[[17:25:52]] [SUCCESS] Screenshot refreshed
[[17:25:52]] [INFO] Refreshing screenshot...
[[17:25:43]] [SUCCESS] Screenshot refreshed successfully
[[17:25:43]] [SUCCESS] Screenshot refreshed successfully
[[17:25:43]] [INFO] Executing Multi Step action step 30/34: Swipe from (50%, 70%) to (50%, 30%)
[[17:25:43]] [SUCCESS] Screenshot refreshed
[[17:25:43]] [INFO] Refreshing screenshot...
[[17:25:39]] [SUCCESS] Screenshot refreshed successfully
[[17:25:39]] [SUCCESS] Screenshot refreshed successfully
[[17:25:39]] [INFO] Executing Multi Step action step 29/34: Tap on image: env[delivery-address-img]
[[17:25:38]] [SUCCESS] Screenshot refreshed
[[17:25:38]] [INFO] Refreshing screenshot...
[[17:25:34]] [SUCCESS] Screenshot refreshed successfully
[[17:25:34]] [SUCCESS] Screenshot refreshed successfully
[[17:25:34]] [INFO] Executing Multi Step action step 28/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[17:25:34]] [SUCCESS] Screenshot refreshed
[[17:25:34]] [INFO] Refreshing screenshot...
[[17:25:27]] [SUCCESS] Screenshot refreshed successfully
[[17:25:27]] [SUCCESS] Screenshot refreshed successfully
[[17:25:27]] [INFO] Executing Multi Step action step 27/34: Tap and Type at (54, 314): "305 238 Flinders"
[[17:25:27]] [SUCCESS] Screenshot refreshed
[[17:25:27]] [INFO] Refreshing screenshot...
[[17:25:21]] [SUCCESS] Screenshot refreshed successfully
[[17:25:21]] [SUCCESS] Screenshot refreshed successfully
[[17:25:21]] [INFO] Executing Multi Step action step 26/34: Tap on Text: "address"
[[17:25:21]] [SUCCESS] Screenshot refreshed
[[17:25:21]] [INFO] Refreshing screenshot...
[[17:25:17]] [SUCCESS] Screenshot refreshed successfully
[[17:25:17]] [SUCCESS] Screenshot refreshed successfully
[[17:25:17]] [INFO] Executing Multi Step action step 25/34: iOS Function: text
[[17:25:16]] [SUCCESS] Screenshot refreshed
[[17:25:16]] [INFO] Refreshing screenshot...
[[17:25:12]] [SUCCESS] Screenshot refreshed successfully
[[17:25:12]] [SUCCESS] Screenshot refreshed successfully
[[17:25:12]] [INFO] Executing Multi Step action step 24/34: textClear action
[[17:25:12]] [SUCCESS] Screenshot refreshed
[[17:25:12]] [INFO] Refreshing screenshot...
[[17:25:08]] [SUCCESS] Screenshot refreshed successfully
[[17:25:08]] [SUCCESS] Screenshot refreshed successfully
[[17:25:08]] [INFO] Executing Multi Step action step 23/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[17:25:08]] [SUCCESS] Screenshot refreshed
[[17:25:08]] [INFO] Refreshing screenshot...
[[17:25:04]] [SUCCESS] Screenshot refreshed successfully
[[17:25:04]] [SUCCESS] Screenshot refreshed successfully
[[17:25:04]] [INFO] Executing Multi Step action step 22/34: textClear action
[[17:25:03]] [SUCCESS] Screenshot refreshed
[[17:25:03]] [INFO] Refreshing screenshot...
[[17:25:00]] [SUCCESS] Screenshot refreshed successfully
[[17:25:00]] [SUCCESS] Screenshot refreshed successfully
[[17:25:00]] [INFO] Executing Multi Step action step 21/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:24:59]] [SUCCESS] Screenshot refreshed
[[17:24:59]] [INFO] Refreshing screenshot...
[[17:24:55]] [SUCCESS] Screenshot refreshed successfully
[[17:24:55]] [SUCCESS] Screenshot refreshed successfully
[[17:24:55]] [INFO] Executing Multi Step action step 20/34: textClear action
[[17:24:55]] [SUCCESS] Screenshot refreshed
[[17:24:55]] [INFO] Refreshing screenshot...
[[17:24:51]] [SUCCESS] Screenshot refreshed successfully
[[17:24:51]] [SUCCESS] Screenshot refreshed successfully
[[17:24:51]] [INFO] Executing Multi Step action step 19/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[17:24:50]] [SUCCESS] Screenshot refreshed
[[17:24:50]] [INFO] Refreshing screenshot...
[[17:24:46]] [SUCCESS] Screenshot refreshed successfully
[[17:24:46]] [SUCCESS] Screenshot refreshed successfully
[[17:24:46]] [INFO] Executing Multi Step action step 18/34: textClear action
[[17:24:46]] [SUCCESS] Screenshot refreshed
[[17:24:46]] [INFO] Refreshing screenshot...
[[17:24:43]] [SUCCESS] Screenshot refreshed successfully
[[17:24:43]] [SUCCESS] Screenshot refreshed successfully
[[17:24:43]] [INFO] Executing Multi Step action step 17/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[17:24:42]] [SUCCESS] Screenshot refreshed
[[17:24:42]] [INFO] Refreshing screenshot...
[[17:24:38]] [SUCCESS] Screenshot refreshed successfully
[[17:24:38]] [SUCCESS] Screenshot refreshed successfully
[[17:24:38]] [INFO] Executing Multi Step action step 16/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[17:24:38]] [SUCCESS] Screenshot refreshed
[[17:24:38]] [INFO] Refreshing screenshot...
[[17:24:20]] [SUCCESS] Screenshot refreshed successfully
[[17:24:20]] [SUCCESS] Screenshot refreshed successfully
[[17:24:19]] [INFO] Executing Multi Step action step 15/34: swipeTillVisible action
[[17:24:19]] [SUCCESS] Screenshot refreshed
[[17:24:19]] [INFO] Refreshing screenshot...
[[17:24:15]] [SUCCESS] Screenshot refreshed successfully
[[17:24:15]] [SUCCESS] Screenshot refreshed successfully
[[17:24:15]] [INFO] Executing Multi Step action step 14/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[17:24:15]] [SUCCESS] Screenshot refreshed
[[17:24:15]] [INFO] Refreshing screenshot...
[[17:24:12]] [INFO] Executing Multi Step action step 13/34: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[17:24:12]] [SUCCESS] Screenshot refreshed successfully
[[17:24:12]] [SUCCESS] Screenshot refreshed successfully
[[17:24:11]] [SUCCESS] Screenshot refreshed
[[17:24:11]] [INFO] Refreshing screenshot...
[[17:24:08]] [SUCCESS] Screenshot refreshed successfully
[[17:24:08]] [SUCCESS] Screenshot refreshed successfully
[[17:24:08]] [INFO] Executing Multi Step action step 12/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:24:07]] [SUCCESS] Screenshot refreshed
[[17:24:07]] [INFO] Refreshing screenshot...
[[17:24:01]] [SUCCESS] Screenshot refreshed successfully
[[17:24:01]] [SUCCESS] Screenshot refreshed successfully
[[17:24:01]] [INFO] Executing Multi Step action step 11/34: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[17:24:00]] [SUCCESS] Screenshot refreshed
[[17:24:00]] [INFO] Refreshing screenshot...
[[17:23:57]] [SUCCESS] Screenshot refreshed successfully
[[17:23:57]] [SUCCESS] Screenshot refreshed successfully
[[17:23:57]] [INFO] Executing Multi Step action step 10/34: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:23:56]] [SUCCESS] Screenshot refreshed
[[17:23:56]] [INFO] Refreshing screenshot...
[[17:23:53]] [SUCCESS] Screenshot refreshed successfully
[[17:23:53]] [SUCCESS] Screenshot refreshed successfully
[[17:23:53]] [INFO] Executing Multi Step action step 9/34: iOS Function: text
[[17:23:52]] [SUCCESS] Screenshot refreshed
[[17:23:52]] [INFO] Refreshing screenshot...
[[17:23:47]] [SUCCESS] Screenshot refreshed successfully
[[17:23:47]] [SUCCESS] Screenshot refreshed successfully
[[17:23:46]] [INFO] Executing Multi Step action step 8/34: Tap on Text: "Find"
[[17:23:46]] [SUCCESS] Screenshot refreshed
[[17:23:46]] [INFO] Refreshing screenshot...
[[17:23:24]] [SUCCESS] Screenshot refreshed successfully
[[17:23:24]] [SUCCESS] Screenshot refreshed successfully
[[17:23:23]] [INFO] Executing Multi Step action step 7/34: If exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]" (timeout: 20s) → Then tap at (0, 0)
[[17:23:23]] [SUCCESS] Screenshot refreshed
[[17:23:23]] [INFO] Refreshing screenshot...
[[17:23:11]] [SUCCESS] Screenshot refreshed successfully
[[17:23:11]] [SUCCESS] Screenshot refreshed successfully
[[17:23:10]] [INFO] Executing Multi Step action step 6/34: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[17:23:10]] [SUCCESS] Screenshot refreshed
[[17:23:10]] [INFO] Refreshing screenshot...
[[17:22:43]] [SUCCESS] Screenshot refreshed successfully
[[17:22:43]] [SUCCESS] Screenshot refreshed successfully
[[17:22:42]] [INFO] Executing Multi Step action step 5/34: If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 25s) → Then click element: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[17:22:42]] [SUCCESS] Screenshot refreshed
[[17:22:42]] [INFO] Refreshing screenshot...
[[17:22:37]] [SUCCESS] Screenshot refreshed successfully
[[17:22:37]] [SUCCESS] Screenshot refreshed successfully
[[17:22:37]] [INFO] Executing Multi Step action step 4/34: Tap on Text: "Save"
[[17:22:36]] [SUCCESS] Screenshot refreshed
[[17:22:36]] [INFO] Refreshing screenshot...
[[17:22:32]] [SUCCESS] Screenshot refreshed successfully
[[17:22:32]] [SUCCESS] Screenshot refreshed successfully
[[17:22:32]] [INFO] Executing Multi Step action step 3/34: Tap on element with accessibility_id: btnCurrentLocationButton
[[17:22:31]] [SUCCESS] Screenshot refreshed
[[17:22:31]] [INFO] Refreshing screenshot...
[[17:22:27]] [SUCCESS] Screenshot refreshed successfully
[[17:22:27]] [SUCCESS] Screenshot refreshed successfully
[[17:22:27]] [INFO] Executing Multi Step action step 2/34: Wait till accessibility_id=btnCurrentLocationButton
[[17:22:26]] [SUCCESS] Screenshot refreshed
[[17:22:26]] [INFO] Refreshing screenshot...
[[17:22:20]] [SUCCESS] Screenshot refreshed successfully
[[17:22:20]] [SUCCESS] Screenshot refreshed successfully
[[17:22:19]] [INFO] Executing Multi Step action step 1/34: Tap on Text: "Edit"
[[17:22:19]] [INFO] Loaded 34 steps from test case: Delivery  Buy
[[17:22:19]] [INFO] Loading steps for Multi Step action: Delivery  Buy
[[17:22:19]] [INFO] Executing action 79/512: Execute Test Case: Delivery  Buy (34 steps)
[[17:22:19]] [SUCCESS] Screenshot refreshed
[[17:22:19]] [INFO] Refreshing screenshot...
[[17:22:15]] [SUCCESS] Screenshot refreshed successfully
[[17:22:15]] [SUCCESS] Screenshot refreshed successfully
[[17:22:15]] [INFO] Executing action 78/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[17:22:14]] [SUCCESS] Screenshot refreshed
[[17:22:14]] [INFO] Refreshing screenshot...
[[17:22:10]] [SUCCESS] Screenshot refreshed successfully
[[17:22:10]] [SUCCESS] Screenshot refreshed successfully
[[17:22:10]] [INFO] Executing action 77/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:22:10]] [SUCCESS] Screenshot refreshed
[[17:22:10]] [INFO] Refreshing screenshot...
[[17:22:05]] [SUCCESS] Screenshot refreshed successfully
[[17:22:05]] [SUCCESS] Screenshot refreshed successfully
[[17:22:05]] [INFO] Executing action 76/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:22:04]] [SUCCESS] Screenshot refreshed
[[17:22:04]] [INFO] Refreshing screenshot...
[[17:22:01]] [SUCCESS] Screenshot refreshed successfully
[[17:22:01]] [SUCCESS] Screenshot refreshed successfully
[[17:22:01]] [INFO] Executing action 75/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:22:00]] [SUCCESS] Screenshot refreshed
[[17:22:00]] [INFO] Refreshing screenshot...
[[17:21:56]] [SUCCESS] Screenshot refreshed successfully
[[17:21:56]] [SUCCESS] Screenshot refreshed successfully
[[17:21:56]] [INFO] Executing action 74/512: Tap on image: banner-close-updated.png
[[17:21:55]] [SUCCESS] Screenshot refreshed
[[17:21:55]] [INFO] Refreshing screenshot...
[[17:21:52]] [SUCCESS] Screenshot refreshed successfully
[[17:21:52]] [SUCCESS] Screenshot refreshed successfully
[[17:21:52]] [INFO] Executing action 73/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:21:51]] [SUCCESS] Screenshot refreshed
[[17:21:51]] [INFO] Refreshing screenshot...
[[17:21:46]] [SUCCESS] Screenshot refreshed successfully
[[17:21:46]] [SUCCESS] Screenshot refreshed successfully
[[17:21:46]] [INFO] Executing action 72/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:21:45]] [SUCCESS] Screenshot refreshed
[[17:21:45]] [INFO] Refreshing screenshot...
[[17:21:42]] [SUCCESS] Screenshot refreshed successfully
[[17:21:42]] [SUCCESS] Screenshot refreshed successfully
[[17:21:41]] [INFO] Executing action 71/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[17:21:41]] [SUCCESS] Screenshot refreshed
[[17:21:41]] [INFO] Refreshing screenshot...
[[17:21:38]] [SUCCESS] Screenshot refreshed successfully
[[17:21:38]] [SUCCESS] Screenshot refreshed successfully
[[17:21:37]] [INFO] Executing action 70/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:21:37]] [SUCCESS] Screenshot refreshed
[[17:21:37]] [INFO] Refreshing screenshot...
[[17:21:33]] [SUCCESS] Screenshot refreshed successfully
[[17:21:33]] [SUCCESS] Screenshot refreshed successfully
[[17:21:33]] [INFO] Executing action 69/512: Tap on image: banner-close-updated.png
[[17:21:32]] [SUCCESS] Screenshot refreshed
[[17:21:32]] [INFO] Refreshing screenshot...
[[17:21:21]] [SUCCESS] Screenshot refreshed successfully
[[17:21:21]] [SUCCESS] Screenshot refreshed successfully
[[17:21:20]] [INFO] Executing action 68/512: Wait for 10 ms
[[17:21:20]] [SUCCESS] Screenshot refreshed
[[17:21:20]] [INFO] Refreshing screenshot...
[[17:21:10]] [SUCCESS] Screenshot refreshed successfully
[[17:21:10]] [SUCCESS] Screenshot refreshed successfully
[[17:21:10]] [INFO] Executing action 67/512: Tap on Text: "Brunswick"
[[17:21:09]] [SUCCESS] Screenshot refreshed
[[17:21:09]] [INFO] Refreshing screenshot...
[[17:21:04]] [SUCCESS] Screenshot refreshed successfully
[[17:21:04]] [SUCCESS] Screenshot refreshed successfully
[[17:21:04]] [INFO] Executing action 66/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:21:03]] [SUCCESS] Screenshot refreshed
[[17:21:03]] [INFO] Refreshing screenshot...
[[17:21:00]] [SUCCESS] Screenshot refreshed successfully
[[17:21:00]] [SUCCESS] Screenshot refreshed successfully
[[17:21:00]] [INFO] Executing action 65/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[17:20:59]] [SUCCESS] Screenshot refreshed
[[17:20:59]] [INFO] Refreshing screenshot...
[[17:20:56]] [SUCCESS] Screenshot refreshed successfully
[[17:20:56]] [SUCCESS] Screenshot refreshed successfully
[[17:20:56]] [INFO] Executing action 64/512: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[17:20:56]] [SUCCESS] Screenshot refreshed
[[17:20:56]] [INFO] Refreshing screenshot...
[[17:20:53]] [SUCCESS] Screenshot refreshed successfully
[[17:20:53]] [SUCCESS] Screenshot refreshed successfully
[[17:20:52]] [INFO] Executing action 63/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:20:52]] [SUCCESS] Screenshot refreshed
[[17:20:52]] [INFO] Refreshing screenshot...
[[17:20:49]] [SUCCESS] Screenshot refreshed successfully
[[17:20:49]] [SUCCESS] Screenshot refreshed successfully
[[17:20:48]] [INFO] Executing action 62/512: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[17:20:48]] [SUCCESS] Screenshot refreshed
[[17:20:48]] [INFO] Refreshing screenshot...
[[17:20:42]] [SUCCESS] Screenshot refreshed successfully
[[17:20:42]] [SUCCESS] Screenshot refreshed successfully
[[17:20:42]] [INFO] Executing action 61/512: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[17:20:41]] [SUCCESS] Screenshot refreshed
[[17:20:41]] [INFO] Refreshing screenshot...
[[17:20:38]] [SUCCESS] Screenshot refreshed successfully
[[17:20:38]] [SUCCESS] Screenshot refreshed successfully
[[17:20:38]] [INFO] Executing action 60/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:20:37]] [SUCCESS] Screenshot refreshed
[[17:20:37]] [INFO] Refreshing screenshot...
[[17:20:34]] [SUCCESS] Screenshot refreshed successfully
[[17:20:34]] [SUCCESS] Screenshot refreshed successfully
[[17:20:34]] [INFO] Executing action 59/512: iOS Function: text
[[17:20:33]] [SUCCESS] Screenshot refreshed
[[17:20:33]] [INFO] Refreshing screenshot...
[[17:20:29]] [SUCCESS] Screenshot refreshed successfully
[[17:20:29]] [SUCCESS] Screenshot refreshed successfully
[[17:20:27]] [INFO] Executing action 58/512: Tap on Text: "Find"
[[17:20:27]] [SUCCESS] Screenshot refreshed
[[17:20:27]] [INFO] Refreshing screenshot...
[[17:20:27]] [SUCCESS] Screenshot refreshed
[[17:20:27]] [INFO] Refreshing screenshot...
[[17:20:24]] [SUCCESS] Screenshot refreshed successfully
[[17:20:24]] [SUCCESS] Screenshot refreshed successfully
[[17:20:24]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[17:20:23]] [SUCCESS] Screenshot refreshed
[[17:20:23]] [INFO] Refreshing screenshot...
[[17:20:19]] [SUCCESS] Screenshot refreshed successfully
[[17:20:19]] [SUCCESS] Screenshot refreshed successfully
[[17:20:19]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[17:20:18]] [SUCCESS] Screenshot refreshed
[[17:20:18]] [INFO] Refreshing screenshot...
[[17:20:14]] [SUCCESS] Screenshot refreshed successfully
[[17:20:14]] [SUCCESS] Screenshot refreshed successfully
[[17:20:14]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:20:14]] [SUCCESS] Screenshot refreshed
[[17:20:14]] [INFO] Refreshing screenshot...
[[17:20:09]] [SUCCESS] Screenshot refreshed successfully
[[17:20:09]] [SUCCESS] Screenshot refreshed successfully
[[17:20:09]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[17:20:09]] [SUCCESS] Screenshot refreshed
[[17:20:09]] [INFO] Refreshing screenshot...
[[17:20:05]] [SUCCESS] Screenshot refreshed successfully
[[17:20:05]] [SUCCESS] Screenshot refreshed successfully
[[17:20:05]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:20:05]] [SUCCESS] Screenshot refreshed
[[17:20:05]] [INFO] Refreshing screenshot...
[[17:19:59]] [SUCCESS] Screenshot refreshed successfully
[[17:19:59]] [SUCCESS] Screenshot refreshed successfully
[[17:19:59]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:19:59]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[17:19:59]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[17:19:59]] [INFO] Executing action 57/512: Execute Test Case: Kmart-Signin (8 steps)
[[17:19:59]] [SUCCESS] Screenshot refreshed
[[17:19:59]] [INFO] Refreshing screenshot...
[[17:19:55]] [SUCCESS] Screenshot refreshed successfully
[[17:19:55]] [SUCCESS] Screenshot refreshed successfully
[[17:19:55]] [INFO] Executing action 56/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:19:55]] [SUCCESS] Screenshot refreshed
[[17:19:55]] [INFO] Refreshing screenshot...
[[17:19:52]] [SUCCESS] Screenshot refreshed successfully
[[17:19:52]] [SUCCESS] Screenshot refreshed successfully
[[17:19:52]] [INFO] Executing action 55/512: iOS Function: alert_accept
[[17:19:51]] [SUCCESS] Screenshot refreshed
[[17:19:51]] [INFO] Refreshing screenshot...
[[17:19:46]] [SUCCESS] Screenshot refreshed successfully
[[17:19:46]] [SUCCESS] Screenshot refreshed successfully
[[17:19:45]] [INFO] Executing action 54/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:19:45]] [SUCCESS] Screenshot refreshed
[[17:19:45]] [INFO] Refreshing screenshot...
[[17:19:30]] [SUCCESS] Screenshot refreshed successfully
[[17:19:30]] [SUCCESS] Screenshot refreshed successfully
[[17:19:29]] [INFO] Executing action 53/512: Restart app: env[appid]
[[17:19:29]] [SUCCESS] Screenshot refreshed
[[17:19:29]] [INFO] Refreshing screenshot...
[[17:19:26]] [SUCCESS] Screenshot refreshed successfully
[[17:19:26]] [SUCCESS] Screenshot refreshed successfully
[[17:19:26]] [INFO] Executing action 52/512: Terminate app: env[appid]
[[17:19:25]] [SUCCESS] Screenshot refreshed
[[17:19:25]] [INFO] Refreshing screenshot...
[[17:19:22]] [SUCCESS] Screenshot refreshed successfully
[[17:19:22]] [SUCCESS] Screenshot refreshed successfully
[[17:19:22]] [INFO] Executing action 51/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:19:21]] [SUCCESS] Screenshot refreshed
[[17:19:21]] [INFO] Refreshing screenshot...
[[17:19:17]] [SUCCESS] Screenshot refreshed successfully
[[17:19:17]] [SUCCESS] Screenshot refreshed successfully
[[17:19:17]] [INFO] Executing action 50/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:19:17]] [SUCCESS] Screenshot refreshed
[[17:19:17]] [INFO] Refreshing screenshot...
[[17:19:11]] [INFO] Executing action 49/512: swipeTillVisible action
[[17:19:11]] [SUCCESS] Screenshot refreshed successfully
[[17:19:11]] [SUCCESS] Screenshot refreshed successfully
[[17:19:10]] [SUCCESS] Screenshot refreshed
[[17:19:10]] [INFO] Refreshing screenshot...
[[17:19:06]] [SUCCESS] Screenshot refreshed successfully
[[17:19:06]] [SUCCESS] Screenshot refreshed successfully
[[17:19:06]] [INFO] Executing action 48/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:19:06]] [SUCCESS] Screenshot refreshed
[[17:19:06]] [INFO] Refreshing screenshot...
[[17:18:59]] [SUCCESS] Screenshot refreshed successfully
[[17:18:59]] [SUCCESS] Screenshot refreshed successfully
[[17:18:59]] [INFO] Executing action 47/512: Tap on element with accessibility_id: Add to bag
[[17:18:59]] [SUCCESS] Screenshot refreshed
[[17:18:59]] [INFO] Refreshing screenshot...
[[17:18:55]] [SUCCESS] Screenshot refreshed successfully
[[17:18:55]] [SUCCESS] Screenshot refreshed successfully
[[17:18:55]] [INFO] Executing action 46/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:18:54]] [SUCCESS] Screenshot refreshed
[[17:18:54]] [INFO] Refreshing screenshot...
[[17:18:51]] [SUCCESS] Screenshot refreshed successfully
[[17:18:51]] [SUCCESS] Screenshot refreshed successfully
[[17:18:50]] [INFO] Executing action 45/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[17:18:50]] [SUCCESS] Screenshot refreshed
[[17:18:50]] [INFO] Refreshing screenshot...
[[17:18:47]] [SUCCESS] Screenshot refreshed successfully
[[17:18:47]] [SUCCESS] Screenshot refreshed successfully
[[17:18:46]] [INFO] Executing action 44/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:18:46]] [SUCCESS] Screenshot refreshed
[[17:18:46]] [INFO] Refreshing screenshot...
[[17:18:42]] [SUCCESS] Screenshot refreshed successfully
[[17:18:42]] [SUCCESS] Screenshot refreshed successfully
[[17:18:42]] [INFO] Executing action 43/512: iOS Function: text
[[17:18:41]] [SUCCESS] Screenshot refreshed
[[17:18:41]] [INFO] Refreshing screenshot...
[[17:18:38]] [SUCCESS] Screenshot refreshed successfully
[[17:18:38]] [SUCCESS] Screenshot refreshed successfully
[[17:18:38]] [INFO] Executing action 42/512: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:18:37]] [SUCCESS] Screenshot refreshed
[[17:18:37]] [INFO] Refreshing screenshot...
[[17:18:34]] [SUCCESS] Screenshot refreshed successfully
[[17:18:34]] [SUCCESS] Screenshot refreshed successfully
[[17:18:34]] [INFO] Executing action 41/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[17:18:33]] [SUCCESS] Screenshot refreshed
[[17:18:33]] [INFO] Refreshing screenshot...
[[17:18:29]] [SUCCESS] Screenshot refreshed successfully
[[17:18:29]] [SUCCESS] Screenshot refreshed successfully
[[17:18:29]] [INFO] Executing action 40/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[17:18:29]] [SUCCESS] Screenshot refreshed
[[17:18:29]] [INFO] Refreshing screenshot...
[[17:18:25]] [SUCCESS] Screenshot refreshed successfully
[[17:18:25]] [SUCCESS] Screenshot refreshed successfully
[[17:18:25]] [INFO] Executing action 39/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:18:24]] [SUCCESS] Screenshot refreshed
[[17:18:24]] [INFO] Refreshing screenshot...
[[17:18:21]] [SUCCESS] Screenshot refreshed successfully
[[17:18:21]] [SUCCESS] Screenshot refreshed successfully
[[17:18:21]] [INFO] Executing action 38/512: iOS Function: text
[[17:18:20]] [SUCCESS] Screenshot refreshed
[[17:18:20]] [INFO] Refreshing screenshot...
[[17:18:17]] [SUCCESS] Screenshot refreshed successfully
[[17:18:17]] [SUCCESS] Screenshot refreshed successfully
[[17:18:17]] [INFO] Executing action 37/512: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:18:16]] [SUCCESS] Screenshot refreshed
[[17:18:16]] [INFO] Refreshing screenshot...
[[17:18:13]] [SUCCESS] Screenshot refreshed successfully
[[17:18:13]] [SUCCESS] Screenshot refreshed successfully
[[17:18:13]] [INFO] Executing action 36/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[17:18:12]] [SUCCESS] Screenshot refreshed
[[17:18:12]] [INFO] Refreshing screenshot...
[[17:18:07]] [SUCCESS] Screenshot refreshed successfully
[[17:18:07]] [SUCCESS] Screenshot refreshed successfully
[[17:18:07]] [INFO] Executing action 35/512: Restart app: env[appid]
[[17:18:07]] [SUCCESS] Screenshot refreshed
[[17:18:07]] [INFO] Refreshing screenshot...
[[17:18:03]] [SUCCESS] Screenshot refreshed successfully
[[17:18:03]] [SUCCESS] Screenshot refreshed successfully
[[17:18:03]] [INFO] Executing action 34/512: Tap on image: env[device-back-img]
[[17:18:02]] [SUCCESS] Screenshot refreshed
[[17:18:02]] [INFO] Refreshing screenshot...
[[17:18:00]] [SUCCESS] Screenshot refreshed successfully
[[17:18:00]] [SUCCESS] Screenshot refreshed successfully
[[17:18:00]] [INFO] Executing action 33/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[17:17:59]] [SUCCESS] Screenshot refreshed
[[17:17:59]] [INFO] Refreshing screenshot...
[[17:17:55]] [SUCCESS] Screenshot refreshed successfully
[[17:17:55]] [SUCCESS] Screenshot refreshed successfully
[[17:17:55]] [INFO] Executing action 32/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[17:17:55]] [SUCCESS] Screenshot refreshed
[[17:17:55]] [INFO] Refreshing screenshot...
[[17:17:51]] [SUCCESS] Screenshot refreshed successfully
[[17:17:51]] [SUCCESS] Screenshot refreshed successfully
[[17:17:51]] [INFO] Executing action 31/512: Tap on image: env[paypal-close-img]
[[17:17:50]] [SUCCESS] Screenshot refreshed
[[17:17:50]] [INFO] Refreshing screenshot...
[[17:17:44]] [SUCCESS] Screenshot refreshed successfully
[[17:17:44]] [SUCCESS] Screenshot refreshed successfully
[[17:17:44]] [INFO] Executing action 30/512: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[17:17:44]] [SUCCESS] Screenshot refreshed
[[17:17:44]] [INFO] Refreshing screenshot...
[[17:17:40]] [SUCCESS] Screenshot refreshed successfully
[[17:17:40]] [SUCCESS] Screenshot refreshed successfully
[[17:17:40]] [INFO] Executing action 29/512: Tap on image: env[device-back-img]
[[17:17:39]] [SUCCESS] Screenshot refreshed
[[17:17:39]] [INFO] Refreshing screenshot...
[[17:17:37]] [SUCCESS] Screenshot refreshed successfully
[[17:17:37]] [SUCCESS] Screenshot refreshed successfully
[[17:17:37]] [INFO] Executing action 28/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[17:17:36]] [SUCCESS] Screenshot refreshed
[[17:17:36]] [INFO] Refreshing screenshot...
[[17:17:31]] [SUCCESS] Screenshot refreshed successfully
[[17:17:31]] [SUCCESS] Screenshot refreshed successfully
[[17:17:31]] [INFO] Executing action 27/512: Tap on element with accessibility_id: Learn more about Zip
[[17:17:30]] [SUCCESS] Screenshot refreshed
[[17:17:30]] [INFO] Refreshing screenshot...
[[17:17:26]] [SUCCESS] Screenshot refreshed successfully
[[17:17:26]] [SUCCESS] Screenshot refreshed successfully
[[17:17:26]] [INFO] Executing action 26/512: Tap on image: env[device-back-img]
[[17:17:26]] [SUCCESS] Screenshot refreshed
[[17:17:26]] [INFO] Refreshing screenshot...
[[17:17:23]] [SUCCESS] Screenshot refreshed successfully
[[17:17:23]] [SUCCESS] Screenshot refreshed successfully
[[17:17:23]] [INFO] Executing action 25/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[17:17:22]] [SUCCESS] Screenshot refreshed
[[17:17:22]] [INFO] Refreshing screenshot...
[[17:17:17]] [SUCCESS] Screenshot refreshed successfully
[[17:17:17]] [SUCCESS] Screenshot refreshed successfully
[[17:17:17]] [INFO] Executing action 24/512: Tap on element with accessibility_id: Learn more about AfterPay
[[17:17:16]] [SUCCESS] Screenshot refreshed
[[17:17:16]] [INFO] Refreshing screenshot...
[[17:17:10]] [SUCCESS] Screenshot refreshed successfully
[[17:17:10]] [SUCCESS] Screenshot refreshed successfully
[[17:17:09]] [INFO] Executing action 23/512: swipeTillVisible action
[[17:17:09]] [SUCCESS] Screenshot refreshed
[[17:17:09]] [INFO] Refreshing screenshot...
[[17:17:05]] [SUCCESS] Screenshot refreshed successfully
[[17:17:05]] [SUCCESS] Screenshot refreshed successfully
[[17:17:04]] [INFO] Executing action 22/512: Tap on image: env[closebtnimage]
[[17:17:03]] [SUCCESS] Screenshot refreshed
[[17:17:03]] [INFO] Refreshing screenshot...
[[17:17:00]] [SUCCESS] Screenshot refreshed successfully
[[17:17:00]] [SUCCESS] Screenshot refreshed successfully
[[17:17:00]] [INFO] Executing action 21/512: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[17:16:59]] [SUCCESS] Screenshot refreshed
[[17:16:59]] [INFO] Refreshing screenshot...
[[17:16:55]] [SUCCESS] Screenshot refreshed successfully
[[17:16:55]] [SUCCESS] Screenshot refreshed successfully
[[17:16:55]] [INFO] Executing action 20/512: Tap on image: env[product-share-img]
[[17:16:54]] [SUCCESS] Screenshot refreshed
[[17:16:54]] [INFO] Refreshing screenshot...
[[17:16:20]] [SUCCESS] Screenshot refreshed successfully
[[17:16:20]] [SUCCESS] Screenshot refreshed successfully
[[17:16:19]] [INFO] Executing action 19/512: Tap on element with xpath:  (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* with fallback: Coordinates (98, 308)
[[17:16:19]] [SUCCESS] Screenshot refreshed
[[17:16:19]] [INFO] Refreshing screenshot...
[[17:16:16]] [SUCCESS] Screenshot refreshed successfully
[[17:16:16]] [SUCCESS] Screenshot refreshed successfully
[[17:16:15]] [INFO] Executing action 18/512: Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*
[[17:16:15]] [SUCCESS] Screenshot refreshed
[[17:16:15]] [INFO] Refreshing screenshot...
[[17:16:03]] [SUCCESS] Screenshot refreshed successfully
[[17:16:03]] [SUCCESS] Screenshot refreshed successfully
[[17:16:03]] [INFO] Executing action 17/512: Wait for 10 ms
[[17:16:02]] [SUCCESS] Screenshot refreshed
[[17:16:02]] [INFO] Refreshing screenshot...
[[17:15:57]] [SUCCESS] Screenshot refreshed successfully
[[17:15:57]] [SUCCESS] Screenshot refreshed successfully
[[17:15:57]] [INFO] Executing action 16/512: Swipe from (50%, 70%) to (50%, 30%)
[[17:15:56]] [SUCCESS] Screenshot refreshed
[[17:15:56]] [INFO] Refreshing screenshot...
[[17:15:53]] [SUCCESS] Screenshot refreshed successfully
[[17:15:53]] [SUCCESS] Screenshot refreshed successfully
[[17:15:52]] [INFO] Executing action 15/512: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[17:15:52]] [SUCCESS] Screenshot refreshed
[[17:15:52]] [INFO] Refreshing screenshot...
[[17:15:49]] [SUCCESS] Screenshot refreshed successfully
[[17:15:49]] [SUCCESS] Screenshot refreshed successfully
[[17:15:49]] [INFO] Executing action 14/512: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[17:15:48]] [SUCCESS] Screenshot refreshed
[[17:15:48]] [INFO] Refreshing screenshot...
[[17:15:45]] [SUCCESS] Screenshot refreshed successfully
[[17:15:45]] [SUCCESS] Screenshot refreshed successfully
[[17:15:44]] [INFO] Executing action 13/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show")]
[[17:15:44]] [SUCCESS] Screenshot refreshed
[[17:15:44]] [INFO] Refreshing screenshot...
[[17:15:41]] [SUCCESS] Screenshot refreshed successfully
[[17:15:41]] [SUCCESS] Screenshot refreshed successfully
[[17:15:40]] [INFO] Executing action 12/512: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[17:15:40]] [SUCCESS] Screenshot refreshed
[[17:15:40]] [INFO] Refreshing screenshot...
[[17:15:37]] [SUCCESS] Screenshot refreshed successfully
[[17:15:37]] [SUCCESS] Screenshot refreshed successfully
[[17:15:36]] [INFO] Executing action 11/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[17:15:36]] [SUCCESS] Screenshot refreshed
[[17:15:36]] [INFO] Refreshing screenshot...
[[17:15:33]] [SUCCESS] Screenshot refreshed successfully
[[17:15:33]] [SUCCESS] Screenshot refreshed successfully
[[17:15:32]] [INFO] Executing action 10/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:15:32]] [SUCCESS] Screenshot refreshed
[[17:15:32]] [INFO] Refreshing screenshot...
[[17:15:27]] [SUCCESS] Screenshot refreshed successfully
[[17:15:27]] [SUCCESS] Screenshot refreshed successfully
[[17:15:27]] [INFO] Executing action 9/512: Tap on Text: "Latest"
[[17:15:27]] [SUCCESS] Screenshot refreshed
[[17:15:27]] [INFO] Refreshing screenshot...
[[17:15:23]] [SUCCESS] Screenshot refreshed successfully
[[17:15:23]] [SUCCESS] Screenshot refreshed successfully
[[17:15:23]] [INFO] Executing action 8/512: Tap on Text: "Toys"
[[17:15:22]] [SUCCESS] Screenshot refreshed
[[17:15:22]] [INFO] Refreshing screenshot...
[[17:15:18]] [SUCCESS] Screenshot refreshed successfully
[[17:15:18]] [SUCCESS] Screenshot refreshed successfully
[[17:15:18]] [INFO] Executing action 7/512: Tap on image: env[device-back-img]
[[17:15:17]] [SUCCESS] Screenshot refreshed
[[17:15:17]] [INFO] Refreshing screenshot...
[[17:15:15]] [SUCCESS] Screenshot refreshed successfully
[[17:15:15]] [SUCCESS] Screenshot refreshed successfully
[[17:15:15]] [INFO] Executing action 6/512: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[17:15:14]] [SUCCESS] Screenshot refreshed
[[17:15:14]] [INFO] Refreshing screenshot...
[[17:15:12]] [SUCCESS] Screenshot refreshed successfully
[[17:15:12]] [SUCCESS] Screenshot refreshed successfully
[[17:15:11]] [INFO] Executing action 5/512: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[17:15:11]] [SUCCESS] Screenshot refreshed
[[17:15:11]] [INFO] Refreshing screenshot...
[[17:15:07]] [SUCCESS] Screenshot refreshed successfully
[[17:15:07]] [SUCCESS] Screenshot refreshed successfully
[[17:15:07]] [INFO] Executing action 4/512: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:15:07]] [SUCCESS] Screenshot refreshed
[[17:15:07]] [INFO] Refreshing screenshot...
[[17:15:04]] [SUCCESS] Screenshot refreshed successfully
[[17:15:04]] [SUCCESS] Screenshot refreshed successfully
[[17:15:04]] [INFO] Executing action 3/512: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:15:03]] [SUCCESS] Screenshot refreshed
[[17:15:03]] [INFO] Refreshing screenshot...
[[17:15:00]] [SUCCESS] Screenshot refreshed successfully
[[17:15:00]] [SUCCESS] Screenshot refreshed successfully
[[17:15:00]] [INFO] Executing action 2/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[17:14:59]] [SUCCESS] Screenshot refreshed
[[17:14:59]] [INFO] Refreshing screenshot...
[[17:14:54]] [INFO] Executing action 1/512: Restart app: env[appid]
[[17:14:54]] [INFO] ExecutionManager: Starting execution of 512 actions...
[[17:14:54]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[17:14:54]] [INFO] Clearing screenshots from database before execution...
[[17:14:54]] [SUCCESS] All screenshots deleted successfully
[[17:14:54]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:14:54]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_171454/screenshots
[[17:14:54]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_171454
[[17:14:54]] [SUCCESS] Report directory initialized successfully
[[17:14:54]] [INFO] Initializing report directory and screenshots folder...
[[17:14:49]] [SUCCESS] All screenshots deleted successfully
[[17:14:49]] [INFO] All actions cleared
[[17:14:49]] [INFO] Cleaning up screenshots...
[[17:14:44]] [SUCCESS] Screenshot refreshed successfully
[[17:14:43]] [SUCCESS] Screenshot refreshed
[[17:14:43]] [INFO] Refreshing screenshot...
[[17:14:42]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:14:42]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:14:35]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:14:32]] [SUCCESS] Found 1 device(s)
[[17:14:32]] [INFO] Refreshing device list...
