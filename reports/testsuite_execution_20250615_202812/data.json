{"name": "UI Execution 15/06/2025, 20:30:43", "testCases": [{"name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1370ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "screenshot": "screenshots/A78UkQrCdd.png", "clean_action_id": "A78UkQrCdd", "prefixed_action_id": "al_A78UkQrCdd", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Add Log: Launched App Successfully (with screenshot)", "status": "passed", "duration": "5ms", "action_id": "4yvdd4fOVD", "screenshot_filename": "4yvdd4fOVD.png", "report_screenshot": "4yvdd4fOVD.png", "resolved_screenshot": "screenshots/4yvdd4fOVD.png", "screenshot": "screenshots/4yvdd4fOVD.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1151ms", "action_id": "nFymuzyXXh", "screenshot_filename": "nFymuzyXXh.png", "report_screenshot": "nFymuzyXXh.png", "resolved_screenshot": "screenshots/nFymuzyXXh.png", "screenshot": "screenshots/nFymuzyXXh.png"}, {"name": "Add Log: Edit link is clicked (with screenshot)", "status": "passed", "duration": "7ms", "action_id": "MShREqmDz8", "screenshot_filename": "MShREqmDz8.png", "report_screenshot": "MShREqmDz8.png", "resolved_screenshot": "screenshots/MShREqmDz8.png", "screenshot": "screenshots/MShREqmDz8.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1541ms", "action_id": "Rx1oAc9kag", "screenshot_filename": "Rx1oAc9kag.png", "report_screenshot": "Rx1oAc9kag.png", "resolved_screenshot": "screenshots/Rx1oAc9kag.png", "screenshot": "screenshots/Rx1oAc9kag.png"}, {"name": "Add Log: Done link is clicked (with screenshot)", "status": "passed", "duration": "7ms", "action_id": "DrTLZuUPCa", "screenshot_filename": "DrTLZuUPCa.png", "report_screenshot": "DrTLZuUPCa.png", "resolved_screenshot": "screenshots/DrTLZuUPCa.png", "screenshot": "screenshots/DrTLZuUPCa.png"}, {"name": "Wait for 1 ms", "status": "passed", "duration": "1006ms", "action_id": "KzjZOcsLuC", "screenshot_filename": "KzjZOcsLuC.png", "report_screenshot": "KzjZOcsLuC.png", "resolved_screenshot": "screenshots/KzjZOcsLuC.png", "screenshot": "screenshots/PxDTh4K1ni.png", "clean_action_id": "PxDTh4K1ni", "prefixed_action_id": "al_PxDTh4K1ni", "action_id_screenshot": "screenshots/KzjZOcsLuC.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1061ms", "action_id": "OqYf9xF3oX", "screenshot_filename": "OqYf9xF3oX.png", "report_screenshot": "OqYf9xF3oX.png", "resolved_screenshot": "screenshots/OqYf9xF3oX.png", "screenshot": "screenshots/Qov8gH1qiV.png", "clean_action_id": "Qov8gH1qiV", "prefixed_action_id": "al_Qov8gH1qiV", "action_id_screenshot": "screenshots/OqYf9xF3oX.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "5ms", "action_id": "SRUQFk60mz", "screenshot_filename": "SRUQFk60mz.png", "report_screenshot": "SRUQFk60mz.png", "resolved_screenshot": "screenshots/SRUQFk60mz.png", "screenshot": "screenshots/SRUQFk60mz.png"}], "retry_info": {"retry_count": 0, "max_retries": 0, "was_retried": false}, "display_name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"}, {"name": "apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "failed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1170ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "screenshot": "screenshots/Yl3FmK12jj.png", "clean_action_id": "Yl3FmK12jj", "prefixed_action_id": "al_Yl3FmK12jj", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "858ms", "action_id": "ZFUrBrteUE", "screenshot_filename": "ZFUrBrteUE.png", "report_screenshot": "ZFUrBrteUE.png", "resolved_screenshot": "screenshots/ZFUrBrteUE.png", "screenshot": "screenshots/ZFUrBrteUE.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1559ms", "action_id": "j2RxggNONv", "screenshot_filename": "j2RxggNONv.png", "report_screenshot": "j2RxggNONv.png", "resolved_screenshot": "screenshots/j2RxggNONv.png", "screenshot": "screenshots/j2RxggNONv.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1052ms", "action_id": "E5An5BbVuK", "screenshot_filename": "E5An5BbVuK.png", "report_screenshot": "E5An5BbVuK.png", "resolved_screenshot": "screenshots/E5An5BbVuK.png", "screenshot": "screenshots/50qAzOuUMo.png", "clean_action_id": "50qAzOuUMo", "prefixed_action_id": "al_50qAzOuUMo", "action_id_screenshot": "screenshots/E5An5BbVuK.png"}, {"name": "Add Log: Closed App Successfully (with screenshot)", "status": "passed", "duration": "6ms", "action_id": "ZKP7rdF8Qg", "screenshot_filename": "ZKP7rdF8Qg.png", "report_screenshot": "ZKP7rdF8Qg.png", "resolved_screenshot": "screenshots/ZKP7rdF8Qg.png", "screenshot": "screenshots/ZKP7rdF8Qg.png"}, {"name": "Execute Test Case: health2 (9 steps)", "status": "failed", "duration": "0ms", "action_id": "KfOSdvcOkk", "screenshot_filename": "KfOSdvcOkk.png", "report_screenshot": "KfOSdvcOkk.png", "resolved_screenshot": "screenshots/KfOSdvcOkk.png", "screenshot": "screenshots/IYaVBCKJv4.png", "clean_action_id": "IYaVBCKJv4", "prefixed_action_id": "al_IYaVBCKJv4", "action_id_screenshot": "screenshots/KfOSdvcOkk.png"}, {"name": "Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)", "status": "unknown", "duration": "0ms", "action_id": "0lLDk9qlTv", "screenshot_filename": "0lLDk9qlTv.png", "report_screenshot": "0lLDk9qlTv.png", "resolved_screenshot": "screenshots/0lLDk9qlTv.png", "screenshot": "screenshots/0lLDk9qlTv.png"}], "retry_info": {"retry_count": 0, "max_retries": 0, "was_retried": false}, "display_name": "apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions"}, {"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "50ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "screenshot": "screenshots/xp9G4CRwvv.png", "clean_action_id": "xp9G4CRwvv", "prefixed_action_id": "al_xp9G4CRwvv", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1206ms", "action_id": "nRdIVAvicf", "screenshot_filename": "nRdIVAvicf.png", "report_screenshot": "nRdIVAvicf.png", "resolved_screenshot": "screenshots/nRdIVAvicf.png", "screenshot": "screenshots/nRdIVAvicf.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1561ms", "action_id": "ReNxYmZNbT", "screenshot_filename": "ReNxYmZNbT.png", "report_screenshot": "ReNxYmZNbT.png", "resolved_screenshot": "screenshots/ReNxYmZNbT.png", "screenshot": "screenshots/ReNxYmZNbT.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1289ms", "action_id": "owEpfdRxU0", "screenshot_filename": "owEpfdRxU0.png", "report_screenshot": "owEpfdRxU0.png", "resolved_screenshot": "screenshots/owEpfdRxU0.png", "screenshot": "screenshots/owEpfdRxU0.png"}, {"name": "Add Log: Clicked on Edit link successfully (with screenshot)", "status": "passed", "duration": "6ms", "action_id": "KD98hePhs2", "screenshot_filename": "KD98hePhs2.png", "report_screenshot": "KD98hePhs2.png", "resolved_screenshot": "screenshots/KD98hePhs2.png", "screenshot": "screenshots/KD98hePhs2.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1559ms", "action_id": "H7I2GUMcg9", "screenshot_filename": "H7I2GUMcg9.png", "report_screenshot": "H7I2GUMcg9.png", "resolved_screenshot": "screenshots/H7I2GUMcg9.png", "screenshot": "screenshots/H7I2GUMcg9.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1024ms", "action_id": "KzjZOcsLuC", "screenshot_filename": "KzjZOcsLuC.png", "report_screenshot": "KzjZOcsLuC.png", "resolved_screenshot": "screenshots/KzjZOcsLuC.png", "screenshot": "screenshots/c23P5XysdU.png", "clean_action_id": "c23P5XysdU", "prefixed_action_id": "al_c23P5XysdU", "action_id_screenshot": "screenshots/KzjZOcsLuC.png"}, {"name": "Execute Test Case: apple health (8 steps)", "status": "passed", "duration": "0ms", "action_id": "OqYf9xF3oX", "screenshot_filename": "OqYf9xF3oX.png", "report_screenshot": "OqYf9xF3oX.png", "resolved_screenshot": "screenshots/OqYf9xF3oX.png", "screenshot": "screenshots/k9H1SHyZgM.png", "clean_action_id": "k9H1SHyZgM", "prefixed_action_id": "al_k9H1SHyZgM", "action_id_screenshot": "screenshots/OqYf9xF3oX.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "10ms", "action_id": "4j3rdXxNzv", "screenshot_filename": "4j3rdXxNzv.png", "report_screenshot": "4j3rdXxNzv.png", "resolved_screenshot": "screenshots/4j3rdXxNzv.png", "screenshot": "screenshots/4j3rdXxNzv.png"}], "retry_info": {"retry_count": 0, "max_retries": 0, "was_retried": false}, "display_name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"}], "passed": 2, "failed": 1, "skipped": 0, "status": "failed"}