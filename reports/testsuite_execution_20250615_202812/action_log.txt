Action Log - 2025-06-15 20:30:44
================================================================================

[[20:30:43]] [INFO] Generating execution report...
[[20:30:43]] [WARNING] 1 test failed.
[[20:30:43]] [SUCCESS] Screenshot refreshed
[[20:30:43]] [INFO] Refreshing screenshot...
[[20:30:42]] [SUCCESS] Screenshot refreshed successfully
[[20:30:42]] [SUCCESS] Screenshot refreshed successfully
[[20:30:41]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[20:30:41]] [SUCCESS] Screenshot refreshed
[[20:30:41]] [INFO] Refreshing screenshot...
[[20:30:40]] [SUCCESS] Screenshot refreshed
[[20:30:40]] [INFO] Refreshing screenshot...
[[20:30:39]] [SUCCESS] Screenshot refreshed successfully
[[20:30:39]] [SUCCESS] Screenshot refreshed successfully
[[20:30:38]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[20:30:38]] [SUCCESS] Screenshot refreshed
[[20:30:38]] [INFO] Refreshing screenshot...
[[20:30:24]] [SUCCESS] Screenshot refreshed successfully
[[20:30:24]] [SUCCESS] Screenshot refreshed successfully
[[20:30:24]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[20:30:23]] [SUCCESS] Screenshot refreshed
[[20:30:23]] [INFO] Refreshing screenshot...
[[20:30:20]] [SUCCESS] Screenshot refreshed successfully
[[20:30:20]] [SUCCESS] Screenshot refreshed successfully
[[20:30:20]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[20:30:19]] [SUCCESS] Screenshot refreshed
[[20:30:19]] [INFO] Refreshing screenshot...
[[20:30:16]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:30:16]] [SUCCESS] Screenshot refreshed successfully
[[20:30:16]] [SUCCESS] Screenshot refreshed successfully
[[20:30:16]] [SUCCESS] Screenshot refreshed
[[20:30:16]] [INFO] Refreshing screenshot...
[[20:30:14]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[20:30:14]] [SUCCESS] Screenshot refreshed successfully
[[20:30:14]] [SUCCESS] Screenshot refreshed successfully
[[20:30:14]] [SUCCESS] Screenshot refreshed
[[20:30:14]] [INFO] Refreshing screenshot...
[[20:30:11]] [SUCCESS] Screenshot refreshed successfully
[[20:30:11]] [SUCCESS] Screenshot refreshed successfully
[[20:30:11]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:30:11]] [SUCCESS] Screenshot refreshed
[[20:30:11]] [INFO] Refreshing screenshot...
[[20:30:08]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:30:07]] [SUCCESS] Screenshot refreshed successfully
[[20:30:07]] [SUCCESS] Screenshot refreshed successfully
[[20:30:07]] [SUCCESS] Screenshot refreshed
[[20:30:07]] [INFO] Refreshing screenshot...
[[20:30:04]] [SUCCESS] Screenshot refreshed successfully
[[20:30:04]] [SUCCESS] Screenshot refreshed successfully
[[20:30:04]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:30:04]] [SUCCESS] Screenshot refreshed
[[20:30:04]] [INFO] Refreshing screenshot...
[[20:30:00]] [SUCCESS] Screenshot refreshed successfully
[[20:30:00]] [SUCCESS] Screenshot refreshed successfully
[[20:29:59]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[20:29:59]] [INFO] Loaded 9 steps from test case: apple health
[[20:29:59]] [INFO] Loading steps for Multi Step action: apple health
[[20:29:59]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[20:29:59]] [SUCCESS] Screenshot refreshed
[[20:29:59]] [INFO] Refreshing screenshot...
[[20:29:56]] [SUCCESS] Screenshot refreshed successfully
[[20:29:56]] [SUCCESS] Screenshot refreshed successfully
[[20:29:55]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[20:29:55]] [SUCCESS] Screenshot refreshed
[[20:29:55]] [INFO] Refreshing screenshot...
[[20:29:52]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:29:52]] [SUCCESS] Screenshot refreshed successfully
[[20:29:52]] [SUCCESS] Screenshot refreshed successfully
[[20:29:51]] [SUCCESS] Screenshot refreshed
[[20:29:51]] [INFO] Refreshing screenshot...
[[20:29:50]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[20:29:50]] [SUCCESS] Screenshot refreshed successfully
[[20:29:50]] [SUCCESS] Screenshot refreshed successfully
[[20:29:49]] [SUCCESS] Screenshot refreshed
[[20:29:49]] [INFO] Refreshing screenshot...
[[20:29:47]] [SUCCESS] Screenshot refreshed successfully
[[20:29:47]] [SUCCESS] Screenshot refreshed successfully
[[20:29:47]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:29:46]] [SUCCESS] Screenshot refreshed
[[20:29:46]] [INFO] Refreshing screenshot...
[[20:29:43]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:29:43]] [SUCCESS] Screenshot refreshed successfully
[[20:29:43]] [SUCCESS] Screenshot refreshed successfully
[[20:29:43]] [SUCCESS] Screenshot refreshed
[[20:29:43]] [INFO] Refreshing screenshot...
[[20:29:40]] [SUCCESS] Screenshot refreshed successfully
[[20:29:40]] [SUCCESS] Screenshot refreshed successfully
[[20:29:40]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:29:39]] [SUCCESS] Screenshot refreshed
[[20:29:39]] [INFO] Refreshing screenshot...
[[20:29:28]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[20:29:28]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[20:29:28]] [INFO] Moving to the next test case after failure (server will handle retry)
[[20:29:28]] [ERROR] Multi Step action step 5 failed: Element with xpath ' //XCUIElementTypeButton[@name="Done"]' not clickable within timeout of 10 seconds
[[20:29:16]] [SUCCESS] Screenshot refreshed successfully
[[20:29:16]] [SUCCESS] Screenshot refreshed successfully
[[20:29:16]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:29:16]] [SUCCESS] Screenshot refreshed
[[20:29:16]] [INFO] Refreshing screenshot...
[[20:29:14]] [SUCCESS] Screenshot refreshed successfully
[[20:29:14]] [SUCCESS] Screenshot refreshed successfully
[[20:29:14]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[20:29:14]] [SUCCESS] Screenshot refreshed
[[20:29:14]] [INFO] Refreshing screenshot...
[[20:29:11]] [SUCCESS] Screenshot refreshed successfully
[[20:29:11]] [SUCCESS] Screenshot refreshed successfully
[[20:29:11]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:29:10]] [SUCCESS] Screenshot refreshed
[[20:29:10]] [INFO] Refreshing screenshot...
[[20:29:09]] [SUCCESS] Screenshot refreshed successfully
[[20:29:09]] [SUCCESS] Screenshot refreshed successfully
[[20:29:09]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[20:29:08]] [SUCCESS] Screenshot refreshed
[[20:29:08]] [INFO] Refreshing screenshot...
[[20:29:04]] [SUCCESS] Screenshot refreshed successfully
[[20:29:04]] [SUCCESS] Screenshot refreshed successfully
[[20:29:03]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[20:29:03]] [INFO] Loaded 9 steps from test case: health2
[[20:29:03]] [INFO] Loading steps for Multi Step action: health2
[[20:29:03]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[20:29:03]] [SUCCESS] Screenshot refreshed
[[20:29:03]] [INFO] Refreshing screenshot...
[[20:29:01]] [SUCCESS] Screenshot refreshed successfully
[[20:29:01]] [SUCCESS] Screenshot refreshed successfully
[[20:29:01]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[20:29:00]] [SUCCESS] Screenshot refreshed
[[20:29:00]] [INFO] Refreshing screenshot...
[[20:28:57]] [SUCCESS] Screenshot refreshed successfully
[[20:28:57]] [SUCCESS] Screenshot refreshed successfully
[[20:28:57]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[20:28:57]] [SUCCESS] Screenshot refreshed
[[20:28:57]] [INFO] Refreshing screenshot...
[[20:28:53]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:28:53]] [SUCCESS] Screenshot refreshed successfully
[[20:28:53]] [SUCCESS] Screenshot refreshed successfully
[[20:28:53]] [SUCCESS] Screenshot refreshed
[[20:28:53]] [INFO] Refreshing screenshot...
[[20:28:51]] [SUCCESS] Screenshot refreshed successfully
[[20:28:51]] [SUCCESS] Screenshot refreshed successfully
[[20:28:51]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[20:28:50]] [SUCCESS] Screenshot refreshed
[[20:28:50]] [INFO] Refreshing screenshot...
[[20:28:38]] [SUCCESS] Screenshot refreshed successfully
[[20:28:38]] [SUCCESS] Screenshot refreshed successfully
[[20:28:37]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[20:28:37]] [SUCCESS] Screenshot refreshed
[[20:28:37]] [INFO] Refreshing screenshot...
[[20:28:35]] [SUCCESS] Screenshot refreshed successfully
[[20:28:35]] [SUCCESS] Screenshot refreshed successfully
[[20:28:35]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[20:28:34]] [SUCCESS] Screenshot refreshed
[[20:28:34]] [INFO] Refreshing screenshot...
[[20:28:31]] [SUCCESS] Screenshot refreshed successfully
[[20:28:31]] [SUCCESS] Screenshot refreshed successfully
[[20:28:31]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[20:28:31]] [SUCCESS] Screenshot refreshed
[[20:28:31]] [INFO] Refreshing screenshot...
[[20:28:28]] [SUCCESS] Screenshot refreshed successfully
[[20:28:28]] [SUCCESS] Screenshot refreshed successfully
[[20:28:28]] [INFO] Executing action 7/25: Wait for 1 ms
[[20:28:28]] [SUCCESS] Screenshot refreshed
[[20:28:28]] [INFO] Refreshing screenshot...
[[20:28:26]] [SUCCESS] Screenshot refreshed successfully
[[20:28:26]] [SUCCESS] Screenshot refreshed successfully
[[20:28:26]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[20:28:26]] [SUCCESS] Screenshot refreshed
[[20:28:26]] [INFO] Refreshing screenshot...
[[20:28:22]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:28:22]] [SUCCESS] Screenshot refreshed successfully
[[20:28:22]] [SUCCESS] Screenshot refreshed successfully
[[20:28:22]] [SUCCESS] Screenshot refreshed
[[20:28:22]] [INFO] Refreshing screenshot...
[[20:28:20]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[20:28:20]] [SUCCESS] Screenshot refreshed successfully
[[20:28:20]] [SUCCESS] Screenshot refreshed successfully
[[20:28:20]] [SUCCESS] Screenshot refreshed
[[20:28:20]] [INFO] Refreshing screenshot...
[[20:28:17]] [SUCCESS] Screenshot refreshed successfully
[[20:28:17]] [SUCCESS] Screenshot refreshed successfully
[[20:28:17]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:28:17]] [SUCCESS] Screenshot refreshed
[[20:28:17]] [INFO] Refreshing screenshot...
[[20:28:15]] [SUCCESS] Screenshot refreshed successfully
[[20:28:15]] [SUCCESS] Screenshot refreshed successfully
[[20:28:15]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[20:28:15]] [SUCCESS] Screenshot refreshed
[[20:28:15]] [INFO] Refreshing screenshot...
[[20:28:12]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[20:28:12]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[20:28:12]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[20:28:12]] [INFO] Clearing screenshots from database before execution...
[[20:28:12]] [SUCCESS] All screenshots deleted successfully
[[20:28:12]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[20:28:12]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_202812/screenshots
[[20:28:12]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_202812
[[20:28:12]] [SUCCESS] Report directory initialized successfully
[[20:28:12]] [INFO] Initializing report directory and screenshots folder...
[[20:28:08]] [SUCCESS] All screenshots deleted successfully
[[20:28:08]] [INFO] All actions cleared
[[20:28:08]] [INFO] Cleaning up screenshots...
[[20:27:59]] [SUCCESS] Screenshot refreshed successfully
[[20:27:58]] [SUCCESS] Screenshot refreshed
[[20:27:58]] [INFO] Refreshing screenshot...
[[20:27:57]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[20:27:57]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[20:27:51]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[20:27:47]] [SUCCESS] Found 1 device(s)
[[20:27:47]] [INFO] Refreshing device list...
