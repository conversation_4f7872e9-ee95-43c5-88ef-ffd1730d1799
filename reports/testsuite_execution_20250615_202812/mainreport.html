<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/15/2025, 8:30:44 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">2</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 15/06/2025, 20:30:43
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions 
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1370ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="4yvdd4fOVD.png" data-action-id="4yvdd4fOVD" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Launched App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: 4yvdd4fOVD">4yvdd4fOVD</span>
                            </div>
                            <span class="test-step-duration">5ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="nFymuzyXXh.png" data-action-id="nFymuzyXXh" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: nFymuzyXXh">nFymuzyXXh</span>
                            </div>
                            <span class="test-step-duration">1151ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="MShREqmDz8.png" data-action-id="MShREqmDz8" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Edit link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: MShREqmDz8">MShREqmDz8</span>
                            </div>
                            <span class="test-step-duration">7ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="Rx1oAc9kag.png" data-action-id="Rx1oAc9kag" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: Rx1oAc9kag">Rx1oAc9kag</span>
                            </div>
                            <span class="test-step-duration">1541ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="DrTLZuUPCa.png" data-action-id="DrTLZuUPCa" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Done link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: DrTLZuUPCa">DrTLZuUPCa</span>
                            </div>
                            <span class="test-step-duration">7ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="KzjZOcsLuC.png" data-action-id="KzjZOcsLuC" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 1 ms <span class="action-id-badge" title="Action ID: KzjZOcsLuC">KzjZOcsLuC</span>
                            </div>
                            <span class="test-step-duration">1006ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="OqYf9xF3oX.png" data-action-id="OqYf9xF3oX" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: OqYf9xF3oX">OqYf9xF3oX</span>
                            </div>
                            <span class="test-step-duration">1061ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="SRUQFk60mz.png" data-action-id="SRUQFk60mz" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: SRUQFk60mz">SRUQFk60mz</span>
                            </div>
                            <span class="test-step-duration">5ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #2 apple health (Copy)
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            7 actions 
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1170ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="ZFUrBrteUE.png" data-action-id="ZFUrBrteUE" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: ZFUrBrteUE">ZFUrBrteUE</span>
                            </div>
                            <span class="test-step-duration">858ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="j2RxggNONv.png" data-action-id="j2RxggNONv" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: j2RxggNONv">j2RxggNONv</span>
                            </div>
                            <span class="test-step-duration">1559ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="E5An5BbVuK.png" data-action-id="E5An5BbVuK" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: E5An5BbVuK">E5An5BbVuK</span>
                            </div>
                            <span class="test-step-duration">1052ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="ZKP7rdF8Qg.png" data-action-id="ZKP7rdF8Qg" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Closed App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: ZKP7rdF8Qg">ZKP7rdF8Qg</span>
                            </div>
                            <span class="test-step-duration">6ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="failed"
                            data-screenshot="KfOSdvcOkk.png" data-action-id="KfOSdvcOkk" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Execute Test Case: health2 (9 steps) <span class="action-id-badge" title="Action ID: KfOSdvcOkk">KfOSdvcOkk</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="0lLDk9qlTv.png" data-action-id="0lLDk9qlTv" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery) <span class="action-id-badge" title="Action ID: 0lLDk9qlTv">0lLDk9qlTv</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #3 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions 
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-2-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">50ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="nRdIVAvicf.png" data-action-id="nRdIVAvicf" onclick="showStepDetails('step-2-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: nRdIVAvicf">nRdIVAvicf</span>
                            </div>
                            <span class="test-step-duration">1206ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="ReNxYmZNbT.png" data-action-id="ReNxYmZNbT" onclick="showStepDetails('step-2-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: ReNxYmZNbT">ReNxYmZNbT</span>
                            </div>
                            <span class="test-step-duration">1561ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="owEpfdRxU0.png" data-action-id="owEpfdRxU0" onclick="showStepDetails('step-2-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: owEpfdRxU0">owEpfdRxU0</span>
                            </div>
                            <span class="test-step-duration">1289ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="KD98hePhs2.png" data-action-id="KD98hePhs2" onclick="showStepDetails('step-2-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Clicked on Edit link successfully (with screenshot) <span class="action-id-badge" title="Action ID: KD98hePhs2">KD98hePhs2</span>
                            </div>
                            <span class="test-step-duration">6ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="H7I2GUMcg9.png" data-action-id="H7I2GUMcg9" onclick="showStepDetails('step-2-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: H7I2GUMcg9">H7I2GUMcg9</span>
                            </div>
                            <span class="test-step-duration">1559ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="KzjZOcsLuC.png" data-action-id="KzjZOcsLuC" onclick="showStepDetails('step-2-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: KzjZOcsLuC">KzjZOcsLuC</span>
                            </div>
                            <span class="test-step-duration">1024ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="OqYf9xF3oX.png" data-action-id="OqYf9xF3oX" onclick="showStepDetails('step-2-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: apple health (8 steps) <span class="action-id-badge" title="Action ID: OqYf9xF3oX">OqYf9xF3oX</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="4j3rdXxNzv.png" data-action-id="4j3rdXxNzv" onclick="showStepDetails('step-2-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: 4j3rdXxNzv">4j3rdXxNzv</span>
                            </div>
                            <span class="test-step-duration">10ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 15/06/2025, 20:30:43","testCases":[{"name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1370ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","screenshot":"screenshots/A78UkQrCdd.png","clean_action_id":"A78UkQrCdd","prefixed_action_id":"al_A78UkQrCdd","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Add Log: Launched App Successfully (with screenshot)","status":"passed","duration":"5ms","action_id":"4yvdd4fOVD","screenshot_filename":"4yvdd4fOVD.png","report_screenshot":"4yvdd4fOVD.png","resolved_screenshot":"screenshots/4yvdd4fOVD.png","screenshot":"screenshots/4yvdd4fOVD.png","action_id_screenshot":"screenshots/4yvdd4fOVD.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1151ms","action_id":"nFymuzyXXh","screenshot_filename":"nFymuzyXXh.png","report_screenshot":"nFymuzyXXh.png","resolved_screenshot":"screenshots/nFymuzyXXh.png","screenshot":"screenshots/nFymuzyXXh.png","action_id_screenshot":"screenshots/nFymuzyXXh.png"},{"name":"Add Log: Edit link is clicked (with screenshot)","status":"passed","duration":"7ms","action_id":"MShREqmDz8","screenshot_filename":"MShREqmDz8.png","report_screenshot":"MShREqmDz8.png","resolved_screenshot":"screenshots/MShREqmDz8.png","screenshot":"screenshots/MShREqmDz8.png","action_id_screenshot":"screenshots/MShREqmDz8.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1541ms","action_id":"Rx1oAc9kag","screenshot_filename":"Rx1oAc9kag.png","report_screenshot":"Rx1oAc9kag.png","resolved_screenshot":"screenshots/Rx1oAc9kag.png","screenshot":"screenshots/Rx1oAc9kag.png","action_id_screenshot":"screenshots/Rx1oAc9kag.png"},{"name":"Add Log: Done link is clicked (with screenshot)","status":"passed","duration":"7ms","action_id":"DrTLZuUPCa","screenshot_filename":"DrTLZuUPCa.png","report_screenshot":"DrTLZuUPCa.png","resolved_screenshot":"screenshots/DrTLZuUPCa.png","screenshot":"screenshots/DrTLZuUPCa.png","action_id_screenshot":"screenshots/DrTLZuUPCa.png"},{"name":"Wait for 1 ms","status":"passed","duration":"1006ms","action_id":"KzjZOcsLuC","screenshot_filename":"KzjZOcsLuC.png","report_screenshot":"KzjZOcsLuC.png","resolved_screenshot":"screenshots/KzjZOcsLuC.png","screenshot":"screenshots/PxDTh4K1ni.png","clean_action_id":"PxDTh4K1ni","prefixed_action_id":"al_PxDTh4K1ni","action_id_screenshot":"screenshots/KzjZOcsLuC.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1061ms","action_id":"OqYf9xF3oX","screenshot_filename":"OqYf9xF3oX.png","report_screenshot":"OqYf9xF3oX.png","resolved_screenshot":"screenshots/OqYf9xF3oX.png","screenshot":"screenshots/Qov8gH1qiV.png","clean_action_id":"Qov8gH1qiV","prefixed_action_id":"al_Qov8gH1qiV","action_id_screenshot":"screenshots/OqYf9xF3oX.png"},{"name":"Add Log: App is closed (with screenshot)","status":"passed","duration":"5ms","action_id":"SRUQFk60mz","screenshot_filename":"SRUQFk60mz.png","report_screenshot":"SRUQFk60mz.png","resolved_screenshot":"screenshots/SRUQFk60mz.png","screenshot":"screenshots/SRUQFk60mz.png","action_id_screenshot":"screenshots/SRUQFk60mz.png"}],"retry_info":{"retry_count":0,"max_retries":0,"was_retried":false},"display_name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"},{"name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1170ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","screenshot":"screenshots/Yl3FmK12jj.png","clean_action_id":"Yl3FmK12jj","prefixed_action_id":"al_Yl3FmK12jj","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"858ms","action_id":"ZFUrBrteUE","screenshot_filename":"ZFUrBrteUE.png","report_screenshot":"ZFUrBrteUE.png","resolved_screenshot":"screenshots/ZFUrBrteUE.png","screenshot":"screenshots/ZFUrBrteUE.png","action_id_screenshot":"screenshots/ZFUrBrteUE.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1559ms","action_id":"j2RxggNONv","screenshot_filename":"j2RxggNONv.png","report_screenshot":"j2RxggNONv.png","resolved_screenshot":"screenshots/j2RxggNONv.png","screenshot":"screenshots/j2RxggNONv.png","action_id_screenshot":"screenshots/j2RxggNONv.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1052ms","action_id":"E5An5BbVuK","screenshot_filename":"E5An5BbVuK.png","report_screenshot":"E5An5BbVuK.png","resolved_screenshot":"screenshots/E5An5BbVuK.png","screenshot":"screenshots/50qAzOuUMo.png","clean_action_id":"50qAzOuUMo","prefixed_action_id":"al_50qAzOuUMo","action_id_screenshot":"screenshots/E5An5BbVuK.png"},{"name":"Add Log: Closed App Successfully (with screenshot)","status":"passed","duration":"6ms","action_id":"ZKP7rdF8Qg","screenshot_filename":"ZKP7rdF8Qg.png","report_screenshot":"ZKP7rdF8Qg.png","resolved_screenshot":"screenshots/ZKP7rdF8Qg.png","screenshot":"screenshots/ZKP7rdF8Qg.png","action_id_screenshot":"screenshots/ZKP7rdF8Qg.png"},{"name":"Execute Test Case: health2 (9 steps)","status":"failed","duration":"0ms","action_id":"KfOSdvcOkk","screenshot_filename":"KfOSdvcOkk.png","report_screenshot":"KfOSdvcOkk.png","resolved_screenshot":"screenshots/KfOSdvcOkk.png","screenshot":"screenshots/IYaVBCKJv4.png","clean_action_id":"IYaVBCKJv4","prefixed_action_id":"al_IYaVBCKJv4","action_id_screenshot":"screenshots/KfOSdvcOkk.png"},{"name":"Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)","status":"unknown","duration":"0ms","action_id":"0lLDk9qlTv","screenshot_filename":"0lLDk9qlTv.png","report_screenshot":"0lLDk9qlTv.png","resolved_screenshot":"screenshots/0lLDk9qlTv.png","screenshot":"screenshots/0lLDk9qlTv.png","action_id_screenshot":"screenshots/0lLDk9qlTv.png"}],"retry_info":{"retry_count":0,"max_retries":0,"was_retried":false},"display_name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions"},{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"50ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","screenshot":"screenshots/xp9G4CRwvv.png","clean_action_id":"xp9G4CRwvv","prefixed_action_id":"al_xp9G4CRwvv","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1206ms","action_id":"nRdIVAvicf","screenshot_filename":"nRdIVAvicf.png","report_screenshot":"nRdIVAvicf.png","resolved_screenshot":"screenshots/nRdIVAvicf.png","screenshot":"screenshots/nRdIVAvicf.png","action_id_screenshot":"screenshots/nRdIVAvicf.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1561ms","action_id":"ReNxYmZNbT","screenshot_filename":"ReNxYmZNbT.png","report_screenshot":"ReNxYmZNbT.png","resolved_screenshot":"screenshots/ReNxYmZNbT.png","screenshot":"screenshots/ReNxYmZNbT.png","action_id_screenshot":"screenshots/ReNxYmZNbT.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1289ms","action_id":"owEpfdRxU0","screenshot_filename":"owEpfdRxU0.png","report_screenshot":"owEpfdRxU0.png","resolved_screenshot":"screenshots/owEpfdRxU0.png","screenshot":"screenshots/owEpfdRxU0.png","action_id_screenshot":"screenshots/owEpfdRxU0.png"},{"name":"Add Log: Clicked on Edit link successfully (with screenshot)","status":"passed","duration":"6ms","action_id":"KD98hePhs2","screenshot_filename":"KD98hePhs2.png","report_screenshot":"KD98hePhs2.png","resolved_screenshot":"screenshots/KD98hePhs2.png","screenshot":"screenshots/KD98hePhs2.png","action_id_screenshot":"screenshots/KD98hePhs2.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1559ms","action_id":"H7I2GUMcg9","screenshot_filename":"H7I2GUMcg9.png","report_screenshot":"H7I2GUMcg9.png","resolved_screenshot":"screenshots/H7I2GUMcg9.png","screenshot":"screenshots/H7I2GUMcg9.png","action_id_screenshot":"screenshots/H7I2GUMcg9.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1024ms","action_id":"KzjZOcsLuC","screenshot_filename":"KzjZOcsLuC.png","report_screenshot":"KzjZOcsLuC.png","resolved_screenshot":"screenshots/KzjZOcsLuC.png","screenshot":"screenshots/c23P5XysdU.png","clean_action_id":"c23P5XysdU","prefixed_action_id":"al_c23P5XysdU","action_id_screenshot":"screenshots/KzjZOcsLuC.png"},{"name":"Execute Test Case: apple health (8 steps)","status":"passed","duration":"0ms","action_id":"OqYf9xF3oX","screenshot_filename":"OqYf9xF3oX.png","report_screenshot":"OqYf9xF3oX.png","resolved_screenshot":"screenshots/OqYf9xF3oX.png","screenshot":"screenshots/k9H1SHyZgM.png","clean_action_id":"k9H1SHyZgM","prefixed_action_id":"al_k9H1SHyZgM","action_id_screenshot":"screenshots/OqYf9xF3oX.png"},{"name":"Add Log: App is closed (with screenshot)","status":"passed","duration":"10ms","action_id":"4j3rdXxNzv","screenshot_filename":"4j3rdXxNzv.png","report_screenshot":"4j3rdXxNzv.png","resolved_screenshot":"screenshots/4j3rdXxNzv.png","screenshot":"screenshots/4j3rdXxNzv.png","action_id_screenshot":"screenshots/4j3rdXxNzv.png"}],"retry_info":{"retry_count":0,"max_retries":0,"was_retried":false},"display_name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"}],"passed":2,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["4kBvNvFi5i.png","7MOUNxtPJz.png","AoLct5ZYWj.png","E5An5BbVuK.png","HphRLWPfSD.png","KfOSdvcOkk.png","KzjZOcsLuC.png","OqYf9xF3oX.png","SaJtvXOGlT.png","To6rgFtm9R.png","UppP3ZuqY6.png","ee5KkVz90e.png","f5C7GOVKXJ.png","jE4eZaRFK6.png","jF4jRny1iE.png","mOoxO3pBlm.png","mmT4QEfEZD.png","muJaxWLMoU.png","oIAtyQB5wY.png","rxDTLvtHmR.png","vjBGuN5y9x.png","wp1dY1wJ58.png","yvWe991wY2.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>