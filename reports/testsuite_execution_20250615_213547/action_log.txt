Action Log - 2025-06-15 21:38:19
================================================================================

[[21:38:18]] [INFO] Generating execution report...
[[21:38:18]] [WARNING] 1 test failed.
[[21:38:18]] [SUCCESS] Screenshot refreshed
[[21:38:18]] [INFO] Refreshing screenshot...
[[21:38:16]] [SUCCESS] Screenshot refreshed successfully
[[21:38:16]] [SUCCESS] Screenshot refreshed successfully
[[21:38:15]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[21:38:15]] [SUCCESS] Screenshot refreshed
[[21:38:15]] [INFO] Refreshing screenshot...
[[21:38:15]] [SUCCESS] Screenshot refreshed
[[21:38:15]] [INFO] Refreshing screenshot...
[[21:38:13]] [SUCCESS] Screenshot refreshed successfully
[[21:38:13]] [SUCCESS] Screenshot refreshed successfully
[[21:38:13]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[21:38:12]] [SUCCESS] Screenshot refreshed
[[21:38:12]] [INFO] Refreshing screenshot...
[[21:37:59]] [SUCCESS] Screenshot refreshed successfully
[[21:37:59]] [SUCCESS] Screenshot refreshed successfully
[[21:37:58]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[21:37:58]] [SUCCESS] Screenshot refreshed
[[21:37:58]] [INFO] Refreshing screenshot...
[[21:37:55]] [SUCCESS] Screenshot refreshed successfully
[[21:37:55]] [SUCCESS] Screenshot refreshed successfully
[[21:37:55]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[21:37:54]] [SUCCESS] Screenshot refreshed
[[21:37:54]] [INFO] Refreshing screenshot...
[[21:37:51]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:37:51]] [SUCCESS] Screenshot refreshed successfully
[[21:37:51]] [SUCCESS] Screenshot refreshed successfully
[[21:37:50]] [SUCCESS] Screenshot refreshed
[[21:37:50]] [INFO] Refreshing screenshot...
[[21:37:49]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[21:37:49]] [SUCCESS] Screenshot refreshed successfully
[[21:37:49]] [SUCCESS] Screenshot refreshed successfully
[[21:37:49]] [SUCCESS] Screenshot refreshed
[[21:37:49]] [INFO] Refreshing screenshot...
[[21:37:46]] [SUCCESS] Screenshot refreshed successfully
[[21:37:46]] [SUCCESS] Screenshot refreshed successfully
[[21:37:46]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:37:45]] [SUCCESS] Screenshot refreshed
[[21:37:45]] [INFO] Refreshing screenshot...
[[21:37:42]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:37:42]] [SUCCESS] Screenshot refreshed successfully
[[21:37:42]] [SUCCESS] Screenshot refreshed successfully
[[21:37:42]] [SUCCESS] Screenshot refreshed
[[21:37:42]] [INFO] Refreshing screenshot...
[[21:37:39]] [SUCCESS] Screenshot refreshed successfully
[[21:37:39]] [SUCCESS] Screenshot refreshed successfully
[[21:37:39]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:37:39]] [SUCCESS] Screenshot refreshed
[[21:37:39]] [INFO] Refreshing screenshot...
[[21:37:34]] [SUCCESS] Screenshot refreshed successfully
[[21:37:34]] [SUCCESS] Screenshot refreshed successfully
[[21:37:34]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[21:37:34]] [INFO] Loaded 9 steps from test case: apple health
[[21:37:34]] [INFO] Loading steps for Multi Step action: apple health
[[21:37:34]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[21:37:33]] [SUCCESS] Screenshot refreshed
[[21:37:33]] [INFO] Refreshing screenshot...
[[21:37:30]] [SUCCESS] Screenshot refreshed successfully
[[21:37:30]] [SUCCESS] Screenshot refreshed successfully
[[21:37:30]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[21:37:30]] [SUCCESS] Screenshot refreshed
[[21:37:30]] [INFO] Refreshing screenshot...
[[21:37:27]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:37:26]] [SUCCESS] Screenshot refreshed successfully
[[21:37:26]] [SUCCESS] Screenshot refreshed successfully
[[21:37:26]] [SUCCESS] Screenshot refreshed
[[21:37:26]] [INFO] Refreshing screenshot...
[[21:37:25]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[21:37:24]] [SUCCESS] Screenshot refreshed successfully
[[21:37:24]] [SUCCESS] Screenshot refreshed successfully
[[21:37:24]] [SUCCESS] Screenshot refreshed
[[21:37:24]] [INFO] Refreshing screenshot...
[[21:37:21]] [SUCCESS] Screenshot refreshed successfully
[[21:37:21]] [SUCCESS] Screenshot refreshed successfully
[[21:37:21]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:37:21]] [SUCCESS] Screenshot refreshed
[[21:37:21]] [INFO] Refreshing screenshot...
[[21:37:18]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:37:18]] [SUCCESS] Screenshot refreshed successfully
[[21:37:18]] [SUCCESS] Screenshot refreshed successfully
[[21:37:17]] [SUCCESS] Screenshot refreshed
[[21:37:17]] [INFO] Refreshing screenshot...
[[21:37:15]] [SUCCESS] Screenshot refreshed successfully
[[21:37:15]] [SUCCESS] Screenshot refreshed successfully
[[21:37:15]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:37:14]] [SUCCESS] Screenshot refreshed
[[21:37:14]] [INFO] Refreshing screenshot...
[[21:37:02]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[21:37:02]] [INFO] Skipping remaining steps in failed test case (moving from action 15 to next test case at 16)
[[21:37:02]] [INFO] Moving to the next test case after failure (server will handle retry)
[[21:37:02]] [ERROR] Multi Step action step 5 failed: Element with xpath ' //XCUIElementTypeButton[@name="Done"]' not clickable within timeout of 10 seconds
[[21:36:51]] [SUCCESS] Screenshot refreshed successfully
[[21:36:51]] [SUCCESS] Screenshot refreshed successfully
[[21:36:51]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:36:50]] [SUCCESS] Screenshot refreshed
[[21:36:50]] [INFO] Refreshing screenshot...
[[21:36:49]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[21:36:49]] [SUCCESS] Screenshot refreshed successfully
[[21:36:49]] [SUCCESS] Screenshot refreshed successfully
[[21:36:48]] [SUCCESS] Screenshot refreshed
[[21:36:48]] [INFO] Refreshing screenshot...
[[21:36:45]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:36:45]] [SUCCESS] Screenshot refreshed successfully
[[21:36:45]] [SUCCESS] Screenshot refreshed successfully
[[21:36:45]] [SUCCESS] Screenshot refreshed
[[21:36:45]] [INFO] Refreshing screenshot...
[[21:36:43]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[21:36:43]] [SUCCESS] Screenshot refreshed successfully
[[21:36:43]] [SUCCESS] Screenshot refreshed successfully
[[21:36:43]] [SUCCESS] Screenshot refreshed
[[21:36:43]] [INFO] Refreshing screenshot...
[[21:36:39]] [SUCCESS] Screenshot refreshed successfully
[[21:36:39]] [SUCCESS] Screenshot refreshed successfully
[[21:36:38]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[21:36:38]] [INFO] Loaded 9 steps from test case: health2
[[21:36:38]] [INFO] Loading steps for Multi Step action: health2
[[21:36:38]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[21:36:38]] [SUCCESS] Screenshot refreshed
[[21:36:38]] [INFO] Refreshing screenshot...
[[21:36:36]] [SUCCESS] Screenshot refreshed successfully
[[21:36:36]] [SUCCESS] Screenshot refreshed successfully
[[21:36:36]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[21:36:35]] [SUCCESS] Screenshot refreshed
[[21:36:35]] [INFO] Refreshing screenshot...
[[21:36:32]] [SUCCESS] Screenshot refreshed successfully
[[21:36:32]] [SUCCESS] Screenshot refreshed successfully
[[21:36:32]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[21:36:31]] [SUCCESS] Screenshot refreshed
[[21:36:31]] [INFO] Refreshing screenshot...
[[21:36:28]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:36:28]] [SUCCESS] Screenshot refreshed successfully
[[21:36:28]] [SUCCESS] Screenshot refreshed successfully
[[21:36:28]] [SUCCESS] Screenshot refreshed
[[21:36:28]] [INFO] Refreshing screenshot...
[[21:36:25]] [SUCCESS] Screenshot refreshed successfully
[[21:36:25]] [SUCCESS] Screenshot refreshed successfully
[[21:36:25]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[21:36:25]] [SUCCESS] Screenshot refreshed
[[21:36:25]] [INFO] Refreshing screenshot...
[[21:36:13]] [SUCCESS] Screenshot refreshed successfully
[[21:36:13]] [SUCCESS] Screenshot refreshed successfully
[[21:36:12]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[21:36:12]] [SUCCESS] Screenshot refreshed
[[21:36:12]] [INFO] Refreshing screenshot...
[[21:36:10]] [SUCCESS] Screenshot refreshed successfully
[[21:36:10]] [SUCCESS] Screenshot refreshed successfully
[[21:36:09]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[21:36:09]] [SUCCESS] Screenshot refreshed
[[21:36:09]] [INFO] Refreshing screenshot...
[[21:36:06]] [SUCCESS] Screenshot refreshed successfully
[[21:36:06]] [SUCCESS] Screenshot refreshed successfully
[[21:36:06]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[21:36:05]] [SUCCESS] Screenshot refreshed
[[21:36:05]] [INFO] Refreshing screenshot...
[[21:36:03]] [SUCCESS] Screenshot refreshed successfully
[[21:36:03]] [SUCCESS] Screenshot refreshed successfully
[[21:36:02]] [INFO] Executing action 7/25: Wait for 1 ms
[[21:36:02]] [SUCCESS] Screenshot refreshed
[[21:36:02]] [INFO] Refreshing screenshot...
[[21:36:01]] [SUCCESS] Screenshot refreshed successfully
[[21:36:01]] [SUCCESS] Screenshot refreshed successfully
[[21:36:00]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[21:36:00]] [SUCCESS] Screenshot refreshed
[[21:36:00]] [INFO] Refreshing screenshot...
[[21:35:57]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[21:35:57]] [SUCCESS] Screenshot refreshed successfully
[[21:35:57]] [SUCCESS] Screenshot refreshed successfully
[[21:35:56]] [SUCCESS] Screenshot refreshed
[[21:35:56]] [INFO] Refreshing screenshot...
[[21:35:55]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[21:35:55]] [SUCCESS] Screenshot refreshed successfully
[[21:35:55]] [SUCCESS] Screenshot refreshed successfully
[[21:35:54]] [SUCCESS] Screenshot refreshed
[[21:35:54]] [INFO] Refreshing screenshot...
[[21:35:52]] [SUCCESS] Screenshot refreshed successfully
[[21:35:52]] [SUCCESS] Screenshot refreshed successfully
[[21:35:51]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[21:35:51]] [SUCCESS] Screenshot refreshed
[[21:35:51]] [INFO] Refreshing screenshot...
[[21:35:49]] [SUCCESS] Screenshot refreshed successfully
[[21:35:49]] [SUCCESS] Screenshot refreshed successfully
[[21:35:49]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[21:35:49]] [SUCCESS] Screenshot refreshed
[[21:35:49]] [INFO] Refreshing screenshot...
[[21:35:47]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[21:35:47]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[21:35:47]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[21:35:47]] [INFO] Clearing screenshots from database before execution...
[[21:35:47]] [SUCCESS] All screenshots deleted successfully
[[21:35:47]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[21:35:47]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_213547/screenshots
[[21:35:47]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_213547
[[21:35:47]] [SUCCESS] Report directory initialized successfully
[[21:35:47]] [INFO] Initializing report directory and screenshots folder...
[[21:35:44]] [SUCCESS] All screenshots deleted successfully
[[21:35:44]] [INFO] All actions cleared
[[21:35:44]] [INFO] Cleaning up screenshots...
[[21:35:37]] [SUCCESS] Screenshot refreshed successfully
[[21:35:37]] [SUCCESS] Screenshot refreshed
[[21:35:37]] [INFO] Refreshing screenshot...
[[21:35:36]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[21:35:36]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[21:35:34]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[21:35:32]] [SUCCESS] Found 1 device(s)
[[21:35:31]] [INFO] Refreshing device list...
