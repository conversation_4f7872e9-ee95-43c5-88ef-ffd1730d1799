{"name": "UI Execution 15/06/2025, 20:50:49", "testCases": [{"name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1341ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "screenshot": "screenshots/hWlJ7F1ghB.png", "clean_action_id": "hWlJ7F1ghB", "prefixed_action_id": "al_hWlJ7F1ghB", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Add Log: Launched App Successfully (with screenshot)", "status": "passed", "duration": "5ms", "action_id": "ZWM88Q6FwX", "screenshot_filename": "ZWM88Q6FwX.png", "report_screenshot": "ZWM88Q6FwX.png", "resolved_screenshot": "screenshots/ZWM88Q6FwX.png", "screenshot": "screenshots/ZWM88Q6FwX.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1222ms", "action_id": "OjHBbF9rvP", "screenshot_filename": "OjHBbF9rvP.png", "report_screenshot": "OjHBbF9rvP.png", "resolved_screenshot": "screenshots/OjHBbF9rvP.png", "screenshot": "screenshots/OjHBbF9rvP.png"}, {"name": "Add Log: Edit link is clicked (with screenshot)", "status": "passed", "duration": "6ms", "action_id": "HJav0tkEo7", "screenshot_filename": "HJav0tkEo7.png", "report_screenshot": "HJav0tkEo7.png", "resolved_screenshot": "screenshots/HJav0tkEo7.png", "screenshot": "screenshots/HJav0tkEo7.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1598ms", "action_id": "bwdIpnaKv1", "screenshot_filename": "bwdIpnaKv1.png", "report_screenshot": "bwdIpnaKv1.png", "resolved_screenshot": "screenshots/bwdIpnaKv1.png", "screenshot": "screenshots/bwdIpnaKv1.png"}, {"name": "Add Log: Done link is clicked (with screenshot)", "status": "passed", "duration": "5ms", "action_id": "BPZngCxri3", "screenshot_filename": "BPZngCxri3.png", "report_screenshot": "BPZngCxri3.png", "resolved_screenshot": "screenshots/BPZngCxri3.png", "screenshot": "screenshots/BPZngCxri3.png"}, {"name": "Wait for 1 ms", "status": "passed", "duration": "1004ms", "action_id": "KzjZOcsLuC", "screenshot_filename": "KzjZOcsLuC.png", "report_screenshot": "KzjZOcsLuC.png", "resolved_screenshot": "screenshots/KzjZOcsLuC.png", "screenshot": "screenshots/28TkXb3ufe.png", "clean_action_id": "28TkXb3ufe", "prefixed_action_id": "al_28TkXb3ufe", "action_id_screenshot": "screenshots/KzjZOcsLuC.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1034ms", "action_id": "OqYf9xF3oX", "screenshot_filename": "OqYf9xF3oX.png", "report_screenshot": "OqYf9xF3oX.png", "resolved_screenshot": "screenshots/OqYf9xF3oX.png", "screenshot": "screenshots/UNyZ37615R.png", "clean_action_id": "UNyZ37615R", "prefixed_action_id": "al_UNyZ37615R", "action_id_screenshot": "screenshots/OqYf9xF3oX.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "passed", "duration": "6ms", "action_id": "8JLDQEIoxp", "screenshot_filename": "8JLDQEIoxp.png", "report_screenshot": "8JLDQEIoxp.png", "resolved_screenshot": "screenshots/8JLDQEIoxp.png", "screenshot": "screenshots/8JLDQEIoxp.png"}], "retry_info": {"retry_count": 0, "max_retries": 0, "was_retried": false}, "display_name": "health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"}, {"name": "apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1160ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "screenshot": "screenshots/GU78ZxQ5EH.png", "clean_action_id": "GU78ZxQ5EH", "prefixed_action_id": "al_GU78ZxQ5EH", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "859ms", "action_id": "x0wgzo8SU8", "screenshot_filename": "x0wgzo8SU8.png", "report_screenshot": "x0wgzo8SU8.png", "resolved_screenshot": "screenshots/x0wgzo8SU8.png", "screenshot": "screenshots/x0wgzo8SU8.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1492ms", "action_id": "ufx2orJb14", "screenshot_filename": "ufx2orJb14.png", "report_screenshot": "ufx2orJb14.png", "resolved_screenshot": "screenshots/ufx2orJb14.png", "screenshot": "screenshots/ufx2orJb14.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1114ms", "action_id": "E5An5BbVuK", "screenshot_filename": "E5An5BbVuK.png", "report_screenshot": "E5An5BbVuK.png", "resolved_screenshot": "screenshots/E5An5BbVuK.png", "screenshot": "screenshots/2Sp8OOFNXN.png", "clean_action_id": "2Sp8OOFNXN", "prefixed_action_id": "al_2Sp8OOFNXN", "action_id_screenshot": "screenshots/E5An5BbVuK.png"}, {"name": "Add Log: Closed App Successfully (with screenshot)", "status": "passed", "duration": "6ms", "action_id": "zK17pI1x08", "screenshot_filename": "zK17pI1x08.png", "report_screenshot": "zK17pI1x08.png", "resolved_screenshot": "screenshots/zK17pI1x08.png", "screenshot": "screenshots/zK17pI1x08.png"}, {"name": "Execute Test Case: health2 (9 steps)", "status": "passed", "duration": "0ms", "action_id": "KfOSdvcOkk", "screenshot_filename": "KfOSdvcOkk.png", "report_screenshot": "KfOSdvcOkk.png", "resolved_screenshot": "screenshots/KfOSdvcOkk.png", "screenshot": "screenshots/C3XqlQHhAa.png", "clean_action_id": "C3XqlQHhAa", "prefixed_action_id": "al_C3XqlQHhAa", "action_id_screenshot": "screenshots/KfOSdvcOkk.png"}, {"name": "Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)", "status": "passed", "duration": "0ms", "action_id": "Xcyu4RqvuV", "screenshot_filename": "Xcyu4RqvuV.png", "report_screenshot": "Xcyu4RqvuV.png", "resolved_screenshot": "screenshots/Xcyu4RqvuV.png", "screenshot": "screenshots/Xcyu4RqvuV.png"}], "retry_info": {"retry_count": 0, "max_retries": 0, "was_retried": false}, "display_name": "apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions"}, {"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions", "status": "failed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1162ms", "action_id": "4kBvNvFi5i", "screenshot_filename": "4kBvNvFi5i.png", "report_screenshot": "4kBvNvFi5i.png", "resolved_screenshot": "screenshots/4kBvNvFi5i.png", "screenshot": "screenshots/GziEH5nULS.png", "clean_action_id": "GziEH5nULS", "prefixed_action_id": "al_GziEH5nULS", "action_id_screenshot": "screenshots/4kBvNvFi5i.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "3885ms", "action_id": "XcqesxXRjo", "screenshot_filename": "XcqesxXRjo.png", "report_screenshot": "XcqesxXRjo.png", "resolved_screenshot": "screenshots/XcqesxXRjo.png", "screenshot": "screenshots/XcqesxXRjo.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1532ms", "action_id": "6I3YDyvrBC", "screenshot_filename": "6I3YDyvrBC.png", "report_screenshot": "6I3YDyvrBC.png", "resolved_screenshot": "screenshots/6I3YDyvrBC.png", "screenshot": "screenshots/6I3YDyvrBC.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1254ms", "action_id": "udcn4iYb25", "screenshot_filename": "udcn4iYb25.png", "report_screenshot": "udcn4iYb25.png", "resolved_screenshot": "screenshots/udcn4iYb25.png", "screenshot": "screenshots/udcn4iYb25.png"}, {"name": "Add Log: Clicked on Edit link successfully (with screenshot)", "status": "passed", "duration": "7ms", "action_id": "HoOYrXt2sE", "screenshot_filename": "HoOYrXt2sE.png", "report_screenshot": "HoOYrXt2sE.png", "resolved_screenshot": "screenshots/HoOYrXt2sE.png", "screenshot": "screenshots/HoOYrXt2sE.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "failed", "duration": "0ms", "action_id": "LEBNj59tOj", "screenshot_filename": "LEBNj59tOj.png", "report_screenshot": "LEBNj59tOj.png", "resolved_screenshot": "screenshots/LEBNj59tOj.png", "screenshot": "screenshots/LEBNj59tOj.png"}, {"name": "Terminate app: com.apple.Health", "status": "unknown", "duration": "0ms", "action_id": "KzjZOcsLuC", "screenshot_filename": "KzjZOcsLuC.png", "report_screenshot": "KzjZOcsLuC.png", "resolved_screenshot": "screenshots/KzjZOcsLuC.png", "screenshot": "screenshots/W7cMO4gA6A.png", "clean_action_id": "W7cMO4gA6A", "prefixed_action_id": "al_W7cMO4gA6A", "action_id_screenshot": "screenshots/KzjZOcsLuC.png"}, {"name": "Execute Test Case: apple health (8 steps)", "status": "unknown", "duration": "0ms", "action_id": "OqYf9xF3oX", "screenshot_filename": "OqYf9xF3oX.png", "report_screenshot": "OqYf9xF3oX.png", "resolved_screenshot": "screenshots/OqYf9xF3oX.png", "screenshot": "screenshots/Qe6o3P4jKE.png", "clean_action_id": "Qe6o3P4jKE", "prefixed_action_id": "al_Qe6o3P4jKE", "action_id_screenshot": "screenshots/OqYf9xF3oX.png"}, {"name": "Add Log: App is closed (with screenshot)", "status": "unknown", "duration": "0ms", "action_id": "FTucUQBRIz", "screenshot_filename": "FTucUQBRIz.png", "report_screenshot": "FTucUQBRIz.png", "resolved_screenshot": "screenshots/FTucUQBRIz.png", "screenshot": "screenshots/FTucUQBRIz.png"}], "retry_info": {"retry_count": 0, "max_retries": 0, "was_retried": false}, "display_name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"}], "passed": 2, "failed": 1, "skipped": 0, "status": "failed"}