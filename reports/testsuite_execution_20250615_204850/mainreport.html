<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/15/2025, 8:50:50 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">2</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 15/06/2025, 20:50:49
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 health2
                            
                            
                                    testing labels
                                
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions 
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1341ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="ZWM88Q6FwX.png" data-action-id="ZWM88Q6FwX" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Launched App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: ZWM88Q6FwX">ZWM88Q6FwX</span>
                            </div>
                            <span class="test-step-duration">5ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="OjHBbF9rvP.png" data-action-id="OjHBbF9rvP" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: OjHBbF9rvP">OjHBbF9rvP</span>
                            </div>
                            <span class="test-step-duration">1222ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="HJav0tkEo7.png" data-action-id="HJav0tkEo7" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Edit link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: HJav0tkEo7">HJav0tkEo7</span>
                            </div>
                            <span class="test-step-duration">6ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="bwdIpnaKv1.png" data-action-id="bwdIpnaKv1" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: bwdIpnaKv1">bwdIpnaKv1</span>
                            </div>
                            <span class="test-step-duration">1598ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="BPZngCxri3.png" data-action-id="BPZngCxri3" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Done link is clicked (with screenshot) <span class="action-id-badge" title="Action ID: BPZngCxri3">BPZngCxri3</span>
                            </div>
                            <span class="test-step-duration">5ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="KzjZOcsLuC.png" data-action-id="KzjZOcsLuC" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 1 ms <span class="action-id-badge" title="Action ID: KzjZOcsLuC">KzjZOcsLuC</span>
                            </div>
                            <span class="test-step-duration">1004ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="OqYf9xF3oX.png" data-action-id="OqYf9xF3oX" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: OqYf9xF3oX">OqYf9xF3oX</span>
                            </div>
                            <span class="test-step-duration">1034ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="8JLDQEIoxp.png" data-action-id="8JLDQEIoxp" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: 8JLDQEIoxp">8JLDQEIoxp</span>
                            </div>
                            <span class="test-step-duration">6ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 apple health (Copy)
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            7 actions 
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1160ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="x0wgzo8SU8.png" data-action-id="x0wgzo8SU8" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: x0wgzo8SU8">x0wgzo8SU8</span>
                            </div>
                            <span class="test-step-duration">859ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="ufx2orJb14.png" data-action-id="ufx2orJb14" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: ufx2orJb14">ufx2orJb14</span>
                            </div>
                            <span class="test-step-duration">1492ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="E5An5BbVuK.png" data-action-id="E5An5BbVuK" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: E5An5BbVuK">E5An5BbVuK</span>
                            </div>
                            <span class="test-step-duration">1114ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="zK17pI1x08.png" data-action-id="zK17pI1x08" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Closed App Successfully (with screenshot) <span class="action-id-badge" title="Action ID: zK17pI1x08">zK17pI1x08</span>
                            </div>
                            <span class="test-step-duration">6ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="KfOSdvcOkk.png" data-action-id="KfOSdvcOkk" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: health2 (9 steps) <span class="action-id-badge" title="Action ID: KfOSdvcOkk">KfOSdvcOkk</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="Xcyu4RqvuV.png" data-action-id="Xcyu4RqvuV" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery) <span class="action-id-badge" title="Action ID: Xcyu4RqvuV">Xcyu4RqvuV</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="9 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #3 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Remove
                            
                            9 actions 
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="4kBvNvFi5i.png" data-action-id="4kBvNvFi5i" onclick="showStepDetails('step-2-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: 4kBvNvFi5i">4kBvNvFi5i</span>
                            </div>
                            <span class="test-step-duration">1162ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XcqesxXRjo.png" data-action-id="XcqesxXRjo" onclick="showStepDetails('step-2-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XcqesxXRjo">XcqesxXRjo</span>
                            </div>
                            <span class="test-step-duration">3885ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="6I3YDyvrBC.png" data-action-id="6I3YDyvrBC" onclick="showStepDetails('step-2-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: 6I3YDyvrBC">6I3YDyvrBC</span>
                            </div>
                            <span class="test-step-duration">1532ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="udcn4iYb25.png" data-action-id="udcn4iYb25" onclick="showStepDetails('step-2-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: udcn4iYb25">udcn4iYb25</span>
                            </div>
                            <span class="test-step-duration">1254ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="HoOYrXt2sE.png" data-action-id="HoOYrXt2sE" onclick="showStepDetails('step-2-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Add Log: Clicked on Edit link successfully (with screenshot) <span class="action-id-badge" title="Action ID: HoOYrXt2sE">HoOYrXt2sE</span>
                            </div>
                            <span class="test-step-duration">7ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="failed"
                            data-screenshot="LEBNj59tOj.png" data-action-id="LEBNj59tOj" onclick="showStepDetails('step-2-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: LEBNj59tOj">LEBNj59tOj</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="KzjZOcsLuC.png" data-action-id="KzjZOcsLuC" onclick="showStepDetails('step-2-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: KzjZOcsLuC">KzjZOcsLuC</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="OqYf9xF3oX.png" data-action-id="OqYf9xF3oX" onclick="showStepDetails('step-2-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: apple health (8 steps) <span class="action-id-badge" title="Action ID: OqYf9xF3oX">OqYf9xF3oX</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="unknown"
                            data-screenshot="FTucUQBRIz.png" data-action-id="FTucUQBRIz" onclick="showStepDetails('step-2-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Add Log: App is closed (with screenshot) <span class="action-id-badge" title="Action ID: FTucUQBRIz">FTucUQBRIz</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 15/06/2025, 20:50:49","testCases":[{"name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1341ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","screenshot":"screenshots/hWlJ7F1ghB.png","clean_action_id":"hWlJ7F1ghB","prefixed_action_id":"al_hWlJ7F1ghB","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Add Log: Launched App Successfully (with screenshot)","status":"passed","duration":"5ms","action_id":"ZWM88Q6FwX","screenshot_filename":"ZWM88Q6FwX.png","report_screenshot":"ZWM88Q6FwX.png","resolved_screenshot":"screenshots/ZWM88Q6FwX.png","screenshot":"screenshots/ZWM88Q6FwX.png","action_id_screenshot":"screenshots/ZWM88Q6FwX.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1222ms","action_id":"OjHBbF9rvP","screenshot_filename":"OjHBbF9rvP.png","report_screenshot":"OjHBbF9rvP.png","resolved_screenshot":"screenshots/OjHBbF9rvP.png","screenshot":"screenshots/OjHBbF9rvP.png","action_id_screenshot":"screenshots/OjHBbF9rvP.png"},{"name":"Add Log: Edit link is clicked (with screenshot)","status":"passed","duration":"6ms","action_id":"HJav0tkEo7","screenshot_filename":"HJav0tkEo7.png","report_screenshot":"HJav0tkEo7.png","resolved_screenshot":"screenshots/HJav0tkEo7.png","screenshot":"screenshots/HJav0tkEo7.png","action_id_screenshot":"screenshots/HJav0tkEo7.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1598ms","action_id":"bwdIpnaKv1","screenshot_filename":"bwdIpnaKv1.png","report_screenshot":"bwdIpnaKv1.png","resolved_screenshot":"screenshots/bwdIpnaKv1.png","screenshot":"screenshots/bwdIpnaKv1.png","action_id_screenshot":"screenshots/bwdIpnaKv1.png"},{"name":"Add Log: Done link is clicked (with screenshot)","status":"passed","duration":"5ms","action_id":"BPZngCxri3","screenshot_filename":"BPZngCxri3.png","report_screenshot":"BPZngCxri3.png","resolved_screenshot":"screenshots/BPZngCxri3.png","screenshot":"screenshots/BPZngCxri3.png","action_id_screenshot":"screenshots/BPZngCxri3.png"},{"name":"Wait for 1 ms","status":"passed","duration":"1004ms","action_id":"KzjZOcsLuC","screenshot_filename":"KzjZOcsLuC.png","report_screenshot":"KzjZOcsLuC.png","resolved_screenshot":"screenshots/KzjZOcsLuC.png","screenshot":"screenshots/28TkXb3ufe.png","clean_action_id":"28TkXb3ufe","prefixed_action_id":"al_28TkXb3ufe","action_id_screenshot":"screenshots/KzjZOcsLuC.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1034ms","action_id":"OqYf9xF3oX","screenshot_filename":"OqYf9xF3oX.png","report_screenshot":"OqYf9xF3oX.png","resolved_screenshot":"screenshots/OqYf9xF3oX.png","screenshot":"screenshots/UNyZ37615R.png","clean_action_id":"UNyZ37615R","prefixed_action_id":"al_UNyZ37615R","action_id_screenshot":"screenshots/OqYf9xF3oX.png"},{"name":"Add Log: App is closed (with screenshot)","status":"passed","duration":"6ms","action_id":"8JLDQEIoxp","screenshot_filename":"8JLDQEIoxp.png","report_screenshot":"8JLDQEIoxp.png","resolved_screenshot":"screenshots/8JLDQEIoxp.png","screenshot":"screenshots/8JLDQEIoxp.png","action_id_screenshot":"screenshots/8JLDQEIoxp.png"}],"retry_info":{"retry_count":0,"max_retries":0,"was_retried":false},"display_name":"health2\n                            \n                            \n                                    testing labels\n                                \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"},{"name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1160ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","screenshot":"screenshots/GU78ZxQ5EH.png","clean_action_id":"GU78ZxQ5EH","prefixed_action_id":"al_GU78ZxQ5EH","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"859ms","action_id":"x0wgzo8SU8","screenshot_filename":"x0wgzo8SU8.png","report_screenshot":"x0wgzo8SU8.png","resolved_screenshot":"screenshots/x0wgzo8SU8.png","screenshot":"screenshots/x0wgzo8SU8.png","action_id_screenshot":"screenshots/x0wgzo8SU8.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1492ms","action_id":"ufx2orJb14","screenshot_filename":"ufx2orJb14.png","report_screenshot":"ufx2orJb14.png","resolved_screenshot":"screenshots/ufx2orJb14.png","screenshot":"screenshots/ufx2orJb14.png","action_id_screenshot":"screenshots/ufx2orJb14.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1114ms","action_id":"E5An5BbVuK","screenshot_filename":"E5An5BbVuK.png","report_screenshot":"E5An5BbVuK.png","resolved_screenshot":"screenshots/E5An5BbVuK.png","screenshot":"screenshots/2Sp8OOFNXN.png","clean_action_id":"2Sp8OOFNXN","prefixed_action_id":"al_2Sp8OOFNXN","action_id_screenshot":"screenshots/E5An5BbVuK.png"},{"name":"Add Log: Closed App Successfully (with screenshot)","status":"passed","duration":"6ms","action_id":"zK17pI1x08","screenshot_filename":"zK17pI1x08.png","report_screenshot":"zK17pI1x08.png","resolved_screenshot":"screenshots/zK17pI1x08.png","screenshot":"screenshots/zK17pI1x08.png","action_id_screenshot":"screenshots/zK17pI1x08.png"},{"name":"Execute Test Case: health2 (9 steps)","status":"passed","duration":"0ms","action_id":"KfOSdvcOkk","screenshot_filename":"KfOSdvcOkk.png","report_screenshot":"KfOSdvcOkk.png","resolved_screenshot":"screenshots/KfOSdvcOkk.png","screenshot":"screenshots/C3XqlQHhAa.png","clean_action_id":"C3XqlQHhAa","prefixed_action_id":"al_C3XqlQHhAa","action_id_screenshot":"screenshots/KfOSdvcOkk.png"},{"name":"Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Edit\"] (Recovery)","status":"passed","duration":"0ms","action_id":"Xcyu4RqvuV","screenshot_filename":"Xcyu4RqvuV.png","report_screenshot":"Xcyu4RqvuV.png","resolved_screenshot":"screenshots/Xcyu4RqvuV.png","screenshot":"screenshots/Xcyu4RqvuV.png","action_id_screenshot":"screenshots/Xcyu4RqvuV.png"}],"retry_info":{"retry_count":0,"max_retries":0,"was_retried":false},"display_name":"apple health (Copy)\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            7 actions"},{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions","status":"failed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1162ms","action_id":"4kBvNvFi5i","screenshot_filename":"4kBvNvFi5i.png","report_screenshot":"4kBvNvFi5i.png","resolved_screenshot":"screenshots/4kBvNvFi5i.png","screenshot":"screenshots/GziEH5nULS.png","clean_action_id":"GziEH5nULS","prefixed_action_id":"al_GziEH5nULS","action_id_screenshot":"screenshots/4kBvNvFi5i.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"3885ms","action_id":"XcqesxXRjo","screenshot_filename":"XcqesxXRjo.png","report_screenshot":"XcqesxXRjo.png","resolved_screenshot":"screenshots/XcqesxXRjo.png","screenshot":"screenshots/XcqesxXRjo.png","action_id_screenshot":"screenshots/XcqesxXRjo.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1532ms","action_id":"6I3YDyvrBC","screenshot_filename":"6I3YDyvrBC.png","report_screenshot":"6I3YDyvrBC.png","resolved_screenshot":"screenshots/6I3YDyvrBC.png","screenshot":"screenshots/6I3YDyvrBC.png","action_id_screenshot":"screenshots/6I3YDyvrBC.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1254ms","action_id":"udcn4iYb25","screenshot_filename":"udcn4iYb25.png","report_screenshot":"udcn4iYb25.png","resolved_screenshot":"screenshots/udcn4iYb25.png","screenshot":"screenshots/udcn4iYb25.png","action_id_screenshot":"screenshots/udcn4iYb25.png"},{"name":"Add Log: Clicked on Edit link successfully (with screenshot)","status":"passed","duration":"7ms","action_id":"HoOYrXt2sE","screenshot_filename":"HoOYrXt2sE.png","report_screenshot":"HoOYrXt2sE.png","resolved_screenshot":"screenshots/HoOYrXt2sE.png","screenshot":"screenshots/HoOYrXt2sE.png","action_id_screenshot":"screenshots/HoOYrXt2sE.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"failed","duration":"0ms","action_id":"LEBNj59tOj","screenshot_filename":"LEBNj59tOj.png","report_screenshot":"LEBNj59tOj.png","resolved_screenshot":"screenshots/LEBNj59tOj.png","screenshot":"screenshots/LEBNj59tOj.png","action_id_screenshot":"screenshots/LEBNj59tOj.png"},{"name":"Terminate app: com.apple.Health","status":"unknown","duration":"0ms","action_id":"KzjZOcsLuC","screenshot_filename":"KzjZOcsLuC.png","report_screenshot":"KzjZOcsLuC.png","resolved_screenshot":"screenshots/KzjZOcsLuC.png","screenshot":"screenshots/W7cMO4gA6A.png","clean_action_id":"W7cMO4gA6A","prefixed_action_id":"al_W7cMO4gA6A","action_id_screenshot":"screenshots/KzjZOcsLuC.png"},{"name":"Execute Test Case: apple health (8 steps)","status":"unknown","duration":"0ms","action_id":"OqYf9xF3oX","screenshot_filename":"OqYf9xF3oX.png","report_screenshot":"OqYf9xF3oX.png","resolved_screenshot":"screenshots/OqYf9xF3oX.png","screenshot":"screenshots/Qe6o3P4jKE.png","clean_action_id":"Qe6o3P4jKE","prefixed_action_id":"al_Qe6o3P4jKE","action_id_screenshot":"screenshots/OqYf9xF3oX.png"},{"name":"Add Log: App is closed (with screenshot)","status":"unknown","duration":"0ms","action_id":"FTucUQBRIz","screenshot_filename":"FTucUQBRIz.png","report_screenshot":"FTucUQBRIz.png","resolved_screenshot":"screenshots/FTucUQBRIz.png","screenshot":"screenshots/FTucUQBRIz.png","action_id_screenshot":"screenshots/FTucUQBRIz.png"}],"retry_info":{"retry_count":0,"max_retries":0,"was_retried":false},"display_name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Remove\n                            \n                            9 actions"}],"passed":2,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["4kBvNvFi5i.png","7MOUNxtPJz.png","AoLct5ZYWj.png","E5An5BbVuK.png","HphRLWPfSD.png","KfOSdvcOkk.png","KzjZOcsLuC.png","OqYf9xF3oX.png","SaJtvXOGlT.png","To6rgFtm9R.png","UppP3ZuqY6.png","ee5KkVz90e.png","f5C7GOVKXJ.png","jE4eZaRFK6.png","mOoxO3pBlm.png","mmT4QEfEZD.png","oIAtyQB5wY.png","rxDTLvtHmR.png","wp1dY1wJ58.png","yvWe991wY2.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>