Action Log - 2025-06-15 20:50:50
================================================================================

[[20:50:49]] [INFO] Generating execution report...
[[20:50:49]] [WARNING] 1 test failed.
[[20:50:49]] [INFO] Skipping remaining steps in failed test case (moving from action 22 to next test case at 25)
[[20:50:49]] [ERROR] Action 22 failed: Element with xpath ' //XCUIElementTypeButton[@name="Done"]' not clickable within timeout of 10 seconds
[[20:50:38]] [SUCCESS] Screenshot refreshed successfully
[[20:50:38]] [SUCCESS] Screenshot refreshed successfully
[[20:50:38]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:50:37]] [SUCCESS] Screenshot refreshed
[[20:50:37]] [INFO] Refreshing screenshot...
[[20:50:36]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[20:50:36]] [SUCCESS] Screenshot refreshed successfully
[[20:50:36]] [SUCCESS] Screenshot refreshed successfully
[[20:50:35]] [SUCCESS] Screenshot refreshed
[[20:50:35]] [INFO] Refreshing screenshot...
[[20:50:32]] [SUCCESS] Screenshot refreshed successfully
[[20:50:32]] [SUCCESS] Screenshot refreshed successfully
[[20:50:32]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:50:32]] [SUCCESS] Screenshot refreshed
[[20:50:32]] [INFO] Refreshing screenshot...
[[20:50:29]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:50:29]] [SUCCESS] Screenshot refreshed successfully
[[20:50:29]] [SUCCESS] Screenshot refreshed successfully
[[20:50:28]] [SUCCESS] Screenshot refreshed
[[20:50:28]] [INFO] Refreshing screenshot...
[[20:50:23]] [SUCCESS] Screenshot refreshed successfully
[[20:50:23]] [SUCCESS] Screenshot refreshed successfully
[[20:50:23]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:50:22]] [SUCCESS] Screenshot refreshed
[[20:50:22]] [INFO] Refreshing screenshot...
[[20:50:10]] [SUCCESS] Screenshot refreshed successfully
[[20:50:10]] [SUCCESS] Screenshot refreshed successfully
[[20:50:10]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[20:50:09]] [SUCCESS] Screenshot refreshed
[[20:50:09]] [INFO] Refreshing screenshot...
[[20:50:09]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[20:50:09]] [INFO] Executing action 16/25: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[20:50:09]] [SUCCESS] Screenshot refreshed
[[20:50:09]] [INFO] Refreshing screenshot...
[[20:50:09]] [SUCCESS] Screenshot refreshed
[[20:50:09]] [INFO] Refreshing screenshot...
[[20:50:07]] [SUCCESS] Screenshot refreshed successfully
[[20:50:07]] [SUCCESS] Screenshot refreshed successfully
[[20:50:07]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[20:50:06]] [SUCCESS] Screenshot refreshed
[[20:50:06]] [INFO] Refreshing screenshot...
[[20:50:03]] [SUCCESS] Screenshot refreshed successfully
[[20:50:03]] [SUCCESS] Screenshot refreshed successfully
[[20:50:03]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[20:50:02]] [SUCCESS] Screenshot refreshed
[[20:50:02]] [INFO] Refreshing screenshot...
[[20:50:00]] [SUCCESS] Screenshot refreshed successfully
[[20:50:00]] [SUCCESS] Screenshot refreshed successfully
[[20:50:00]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[20:49:59]] [SUCCESS] Screenshot refreshed
[[20:49:59]] [INFO] Refreshing screenshot...
[[20:49:58]] [SUCCESS] Screenshot refreshed successfully
[[20:49:58]] [SUCCESS] Screenshot refreshed successfully
[[20:49:58]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[20:49:57]] [SUCCESS] Screenshot refreshed
[[20:49:57]] [INFO] Refreshing screenshot...
[[20:49:54]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:49:54]] [SUCCESS] Screenshot refreshed successfully
[[20:49:54]] [SUCCESS] Screenshot refreshed successfully
[[20:49:54]] [SUCCESS] Screenshot refreshed
[[20:49:54]] [INFO] Refreshing screenshot...
[[20:49:52]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[20:49:52]] [SUCCESS] Screenshot refreshed successfully
[[20:49:52]] [SUCCESS] Screenshot refreshed successfully
[[20:49:52]] [SUCCESS] Screenshot refreshed
[[20:49:52]] [INFO] Refreshing screenshot...
[[20:49:49]] [SUCCESS] Screenshot refreshed successfully
[[20:49:49]] [SUCCESS] Screenshot refreshed successfully
[[20:49:49]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:49:49]] [SUCCESS] Screenshot refreshed
[[20:49:49]] [INFO] Refreshing screenshot...
[[20:49:47]] [SUCCESS] Screenshot refreshed successfully
[[20:49:47]] [SUCCESS] Screenshot refreshed successfully
[[20:49:47]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[20:49:47]] [SUCCESS] Screenshot refreshed
[[20:49:47]] [INFO] Refreshing screenshot...
[[20:49:42]] [SUCCESS] Screenshot refreshed successfully
[[20:49:42]] [SUCCESS] Screenshot refreshed successfully
[[20:49:42]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[20:49:42]] [INFO] Loaded 9 steps from test case: health2
[[20:49:42]] [INFO] Loading steps for Multi Step action: health2
[[20:49:42]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[20:49:41]] [SUCCESS] Screenshot refreshed
[[20:49:41]] [INFO] Refreshing screenshot...
[[20:49:40]] [SUCCESS] Screenshot refreshed successfully
[[20:49:40]] [SUCCESS] Screenshot refreshed successfully
[[20:49:39]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[20:49:39]] [SUCCESS] Screenshot refreshed
[[20:49:39]] [INFO] Refreshing screenshot...
[[20:49:35]] [SUCCESS] Screenshot refreshed successfully
[[20:49:35]] [SUCCESS] Screenshot refreshed successfully
[[20:49:35]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[20:49:35]] [SUCCESS] Screenshot refreshed
[[20:49:35]] [INFO] Refreshing screenshot...
[[20:49:32]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:49:32]] [SUCCESS] Screenshot refreshed successfully
[[20:49:32]] [SUCCESS] Screenshot refreshed successfully
[[20:49:31]] [SUCCESS] Screenshot refreshed
[[20:49:31]] [INFO] Refreshing screenshot...
[[20:49:29]] [SUCCESS] Screenshot refreshed successfully
[[20:49:29]] [SUCCESS] Screenshot refreshed successfully
[[20:49:29]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[20:49:29]] [SUCCESS] Screenshot refreshed
[[20:49:29]] [INFO] Refreshing screenshot...
[[20:49:16]] [SUCCESS] Screenshot refreshed successfully
[[20:49:16]] [SUCCESS] Screenshot refreshed successfully
[[20:49:16]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[20:49:15]] [SUCCESS] Screenshot refreshed
[[20:49:15]] [INFO] Refreshing screenshot...
[[20:49:14]] [SUCCESS] Screenshot refreshed successfully
[[20:49:14]] [SUCCESS] Screenshot refreshed successfully
[[20:49:13]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[20:49:13]] [SUCCESS] Screenshot refreshed
[[20:49:13]] [INFO] Refreshing screenshot...
[[20:49:10]] [SUCCESS] Screenshot refreshed successfully
[[20:49:10]] [SUCCESS] Screenshot refreshed successfully
[[20:49:10]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[20:49:09]] [SUCCESS] Screenshot refreshed
[[20:49:09]] [INFO] Refreshing screenshot...
[[20:49:06]] [SUCCESS] Screenshot refreshed successfully
[[20:49:06]] [SUCCESS] Screenshot refreshed successfully
[[20:49:06]] [INFO] Executing action 7/25: Wait for 1 ms
[[20:49:06]] [SUCCESS] Screenshot refreshed
[[20:49:06]] [INFO] Refreshing screenshot...
[[20:49:04]] [SUCCESS] Screenshot refreshed successfully
[[20:49:04]] [SUCCESS] Screenshot refreshed successfully
[[20:49:04]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[20:49:04]] [SUCCESS] Screenshot refreshed
[[20:49:04]] [INFO] Refreshing screenshot...
[[20:49:01]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[20:49:01]] [SUCCESS] Screenshot refreshed successfully
[[20:49:01]] [SUCCESS] Screenshot refreshed successfully
[[20:49:00]] [SUCCESS] Screenshot refreshed
[[20:49:00]] [INFO] Refreshing screenshot...
[[20:48:59]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[20:48:59]] [SUCCESS] Screenshot refreshed successfully
[[20:48:59]] [SUCCESS] Screenshot refreshed successfully
[[20:48:58]] [SUCCESS] Screenshot refreshed
[[20:48:58]] [INFO] Refreshing screenshot...
[[20:48:56]] [SUCCESS] Screenshot refreshed successfully
[[20:48:56]] [SUCCESS] Screenshot refreshed successfully
[[20:48:56]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[20:48:55]] [SUCCESS] Screenshot refreshed
[[20:48:55]] [INFO] Refreshing screenshot...
[[20:48:54]] [SUCCESS] Screenshot refreshed successfully
[[20:48:54]] [SUCCESS] Screenshot refreshed successfully
[[20:48:53]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[20:48:53]] [SUCCESS] Screenshot refreshed
[[20:48:53]] [INFO] Refreshing screenshot...
[[20:48:50]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[20:48:50]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[20:48:50]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[20:48:50]] [INFO] Clearing screenshots from database before execution...
[[20:48:50]] [SUCCESS] All screenshots deleted successfully
[[20:48:50]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[20:48:50]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_204850/screenshots
[[20:48:50]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250615_204850
[[20:48:50]] [SUCCESS] Report directory initialized successfully
[[20:48:50]] [INFO] Initializing report directory and screenshots folder...
[[20:48:45]] [SUCCESS] All screenshots deleted successfully
[[20:48:45]] [INFO] All actions cleared
[[20:48:45]] [INFO] Cleaning up screenshots...
[[20:48:41]] [SUCCESS] Screenshot refreshed successfully
[[20:48:40]] [SUCCESS] Screenshot refreshed
[[20:48:40]] [INFO] Refreshing screenshot...
[[20:48:39]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[20:48:39]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[20:48:33]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[20:48:30]] [SUCCESS] Found 1 device(s)
[[20:48:29]] [INFO] Refreshing device list...
