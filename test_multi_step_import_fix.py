#!/usr/bin/env python3
"""
Test script to verify that the Multi Steps action import fix is working correctly.
This script tests the import functionality without requiring a device connection.
"""

import sys
import os
import logging

# Add app directory to path (same as run.py)
app_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app')
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_multi_step_action_imports():
    """Test that MultiStepAction can be imported and instantiated without errors"""
    print("Testing Multi Step Action imports...")
    
    try:
        # Test importing the MultiStepAction class
        from actions.multi_step_action import MultiStepAction
        print("✅ Successfully imported MultiStepAction")
        
        # Test creating an instance (with None controller for testing)
        multi_step_action = MultiStepAction(controller=None)
        print("✅ Successfully created MultiStepAction instance")
        
        # Test that the action has the expected methods
        assert hasattr(multi_step_action, 'execute'), "MultiStepAction should have execute method"
        print("✅ MultiStepAction has execute method")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_action_factory_imports():
    """Test that ActionFactory can be imported and can discover MultiStepAction"""
    print("\nTesting Action Factory imports...")
    
    try:
        # Test importing the ActionFactory class
        from actions.action_factory import ActionFactory
        print("✅ Successfully imported ActionFactory")
        
        # Test creating an instance
        action_factory = ActionFactory(controller=None)
        print("✅ Successfully created ActionFactory instance")
        
        # Test that it can discover action handlers
        action_factory._discover_action_handlers()
        print("✅ Successfully discovered action handlers")
        
        # Check if multiStep action is registered
        available_actions = list(action_factory.action_handlers.keys())
        print(f"📋 Available actions: {sorted(available_actions)}")
        
        # Look for multiStep or related actions
        multi_step_actions = [action for action in available_actions if 'multi' in action.lower() or 'step' in action.lower()]
        if multi_step_actions:
            print(f"✅ Found multi-step related actions: {multi_step_actions}")
        else:
            print("⚠️  No multi-step actions found in registered handlers")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_test_case_manager_imports():
    """Test that TestCaseManager can be imported from MultiStepAction"""
    print("\nTesting TestCaseManager imports from MultiStepAction...")
    
    try:
        # Test the import fallback logic that was fixed
        try:
            from utils.test_case_manager import TestCaseManager
            print("✅ Successfully imported TestCaseManager using relative import")
            import_method = "relative import (utils.test_case_manager)"
        except ImportError:
            try:
                from app.utils.test_case_manager import TestCaseManager
                print("✅ Successfully imported TestCaseManager using absolute import")
                import_method = "absolute import (app.utils.test_case_manager)"
            except ImportError:
                print("❌ Failed to import TestCaseManager using any method")
                return False
        
        # Test creating an instance
        test_case_manager = TestCaseManager(test_cases_dir="/tmp")
        print(f"✅ Successfully created TestCaseManager instance using {import_method}")
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Multi Steps Action Import Fix")
    print("=" * 50)
    
    tests = [
        test_multi_step_action_imports,
        test_action_factory_imports,
        test_test_case_manager_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Multi Steps action import fix is working correctly.")
        return 0
    else:
        print("💥 Some tests failed. The import fix may need additional work.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
